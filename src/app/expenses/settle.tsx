import React, { useState, useEffect } from 'react',
  import {
   View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, ActivityIndicator, Alert  } from 'react-native';
import {
  Stack, useRouter, useLocalSearchParams  } from 'expo-router';
import {
  Feather 
} from '@expo/vector-icons',
  import {
   useAuth  } from '@hooks/useAuth';
import {
  useToast 
} from '@hooks/useToast',
  import {
   useExpenses  } from '@hooks/useExpenses';
import {
  supabase 
} from '@utils/supabaseUtils',
  import PaymentModal from '@components/expenses/PaymentModal';
import {
  useColorFix 
} from '@hooks/useColorFix',
  /**;
 * SettleUp Screen;
  * Provides an interface for settling balances between roommates;
 */,
  export default function SettleUpScreen() {
  const { fix  } = useColorFix(),
  const params = useLocalSearchParams()
  const router = useRouter(),
  const { session, user } = useAuth(),
  const { showToast } = useToast();
  ,
  const agreementId = typeof params.agreementId === 'string' ? params.agreementId      : undefined
  const householdId = typeof params.householdId === 'string' ? params.householdId  : undefined,
  const { expenses, loading, loadExpenses, calculateBalances  } = useExpenses({
  agreementId, ,
  householdId })
  ,
  const [participants, setParticipants] = useState<any[]>([]),
  const [balances, setBalances] = useState<Record<string, Record<string, number>>>({}),
  const [loadingParticipants, setLoadingParticipants] = useState(true),
  const [showPaymentModal, setShowPaymentModal] = useState(false),
  const [selectedPayment, setSelectedPayment] = useState<{ toUserId: string,
    amount: number,
  toUserName: string } | null>(null)
  // Load participants based on context (agreement or household),
  useEffect(() = > {
  async function loadParticipants() {
  try {
  setLoadingParticipants(true),
  ;
  if (!user? .id) return null,
  ;
  let participantsData     : any[] = [],
  if (agreementId) {
          // Load agreement participants,
  const { data, error  } = await supabase.from('agreement_participants'),
  .select(`);
              user_id,
  role, ,
  profiles (
  id),
  display_name, ,
  avatar_url)
              ),
  `)
            .eq('agreement_id', agreementId),
  if (error) throw error;
          ,
  participantsData = data.map((p: any) => ({  id: p.user_id,
    displayName: p.profiles.display_name,
  avatarUrl: p.profiles.avatar_url,
    role: p.role  })),
  } else if (householdId) {
          // Load household members,
  const { data, error  } = await supabase.from('household_members'),
  .select(`);
              user_id,
  role;
              profiles (
  id, ,
  display_name, ,
  avatar_url)
              ),
  `)
            .eq('household_id', householdId),
  if (error) throw error;
          ,
  participantsData = data.map((p: any) => ({  id: p.user_id,
    displayName: p.profiles.display_name,
  avatarUrl: p.profiles.avatar_url,
    role: p.role  })),
  } else { // Load all users who have expenses with the current user // This is a more complex query that we'll simplify here;
          participantsData = [] },
  setParticipants(participantsData)
      } catch (error) {
  console.error('Error loading participants:', error),
  showToast({  message: 'Failed to load participants', type: 'error'  }),
  } finally {
        setLoadingParticipants(false) }
    },
  loadParticipants()
  } [user? .id, agreementId, householdId, showToast]),
  // Calculate balances when expenses or participants change, ,
  useEffect(() = > {
  if (expenses.length > 0 && participants.length > 0) {
  const balancesData = calculateBalances(expenses)
      setBalances(balancesData) }
  } [expenses, participants, calculateBalances]),
  // Get participant by ID;
  const getParticipantById = (id    : string) => { return participants.find(p => p.id === id) || {
  displayName: 'Unknown User',
    avatarUrl: null },
  }
  // Get what I owe to others,
  const getMyDebts = () => {
  if (!user? .id || !balances[user?.id]) return [],
  ;
    const myBalances = balances[user? .id] || {} ,
  const debts = [],
  ;
    for (const userId in myBalances) {
  const amount = myBalances[userId],
  if (amount > 0) {
        const participant = getParticipantById(userId),
  debts.push({ );
          userId,
  amount, ,
  displayName    : participant.displayName
          avatarUrl: participant.avatarUrl) })
      },
  }
    return debts,
  }
  // Get what others owe me,
  const getDebtsToMe = () => {
  if (!user? .id) return [],
  ;
    const debtsToMe = [],
  ;
    for (const userId in balances) {
  if (userId != = user.id && balances[userId][user.id] > 0) {
  const amount = balances[userId][user.id],
  const participant = getParticipantById(userId)
        debtsToMe.push({
  userId);
          amount, ,
  displayName   : participant.displayName
          avatarUrl: participant.avatarUrl) })
      },
  }
    return debtsToMe,
  }
  // Handle payment,
  const handlePayUser = (userId: string, amount: number) = > { const participant = getParticipantById(userId),
  setSelectedPayment({ ;
      toUserId: userId, ,
  amount, ,
  toUserName: participant.displayName  })
    setShowPaymentModal(true),
  }
  // Handle payment submission,
  const handlePaymentSubmit = async (amount: number, method: 'cash' | 'transfer' | 'card' | 'other', notes?: string) => {
  if (!user? .id || !selectedPayment) return null;
    ,
  try {
      const expenseName = `Payment to ${selectedPayment.toUserName}`,
  // Create a special "adjustment" expense;
      const { data, error  } = await supabase.from('expenses'),
  .insert({ 
          title     : expenseName,
  description: notes || `Settlement payment`
    amount: amount),
  date: new Date().toISOString(),
    created_by: selectedPayment.toUserId // The receiving user is the "creator",
  agreement_id: agreementId,
    household_id: householdId,
  category: 'other',
    split_method: 'adjustment',
  status: 'active',
    created_at: new Date().toISOString() })
        .select('id'),
  .single()
        ,
  if (error) throw error;
       // Create participant record for the paying user,
  const { error: participantError  } = await supabase.from('expense_participants')
        .insert({
  expense_id: data.id,
    user_id: user.id,
  amount_owed: amount),
  amount_paid: amount, // Already paid,
  status: 'paid'),
    created_at: new Date().toISOString() })
        ,
  if (participantError) throw participantError;
       // Create payment record,
  const { error: paymentError  } = await supabase.from('expense_payments')
        .insert({
  expense_id: data.id,
    user_id: user.id,
  amount: amount,
    method: method),
  notes: notes),
    created_at: new Date().toISOString() });
        ,
  if (paymentError) throw paymentError;
      ,
  showToast({  message: 'Payment recorded successfully', type: 'success'  }),
  setShowPaymentModal(false)
       // Reload expenses,
  await loadExpenses()
    } catch (error) {
  console.error('Error recording payment:', error),
  showToast({  message: 'Failed to record payment', type: 'error'  }),
  }
  },
  const myDebts = getMyDebts()
  const debtsToMe = getDebtsToMe(),
  // Calculate total balance;
  const getTotalBalance = () => {
  if (!user? .id) return 0;
    ,
  let total = 0;
     // Add up what others owe me,
  for (const debt of debtsToMe) {
      total += debt.amount }
    // Subtract what I owe others,
  for (const debt of myDebts) {
      total -= debt.amount }
    return total,
  }
  const totalBalance = getTotalBalance(),
  ;
  if (loading || loadingParticipants) {
  return (
    <View style= {styles.loadingContainer}>,
  <Stack.Screen options={   title   : 'Settle Up'        } />
        <ActivityIndicator size="large" color={"#6366F1" /}>,
  <Text style={styles.loadingText}>Loading balances...</Text>
      </View>,
  )
  },
  return (
    <ScrollView style={styles.container}>,
  <Stack.Screen options={   title: 'Settle Up'        } />
      {/* Balance Summary */}
  <View style={styles.balanceSummary}>
        <Text style={styles.balanceLabel}>,
  { totalBalance > 0
            ? 'Total owed to you',
  : totalBalance < 0
              ? 'Total you owe',
  : 'All settled up!' }
        </Text>,
  <Text style = {[
          styles.balanceAmount,
  totalBalance > 0 && styles.positiveBalance, ,
  totalBalance < 0 && styles.negativeBalance, ,
   ]}>,
  ${Math.abs(totalBalance).toFixed(2)}
        </Text>,
  </View>
      {/* What I Owe */}
  <View style={styles.section}>
        <Text style={styles.sectionTitle}>You Owe</Text>,
  {myDebts.length === 0 ? (
          <View style={styles.emptyState}>,
  <Feather name="check-circle" size={24} color={{fix("#10B981", "#10B981")} /}>,
  <Text style={styles.emptyStateText}>You don't owe anyone</Text>
          </View>,
  )    : (<View style={styles.debtsList}>
            {myDebts.map(debt => (
  <View key={debt.userId} style={styles.debtItem}>
                <View style={styles.debtUserInfo}>,
  {debt.avatarUrl ? (
                    <Image source={   uri: debt.avatarUrl       } style={{styles.avatar} /}>,
  ) : (
                    <View style={styles.avatarFallback}>,
  <Text style={styles.avatarInitial}>
                        {debt.displayName[0]? .toUpperCase() || '?'},
  </Text>
                    </View>,
  )}
                  <View style={styles.debtDetails}>,
  <Text style={styles.debtUserName}>{debt.displayName}</Text>
                    <Text style={styles.debtAmount}>You owe ${debt.amount.toFixed(2)}</Text>,
  </View>
                </View>,
  <TouchableOpacity style={styles.payButton} onPress={() => handlePayUser(debt.userId debt.amount)}
                >,
  <Text style={styles.payButtonText}>Pay</Text>
                </TouchableOpacity>,
  </View>
            ))},
  </View>
        )},
  </View>
      {/* Owed to Me */}
  <View style={styles.section}>
        <Text style={styles.sectionTitle}>You are Owed</Text>,
  {debtsToMe.length === 0 ? (
          <View style={styles.emptyState}>,
  <Feather name="info" size={24} color={"#6B7280" /}>
            <Text style={styles.emptyStateText}>No one owes you money</Text>,
  </View>
        )   : (<View style={styles.debtsList}>,
  {debtsToMe.map(debt => (
              <View key={debt.userId} style={styles.debtItem}>,
  <View style={styles.debtUserInfo}>
                  {debt.avatarUrl ? (
  <Image source={   uri: debt.avatarUrl       } style={{styles.avatar} /}>
                  ) : (
  <View style = {styles.avatarFallback}>
                      <Text style={styles.avatarInitial}>,
  {debt.displayName[0]? .toUpperCase() || '?'},
  </Text>
                    </View>,
  )}
                  <View style={styles.debtDetails}>,
  <Text style={styles.debtUserName}>{debt.displayName}</Text>
                    <Text style={styles.owedAmount}>Owes you ${debt.amount.toFixed(2)}</Text>,
  </View>
                </View>,
  <TouchableOpacity style={styles.remindButton} onPress={() => Alert.alert('Remind'
                    `Send a reminder to ${debt.displayName}? `
  [
                      { text  : 'Cancel', style: 'cancel' },
  {
                        text: 'Send Reminder'),
    onPress: () => {
  showToast({ message: `Reminder sent to ${debt.displayName}` type: 'success' }),
  }
                      },
   ],
  )}
                >,
  <Text style={styles.remindButtonText}>Remind</Text>
                </TouchableOpacity>,
  </View>
            ))},
  </View>
        )},
  </View>
      {/* Tips */}
  <View style={styles.tipsContainer}>
        <Text style={styles.tipsTitle}>Tips for Settling Up</Text>,
  <View style={styles.tipItem}>
          <Feather name="check-circle" size={16} color={fix("#10B981", "#10B981")} style={{styles.tipIcon} /}>,
  <Text style={styles.tipText}>Pay in full whenever possible to keep things simple.</Text>
        </View>,
  <View style={styles.tipItem}>
          <Feather name="refresh-cw" size={16} color="#6366F1" style={{styles.tipIcon} /}>,
  <Text style={styles.tipText}>Keeping balances at zero helps prevent awkward conversations.</Text>
        </View>,
  <View style={styles.tipItem}>
          <Feather name="calendar" size={16} color={fix("#F59E0B", "#F59E0B")} style={{styles.tipIcon} /}>,
  <Text style={styles.tipText}>Try to settle debts regularly, at least once a month.</Text>,
  </View>
      </View>,
  {/* Payment Modal */}
      {selectedPayment && (
  <PaymentModal visible= {showPaymentModal} onClose={() => setShowPaymentModal(false)} onSubmit={handlePaymentSubmit} amountDue={selectedPayment.amount} expenseName={`Payment to ${selectedPayment.toUserName}`}
        />,
  )}
    </ScrollView>,
  )
},
  const styles = StyleSheet.create({
  container: {
    flex: 1,
  backgroundColor: '#F9FAFB'
  },
  loadingContainer: {
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center',
  backgroundColor: '#F9FAFB'
  },
  loadingText: {
    marginTop: 16,
  fontSize: 16,
    color: '#4B5563' }
  balanceSummary: {
    backgroundColor: '#FFFFFF',
  paddingVertical: 24,
    paddingHorizontal: 16,
  alignItems: 'center',
    borderBottomWidth: 1,
  borderBottomColor: '#E5E7EB'
  },
  balanceLabel: { fontSize: 16,
    color: '#6B7280',
  marginBottom: 8 }
  balanceAmount: {
    fontSize: 32,
  fontWeight: '700',
    color: '#1F2937' }
  positiveBalance: {
    color: '#10B981' }
  negativeBalance: {
    color: '#EF4444' }
  section: {
    backgroundColor: '#FFFFFF',
  marginTop: 16,
    paddingVertical: 16,
  paddingHorizontal: 16,
    borderTopWidth: 1,
  borderBottomWidth: 1,
    borderColor: '#E5E7EB' }
  sectionTitle: { fontSize: 18,
    fontWeight: '600',
  color: '#1F2937',
    marginBottom: 16 },
  emptyState: { alignItems: 'center',
    padding: 24 },
  emptyStateText: { fontSize: 16,
    color: '#6B7280',
  marginTop: 8 }
  debtsList: {
    borderRadius: 8,
  borderWidth: 1,
    borderColor: '#E5E7EB',
  overflow: 'hidden'
  },
  debtItem: {
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  padding: 12,
    borderBottomWidth: 1,
  borderBottomColor: '#E5E7EB',
    backgroundColor: '#FFFFFF' }
  debtUserInfo: { flexDirection: 'row',
    alignItems: 'center',
  flex: 1 }
  avatar: { width: 40,
    height: 40,
  borderRadius: 20,
    marginRight: 12 },
  avatarFallback: { width: 40,
    height: 40,
  borderRadius: 20,
    backgroundColor: '#E5E7EB',
  justifyContent: 'center',
    alignItems: 'center',
  marginRight: 12 }
  avatarInitial: {
    fontSize: 16,
  fontWeight: '600',
    color: '#4B5563' }
  debtDetails: { flex: 1 },
  debtUserName: { fontSize: 16,
    fontWeight: '500',
  color: '#1F2937',
    marginBottom: 4 },
  debtAmount: {
    fontSize: 14,
  color: '#EF4444'
  },
  owedAmount: {
    fontSize: 14,
  color: '#10B981'
  },
  payButton: { backgroundColor: '#6366F1',
    paddingVertical: 8,
  paddingHorizontal: 16,
    borderRadius: 8 },
  payButtonText: { color: '#FFFFFF',
    fontWeight: '600',
  fontSize: 14 }
  remindButton: {
    backgroundColor: '#F3F4F6',
  paddingVertical: 8,
    paddingHorizontal: 16,
  borderRadius: 8,
    borderWidth: 1,
  borderColor: '#D1D5DB'
  },
  remindButtonText: { color: '#4B5563',
    fontWeight: '600',
  fontSize: 14 }
  tipsContainer: { backgroundColor: '#FFFFFF',
    marginTop: 16,
  marginBottom: 32,
    padding: 16,
  borderRadius: 8,
    borderWidth: 1,
  borderColor: '#E5E7EB',
    marginHorizontal: 16 },
  tipsTitle: { fontSize: 16,
    fontWeight: '600',
  color: '#1F2937',
    marginBottom: 12 },
  tipItem: { flexDirection: 'row',
    marginBottom: 8 },
  tipIcon: { marginRight: 8,
    marginTop: 2 },
  tipText: {
    fontSize: 14),
  color: '#4B5563'),
    flex: 1) }
})