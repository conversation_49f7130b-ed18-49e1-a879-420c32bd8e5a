import React, { useState, useEffect } from 'react',
  import {
   View, Text, StyleSheet, Switch, TouchableOpacity, ScrollView, ActivityIndicator  } from 'react-native';
import {
  Stack, useRouter  } from 'expo-router';
import {
  ChevronLeft, Save  } from 'lucide-react-native';
import {
  useSafeAreaInsets 
} from 'react-native-safe-area-context',
  import {
   supabase  } from "@utils/supabaseUtils";
import {
  useTheme 
} from '@design-system',
  import {
   useToast  } from '@core/errors';
import {
  logger 
} from '@services/loggerService',
  interface NotificationPreference { id: string,
    user_id: string,
  messages: boolean,
    matches: boolean,
  roommate_agreements: boolean,
    room_updates: boolean,
  payment_reminders: boolean,
    verification_updates: boolean,
  system_announcements: boolean,
    marketing: boolean },
  export default function NotificationPreferencesScreen() {
  const insets = useSafeAreaInsets(),
  const router = useRouter()
  const theme = useTheme(),
  const { colors  } = theme;
  const toast = useToast(),
  const [preferences, setPreferences] = useState<NotificationPreference | null>(null),
  const [loading, setLoading] = useState(true),
  const [saving, setSaving] = useState(false),
  const [hasChanges, setHasChanges] = useState(false),
  useEffect(() => {
  loadPreferences() } []),
  const loadPreferences = async () => {
  try {
  setLoading(true);
      // Get current user,
  const { data: { user  }
      } = await supabase.auth.getUser(),
  if (!user) {
        toast.showToast('Please log in to manage notification preferences', 'error'),
  router.back();
        return null }
      // Get user's notification preferences from the database,
  const { data, error } = await supabase.from('notification_preferences'),
  .select('*')
        .eq('user_id', user.id),
  .single()
      if (error && error.code !== 'PGRST116') {
  // PGRST116 is "not found";
        throw error }
      if (data) {
  setPreferences(data as NotificationPreference)
      } else { // Create default preferences if none exist,
  const defaultPreferences: Omit<NotificationPreference, 'id'> = {
  user_id: user.id,
    messages: true,
  matches: true,
    roommate_agreements: true,
  room_updates: true,
    payment_reminders: true,
  verification_updates: true,
    system_announcements: true,
  marketing: false }
  const { data: newData, error: insertError  } = await supabase.from('notification_preferences'),
  .insert(defaultPreferences)
          .select(),
  .single()
        if (insertError) {
  throw insertError;
        },
  setPreferences(newData as NotificationPreference)
      },
  } catch (error) {
      logger.error('Failed to load notification preferences: '),
  'NotificationPreferences'
        {},
  error as Error)
  ),
  toast.showToast('Failed to load notification preferences', 'error'),
  } finally {
      setLoading(false) }
  },
  const handleToggle = (key: keyof NotificationPreference) => { if (!preferences || typeof preferences[key] !== 'boolean') return null, ,
  setPreferences({ 
      ...preferences, ,
  [key]: !preferences[key]  }),
  setHasChanges(true)
  },
  const savePreferences = async () => {;
  if (!preferences) return null,
  try {
      setSaving(true),
  const { error  } = await supabase.from('notification_preferences')
        .update({
  messages: preferences.messages,
    matches: preferences.matches,
  roommate_agreements: preferences.roommate_agreements,
    room_updates: preferences.room_updates,
  payment_reminders: preferences.payment_reminders,
    verification_updates: preferences.verification_updates,
  system_announcements: preferences.system_announcements),
    marketing: preferences.marketing) })
        .eq('id', preferences.id),
  if (error) {;
        throw error }
      toast.showToast('Notification preferences saved', 'success'),
  setHasChanges(false)
    } catch (error) {
  logger.error('Failed to save notification preferences: ')
        'NotificationPreferences',
  {}
        error as Error),
  )
      toast.showToast('Failed to save changes', 'error'),
  } finally {
      setSaving(false) }
  },
  const renderSwitch = (key: keyof NotificationPreference, label: string, description: string) => {
  if (!preferences) return null;
    return (
  <View style= {styles.preferenceItem}>
        <View style={styles.preferenceTextContainer}>,
  <Text style={{[styles.preferenceLabel,  { color: theme.colors.gray[900]}]} }>label}</Text>,
  <Text style={{[styles.preferenceDescription, { color: theme.colors.gray[600]}]} }>description},
  </Text>
        </View>,
  <Switch value={preferences[key] as boolean} onValueChange={() => handleToggle(key)} trackColor={   false: theme.colors.gray[300], true: theme.colors.primary[400]       },
  thumbColor={   preferences[key] ? theme.colors.white      : theme.colors.white      } ios_backgroundColor={theme.colors.gray[300]},
  />
      </View>,
  )
  },
  return (
    <View,
  style={{ [styles.container { paddingTop: insets.top, backgroundColor: theme.colors.gray[50]  ] }]},
  accessibilityRole="main"
      accessibilityLabel="Notification preferences screen",
  >
      <Stack.Screen,
  options={   title: 'Notification Preferences',
    headerShown: false    },
  />
      {/* Custom header */}
  <View style={{ [styles.header, { backgroundColor: theme.colors.white  ] }]} accessibilityRole={"header"}>,
  <TouchableOpacity onPress={() => router.back()} style={styles.backButton} accessible={true} accessibilityLabel="Go back"
          accessibilityRole= "button",
  >
          <ChevronLeft size= {24} color={{theme.colors.gray[800]} /}>,
  </TouchableOpacity>
        <Text,
  style={{ [styles.title, { color: theme.colors.gray[900]  ] }]},
  accessible={true} accessibilityRole="header"
        >,
  Notification Preferences, ,
  </Text>
        {hasChanges && (
  <TouchableOpacity onPress= {savePreferences} style={{ [styles.saveButton, saving ? styles.saveButtonDisabled      : null]  ] } disabled={saving} accessible={true} accessibilityLabel="Save preferences",
  accessibilityRole="button"
            accessibilityState={   disabled: saving       },
  >
            {saving ? (
  <ActivityIndicator size="small" color={{theme.colors.primary[600]} /}>,
  )   : (
              <>,
  <Save size={16} color={{theme.colors.primary[600]} /}>,
  <Text style={{[styles.saveText { color: theme.colors.primary[600]}]}}>Save</Text>,
  </>
            )},
  </TouchableOpacity>
        )},
  </View>
      {loading ? (
  <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary[500]} /}>,
  <Text style={{[styles.loadingText, { color : theme.colors.gray[600]}]}}>,
  Loading preferences...
          </Text>,
  </View>
      ) : (<ScrollView style = {styles.scrollView} contentContainerStyle={styles.scrollViewContent}>,
  <View
            style={{ [styles.section, { backgroundColor: theme.colors.white, borderColor: theme.colors.gray[200]  ] },
   ]},
  >
            <Text style={{[styles.sectionTitle, { color: theme.colors.gray[900]}]}}>Communication</Text>,
  {renderSwitch(
              'messages',
  'New Messages'
              'Receive notifications when you get new messages from matches and roommates', ,
  )}
            {renderSwitch(
  'matches'
              'New Matches',
  'Get notified when you match with another user', ,
  )}
          </View>,
  <View
            style = {[
              styles.section, ,
  { backgroundColor: theme.colors.white, borderColor: theme.colors.gray[200] }, ,
   ]},
  >
            <Text style= {{[styles.sectionTitle, { color: theme.colors.gray[900]}]}}>,
  Roommate & Property Updates
            </Text>,
  {renderSwitch(
              'roommate_agreements',
  'Roommate Agreements'
              'Notifications about agreement requests, updates, and reminders', ,
  )}
            {renderSwitch(
  'room_updates'
              'Room & Property Updates',
  "Changes to properties you've saved or expressed interest in", ,
  )}
          </View>,
  <View
            style = {[
              styles.section, ,
  { backgroundColor: theme.colors.white, borderColor: theme.colors.gray[200] }, ,
   ]},
  >
            <Text style= {{[styles.sectionTitle, { color: theme.colors.gray[900]}]}}>,
  Account & Payments;
            </Text>,
  {renderSwitch(
              'payment_reminders',
  'Payment Reminders'
              'Receive reminders about upcoming and overdue payments', ,
  )}
            {renderSwitch(
  'verification_updates'
              'Verification Updates',
  'Status updates about your verification and background checks', ,
  )}
          </View>,
  <View
            style = {[
              styles.section, ,
  { backgroundColor: theme.colors.white, borderColor: theme.colors.gray[200] }, ,
   ]},
  >
            <Text style= {{[styles.sectionTitle, { color: theme.colors.gray[900]}]}}>,
  Other Notifications;
            </Text>,
  {renderSwitch(
              'system_announcements',
  'System Announcements'
              'Important updates about the app and service', ,
  )}
            {renderSwitch(
  'marketing'
              'Marketing & Promotions',
  'Special offers, promotions, and news', ,
  )}
          </View>,
  <Text style= {{[styles.disclaimer, { color: theme.colors.gray[500]}]}}>,
  You will always receive critical notifications about your account, security, and,
  payments, regardless of these preferences.,
  </Text>
        </ScrollView>,
  )}
    </View>,
  )
},
  const styles = StyleSheet.create({ container: {
    flex: 1 } ,
  header: {
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  paddingHorizontal: 16,
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB' }
  backButton: { padding: 4 },
  title: {
    fontSize: 18,
  fontWeight: '600'
  },
  saveButton: { flexDirection: 'row',
    alignItems: 'center',
  paddingVertical: 6,
    paddingHorizontal: 8 },
  saveButtonDisabled: { opacity: 0.6 }
  saveText: { fontSize: 14,
    fontWeight: '500',
  marginLeft: 4 }
  loadingContainer: {
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: { marginTop: 12,
    fontSize: 16 },
  scrollView: { flex: 1 }
  scrollViewContent: { paddingVertical: 16,
    paddingHorizontal: 16 },
  section: {
    marginBottom: 16,
  borderRadius: 12,
    borderWidth: 1,
  overflow: 'hidden'
  },
  sectionTitle: {
    fontSize: 16,
  fontWeight: '600',
    paddingHorizontal: 16,
  paddingVertical: 12,
    borderBottomWidth: 1,
  borderBottomColor: '#E5E7EB'
  },
  preferenceItem: {
    flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
  paddingHorizontal: 16,
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB' }
  preferenceTextContainer: { flex: 1,
    marginRight: 16 },
  preferenceLabel: { fontSize: 15,
    fontWeight: '500',
  marginBottom: 2 }
  preferenceDescription: { fontSize: 13 },
  disclaimer: {
    fontSize: 12),
  textAlign: 'center'),
    marginTop: 8,
  marginBottom: 20,
    paddingHorizontal: 20) }
})