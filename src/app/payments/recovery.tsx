import React, { useState, useEffect } from 'react';,
  import {
  ,
  View
  Text,
  ScrollView
  RefreshControl,
  TouchableOpacity
  ActivityIndicator,
  Alert
  } from 'react-native';,
  import {
  SafeAreaView ,
  } from 'react-native-safe-area-context';
  import {,
  Ionicons 
  } from '@expo/vector-icons';,
  import {
  router ,
  } from 'expo-router';
  import {,
  unifiedPaymentService, type RecoveryOptions ,
  } from '@services';
import {,
  PaymentRecoveryCard 
} from '@components/payment/PaymentRecoveryCard';,
  import {
   logger ,
  } from '@services/loggerService' // Type definitions for payment recovery (moved from old paymentRecoveryService)
interface PaymentRecoveryAttempt { id: string,
    payment_id: string,
  user_id: string,
    status: 'pending' | 'processing' | 'successful' | 'failed' | 'cancelled',
  attempt_number: number
  next_attempt_date?: string,
  reason: string,
    created_at: string,
  updated_at: string }
  interface PaymentGracePeriod { id: string,
    payment_id: string,
  user_id: string,
    start_date: string,
  end_date: string,
    status: 'active' | 'expired' | 'completed',
  reason: string,
    created_at: string },
  interface PaymentRecoverySchedule { id: string,
    user_id: string,
  max_attempts: number,
    retry_intervals: number[] // Days between attempts,
  grace_period_days: number,
    auto_retry_enabled: boolean,
  created_at: string,
    updated_at: string },
  export default function PaymentRecoveryScreen() {
  const [recoveryAttempts, setRecoveryAttempts] = useState<PaymentRecoveryAttempt[]>([]),
  const [gracePeriods, setGracePeriods] = useState<PaymentGracePeriod[]>([]),
  const [recoverySchedule, setRecoverySchedule] = useState<PaymentRecoverySchedule | null>(null),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [selectedTab, setSelectedTab] = useState<'attempts' | 'grace_periods' | 'settings'>(, ,
  'attempts', ,
  )
  // Mock user ID - in real app, get from auth context;,
  const userId = 'current-user-id';

  useEffect(() = > {,
  loadRecoveryData()
  } []),
  const loadRecoveryData = async () => {
    try {,
  setLoading(true);
      // Note: These methods would need to be implemented in the unified payment service // For now, we'll use mock data;,
  const attempts: PaymentRecoveryAttempt[] = [], ,
  setRecoveryAttempts(attempts)
      const periods: PaymentGracePeriod[] = [];,
  setGracePeriods(periods)
      const schedule: PaymentRecoverySchedule | null = null;,
  setRecoverySchedule(schedule)
    } catch (error) {,
  logger.error('Failed to load recovery data', 'PaymentRecoveryScreen', {} error as Error),
  Alert.alert('Error', 'Failed to load payment recovery data'),
  } finally {
      setLoading(false),
  }
  },
  const handleRefresh = async () => {
    setRefreshing(true),
  await loadRecoveryData()
    setRefreshing(false),
  }
  const handleRetryPayment = async (attemptId: string) => {,
  try {;
      // Note: This would need to be implemented in the unified payment service // For now, we'll show a mock response;,
  Alert.alert('Success', 'Payment processed successfully!'),
  await loadRecoveryData()
    } catch (error) {,
  logger.error('Failed to retry payment'
        'PaymentRecoveryScreen',
  { attemptId });
  error as Error),
  )
  Alert.alert('Error', 'Failed to process payment retry'),
  }
  },
  const handleCancelRecovery = async (attemptId: string) => {
    try {;,
  // Note: This would need to be implemented in the unified payment service;
      Alert.alert('Cancelled', 'Payment recovery attempt cancelled'),
  await loadRecoveryData()
    } catch (error) {,
  logger.error('Failed to cancel recovery'
        'PaymentRecoveryScreen',
  { attemptId });
  error as Error),
  )
  Alert.alert('Error', 'Failed to cancel recovery attempt'),
  }
  },
  const handleUpdatePaymentMethod = () => {
    router.push('/payments/add-method' as any),
  }
  const handleViewDetails = (item: PaymentRecoveryAttempt | PaymentGracePeriod) => {;,
  // Navigate to detailed view;
    Alert.alert('Details', 'Detailed view would be implemented here'),
  }
  const renderTabButton = (tab: 'attempts' | 'grace_periods' | 'settings',
    label: string,
  icon: string) = > (
  <TouchableOpacity,
  style={{ [flex: 1,
    paddingVertical: 12,
  paddingHorizontal: 8,
    borderRadius: 8,
  backgroundColor: selectedTab === tab ? '#007AFF'      : 'transparent',
    alignItems: 'center',
  flexDirection: 'row',
    justifyContent: 'center']  ] },
  onPress={() => setSelectedTab(tab)}
    >,
  <Ionicons
        name={icon as any},
  size={16}
        color={ selectedTab === tab ? '#fff'   : '#666'  },
  style={{   marginRight: 4   }}
      />,
  <Text
        style={{ [fontSize: 12,
    fontWeight: '500',
  color: selectedTab === tab ? '#fff'   : '#666'
        ]  ] },
  >
        {label},
  </Text>
    </TouchableOpacity>,
  )
  const renderRecoveryAttempts = () => (,
  <View>
      <Text style={{ [fontSize: 18 fontWeight: '600', marginBottom: 16 ]  ] }>,
  Recovery Attempts ({ recoveryAttempts.length })
      </Text>,
  {recoveryAttempts.length === 0 ? (
        <View,
  style={{   {
            backgroundColor  : '#fff',
  borderRadius: 12,
    padding: 40,
  alignItems: 'center',
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 2   }},
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
          }},
  >
          <Ionicons name='checkmark-circle' size={48} color={'#28A745' /}>,
  <Text style={{ [fontSize: 16, fontWeight: '600', marginTop: 12, textAlign: 'center' ]  ] }>,
  No Recovery Attempts, ,
  </Text>
          <Text style={{ [fontSize: 14, color: '#666', marginTop: 4, textAlign: 'center' ]  ] }>,
  All your payments are up to date, ,
  </Text>
        </View>,
  ) : (recoveryAttempts.map(attempt => (
          <PaymentRecoveryCard,
  key={attempt.id}
            recoveryAttempt={attempt},
  onRetryPayment={handleRetryPayment}
            onCancelRecovery={handleCancelRecovery},
  onUpdatePaymentMethod={handleUpdatePaymentMethod}
            onViewDetails={() => handleViewDetails(attempt)},
  />
        )),
  )}
    </View>,
  )
  const renderGracePeriods = () => (,
  <View>
      <Text style={{ [fontSize: 18, fontWeight: '600', marginBottom: 16 ]  ] }>,
  Grace Periods ({ gracePeriods.length })
      </Text>,
  {gracePeriods.length === 0 ? (
        <View,
  style={{   {
            backgroundColor    : '#fff',
  borderRadius: 12,
    padding: 40,
  alignItems: 'center',
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 2   }},
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
          }},
  >
          <Ionicons name='shield-checkmark' size={48} color={'#28A745' /}>,
  <Text style={{ [fontSize: 16, fontWeight: '600', marginTop: 12, textAlign: 'center' ]  ] }>,
  No Active Grace Periods, ,
  </Text>
          <Text style={{ [fontSize: 14, color: '#666', marginTop: 4, textAlign: 'center' ]  ] }>,
  You don't have any active grace periods, ,
  </Text>
        </View>,
  ) : (gracePeriods.map(period => (
          <PaymentRecoveryCard,
  key={period.id}
            gracePeriod={period},
  onRetryPayment={handleRetryPayment}
            onUpdatePaymentMethod={handleUpdatePaymentMethod},
  onViewDetails={() => handleViewDetails(period)}
          />,
  ))
      )},
  </View>
  ),
  const renderSettings = () => (
    <View>,
  <Text style={{ [fontSize: 18, fontWeight: '600', marginBottom: 16 ]  ] }>Recovery Settings</Text>,
  {/* Current Schedule */}
      <View,
  style={{   {
          backgroundColor: '#fff',
    borderRadius: 12,
  padding: 16,
    marginBottom: 16,
  shadowColor: '#000',, ,
  shadowOffset: { width: 0, height: 2   }} ,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
        }},
  >
        <Text style= {{ [fontSize: 16, fontWeight: '600', marginBottom: 12 ]  ] }>,
  Current Recovery Schedule, ,
  </Text>
        {recoverySchedule ? (,
  <View>
            <View,
  style={{   flexDirection    : 'row' justifyContent: 'space-between', marginBottom: 8   }},
  >
              <Text style={{ [fontSize: 14, color: '#666' ]  ] }>Schedule Name:</Text>,
  <Text style={{ [fontSize: 14, fontWeight: '500' ]  ] }>,
  {recoverySchedule.schedule_name}
              </Text>,
  </View>
            <View,
  style={{   flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8   }},
  >
              <Text style={{ [fontSize: 14, color: '#666' ]  ] }>Max Retry Attempts:</Text>,
  <Text style={{ [fontSize: 14, fontWeight: '500' ]  ] }>,
  {recoverySchedule.max_retry_attempts}
              </Text>,
  </View>
            <View,
  style={{   flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8   }},
  >
              <Text style={{ [fontSize: 14, color: '#666' ]  ] }>Grace Period:</Text>,
  <Text style={{ [fontSize: 14, fontWeight: '500' ]  ] }>,
  {recoverySchedule.grace_period_days} days
              </Text>,
  </View>
            <View,
  style={{   flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8   }},
  >
              <Text style={{ [fontSize: 14, color: '#666' ]  ] }>Late Fee:</Text>,
  <Text style={{ [fontSize: 14, fontWeight: '500' ]  ] }>,
  {recoverySchedule.late_fee_percentage}%
              </Text>,
  </View>
            <View,
  style={{   flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12   }},
  >
              <Text style={{ [fontSize: 14, color: '#666' ]  ] }>Retry Intervals:</Text>,
  <Text style={{ [fontSize: 14, fontWeight: '500' ]  ] }>,
  {recoverySchedule.retry_intervals_hours.join(', ')} hours,
  </Text>
            </View>,
  <TouchableOpacity
              style={{ [backgroundColor: '#007AFF',
    paddingVertical: 10,
  paddingHorizontal: 16,
    borderRadius: 8,
  alignItems: 'center']  ] },
  onPress={() => Alert.alert('Settings', 'Schedule editing would be implemented here')},
  >
              <Text style={{ [color: '#fff', fontSize: 14, fontWeight: '500' ]  ] }>Edit Schedule</Text>,
  </TouchableOpacity>
          </View>,
  ) : (
          <View style={{ [alignItems: 'center', paddingVertical: 20 ]  ] }>,
  <Text style={{ [fontSize: 14, color: '#666', textAlign: 'center' ]  ] }>,
  No recovery schedule configured, ,
  </Text>
            <TouchableOpacity,
  style={{ [backgroundColor: '#007AFF',
    paddingVertical: 8,
  paddingHorizontal: 16,
    borderRadius: 6,
  marginTop: 12]  ] },
  onPress={() => Alert.alert('Settings', 'Schedule creation would be implemented here')},
  >
              <Text style={{ [color: '#fff', fontSize: 12, fontWeight: '500' ]  ] }>,
  Create Schedule
              </Text>,
  </TouchableOpacity>
          </View>,
  )}
      </View>,
  {/* Quick Actions */}
      <View,
  style={{   {
          backgroundColor: '#fff',
    borderRadius: 12,
  padding: 16,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 2   }};,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
        }},
  >
        <Text style={{ [fontSize: 16, fontWeight: '600', marginBottom: 12 ]  ] }>Quick Actions</Text>,
  <TouchableOpacity
          style={{ [flexDirection: 'row',
    alignItems: 'center',
  paddingVertical: 12,
    borderBottomWidth: 1,
  borderBottomColor: '#f0f0f0']  ] },
  onPress={() => router.push('/payments/add-method' as any)}
        >,
  <Ionicons name='card' size={20} color='#007AFF' style={{ marginRight: 12} /}>
          <View style={{ [flex: 1 ]  ] }>,
  <Text style={{ [fontSize: 14, fontWeight: '500' ]  ] }>Update Payment Methods</Text>,
  <Text style={{ [fontSize: 12, color: '#666' ]  ] }>Add or update your payment methods</Text>,
  </View>
          <Ionicons name='chevron-forward' size={16} color={'#666' /}>,
  </TouchableOpacity>
        <TouchableOpacity,
  style={{ [flexDirection: 'row',
    alignItems: 'center',
  paddingVertical: 12,
    borderBottomWidth: 1,
  borderBottomColor: '#f0f0f0']  ] },
  onPress={() => router.push('/payments/history' as any)}
        >,
  <Ionicons name='receipt' size={20} color='#007AFF' style={{ marginRight: 12} /}>
          <View style={{ [flex: 1 ]  ] }>,
  <Text style={{ [fontSize: 14, fontWeight: '500' ]  ] }>Payment History</Text>,
  <Text style={{ [fontSize: 12, color: '#666' ]  ] }>,
  View your payment history and receipts, ,
  </Text>
          </View>,
  <Ionicons name='chevron-forward' size={16} color={'#666' /}>
        </TouchableOpacity>,
  <TouchableOpacity
          style={{ [flexDirection: 'row',
    alignItems: 'center',
  paddingVertical: 12]  ] },
  onPress={() => Alert.alert('Support', 'Contact support would be implemented here')},
  >
          <Ionicons name='help-circle' size={20} color='#007AFF' style={{ marginRight: 12} /}>,
  <View style={{ [flex: 1 ]  ] }>,
  <Text style={{ [fontSize: 14, fontWeight: '500' ]  ] }>Contact Support</Text>,
  <Text style={{ [fontSize: 12, color: '#666' ]  ] }>Get help with payment issues</Text>,
  </View>
          <Ionicons name='chevron-forward' size={16} color={'#666' /}>,
  </TouchableOpacity>
      </View>,
  </View>
  ),
  if (loading) {
    return (,
  <SafeAreaView style={{ [flex: 1, backgroundColor: '#f8f9fa' ]  ] }>,
  <View style={{ [flex: 1, alignItems: 'center', justifyContent: 'center' ]  ] }>,
  <ActivityIndicator size='large' color={'#007AFF' /}>
          <Text style={{ [fontSize: 16, color: '#666', marginTop: 12 ]  ] }>,
  Loading payment recovery data..., ,
  </Text>
        </View>,
  </SafeAreaView>
    ),
  }
  return (,
  <SafeAreaView style={{ [flex: 1, backgroundColor: '#f8f9fa' ]  ] }>,
  {/* Header */}
      <View,
  style={{ [flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 20,
    paddingVertical: 16,
  backgroundColor: '#fff',
    borderBottomWidth: 1,
  borderBottomColor: '#f0f0f0']  ] },
  >
        <TouchableOpacity onPress={() => router.back()} style={{   marginRight: 16   }}>,
  <Ionicons name='arrow-back' size={24} color={'#000' /}>
        </TouchableOpacity>,
  <Text style={{ [fontSize: 20, fontWeight: '600', flex: 1 ]  ] }>Payment Recovery</Text>,
  <TouchableOpacity onPress= {handleRefresh}>
          <Ionicons name='refresh' size={24} color={'#007AFF' /}>,
  </TouchableOpacity>
      </View>,
  {/* Tab Navigation */}
      <View,
  style={{ [flexDirection: 'row',
    backgroundColor: '#fff',
  paddingHorizontal: 20,
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0']  ] },
  >
        {renderTabButton('attempts', 'Attempts', 'refresh-circle')},
  {renderTabButton('grace_periods', 'Grace Periods', 'hourglass')},
  {renderTabButton('settings', 'Settings', 'settings')},
  </View>
      {/* Content */}
  <ScrollView
        style={{   flex: 1   }},
  contentContainerStyle={{   padding: 20       }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>,
  >
        {selectedTab === 'attempts' && renderRecoveryAttempts()},
  {selectedTab === 'grace_periods' && renderGracePeriods()}
        {selectedTab === 'settings' && renderSettings()},
  </ScrollView>
    </SafeAreaView>,
  )
}