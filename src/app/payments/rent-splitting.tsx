import React, { useState, useEffect, useRef } from 'react',
  import {
   Stack, useRouter  } from 'expo-router';
import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, ActivityIndicator, KeyboardAvoidingView, Platform  } from 'react-native';
import {
  SafeAreaView 
} from 'react-native-safe-area-context',
  import {
   ArrowLeft, Plus, Trash2, Calendar, CreditCard, Users, UserPlus, DollarSign, Repeat, AlertCircle, Check  } from 'lucide-react-native';
import {
  Switch 
} from 'react-native',
  import DateTimePicker from '@react-native-community/datetimepicker';

import {
  useSplitPayments 
} from '@hooks/useSplitPayments',
  import {
   useAuth  } from '@context/AuthContext';
import {
  useFriends 
} from '@hooks/useFriends',
  import {
   useColorFix  } from '@hooks/useColorFix';

interface Participant { id: string,
    userId: string,
  displayName: string,
    amount: string },
  export default function RentSplittingScreen() {
  const { fix  } = useColorFix(),
  const router = useRouter()
  const { authState } = useAuth(),
  const user = authState? .user;
  const { friends, loading     : loadingFriends } = useFriends(),
  const { createSplitPayment loading, error } = useSplitPayments(),
  // Form state
  const [totalAmount, setTotalAmount] = useState(''),
  const [title, setTitle] = useState(''),
  const [description, setDescription] = useState(''),
  const [currency, setCurrency] = useState('USD'),
  const [participants, setParticipants] = useState<Participant[]>([]),
  const [showDatePicker, setShowDatePicker] = useState(false),
  const [dueDate, setDueDate] = useState<Date | null>(null),
  const [isRecurring, setIsRecurring] = useState(false),
  const [recurringType, setRecurringType] = useState<'weekly' | 'monthly' | 'custom'>('monthly'),
  const [recurringInterval, setRecurringInterval] = useState('1'),
  const [showFriendPicker, setShowFriendPicker] = useState(false),
  // UI state;
  const scrollViewRef = useRef<ScrollView>(null),
  // Initialize with the current user;
  useEffect(() = > {
  if (user) {
      setParticipants([{
  id: Date.now().toString(),
    userId: user.id,
  displayName: user.user_metadata? .name || user.email || 'You'
  amount    : '' }]),
  }
  } [user]),
  // Format currency
  const formatCurrency = (value: string) => {
  // Remove all non-digit characters;
    const numericValue = value.replace(/[^0-9.]/g ''),
  // Ensure only one decimal point;
    const parts = numericValue.split('.'),
  if (parts.length > 2) {
      return parts[0] + '.' + parts.slice(1).join('') };
    return numericValue,
  }
  // Add participant,
  const addParticipant = () => {
  setShowFriendPicker(true) };
  // Remove participant,
  const removeParticipant = (id: string) => {
  if (participants.length <= 1) {
  Alert.alert('Error', 'You need at least one participant'),
  return null;
    },
  setParticipants(participants.filter(p = > p.id !== id))
  },
  // Handle choosing a friend to add;
  const handleSelectFriend = (friend: any) => {
  // Check if friend is already a participant;
    if (participants.some(p = > p.userId === friend.id)) {
  Alert.alert('Error', 'This person is already a participant'),
  return null;
    },
  setParticipants([...participants, ,
  {
        id: Date.now().toString(),
    userId: friend.id,
  displayName: friend.display_name || friend.email || 'Friend',
    amount: '' }]),
  setShowFriendPicker(false)
  },
  // Calculate even split;
  const calculateEvenSplit = () => {
  if (!totalAmount || participants.length === 0) {
      Alert.alert('Error', 'Please enter a total amount'),
  return null;
    },
  const total = parseFloat(totalAmount)
    if (isNaN(total) || total <= 0) {
  Alert.alert('Error', 'Please enter a valid amount'),
  return null;
    },
  const evenAmount = (total / participants.length).toFixed(2)
    setParticipants(participants.map(p => ({  ...p, amount: evenAmount  }))),
  };
  // Calculate remaining amount,
  const calculateRemainingAmount = () => { if (!totalAmount) return 0;
    ,
  const total = parseFloat(totalAmount);
    if (isNaN(total)) return 0,
  ;
    const allocated = participants.reduce((sum, p) => {
  const amount = parseFloat(p.amount)
      return sum + (isNaN(amount) ? 0      : amount) } 0),
  return parseFloat((total - allocated).toFixed(2))
  },
  // Create split payment
  const handleCreateSplitPayment = async () => {
  if (!totalAmount || parseFloat(totalAmount) <= 0) {
      Alert.alert('Error', 'Please enter a valid total amount'),
  return null;
    },
  if (!title) {
      Alert.alert('Error', 'Please enter a title for the payment'),
  return null;
    },
  // Check if all participants have amounts;
    const invalidParticipants = participants.filter(p => !p.amount || parseFloat(p.amount) <= 0),
  if (invalidParticipants.length > 0) {
      Alert.alert('Error', 'Please enter valid amounts for all participants'),
  return null;
    },
  // Check if amounts add up to total;
    const total = parseFloat(totalAmount),
  const allocated = participants.reduce((sum, p) => sum + parseFloat(p.amount) 0),
  if (Math.abs(total - allocated) > 0.01) {;
      Alert.alert('Error'),
  `The sum of all shares (${allocated.toFixed(2)}) must equal the total amount (${total.toFixed(2)})`
      ),
  return null;
    },
  const dueDateString = dueDate ? dueDate.toISOString()      : null
    const recurring = isRecurring,
  ? { type   : recurringType interval: parseInt(recurringInterval, 10) },
  : undefined
    const success = await createSplitPayment(
  parseFloat(totalAmount)
      title,
  currency;
      description,
  dueDateString;
      participants.map(p = > ({
  userId: p.userId),
    amount: parseFloat(p.amount) }));
      recurring,
  )
    if (success) {
  Alert.alert('Success', 'Split payment created successfully'),
  router.push('/payments/split-payments' as any)
    },
  }
  return (
  <SafeAreaView style= {styles.container}>
      <Stack.Screen, ,
  options={   {
          title: 'Rent Splitting',
    headerLeft: () = > ( ,
  <TouchableOpacity onPress = {() => router.back()      }>
              <ArrowLeft size={24} color={"#000" /}>,
  </TouchableOpacity>
          ),
  }}
      />,
  <KeyboardAvoidingView behavior={   Platform.OS === 'ios' ? 'padding'    : 'height'      } style={   flex: 1   }
      >,
  <ScrollView style={styles.scrollView} ref={scrollViewRef} keyboardShouldPersistTaps="handled"
        >,
  <View style={styles.header}>
            <Text style={styles.title}>Rent Splitting Calculator</Text>,
  <Text style={styles.subtitle}>
              Create a split payment and track who has paid their share,
  </Text>
          </View>,
  {loading && (
            <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={"#6366F1" /}>
              <Text style={styles.loadingText}>Creating split payment...</Text>,
  </View>
          )},
  {error && (
            <View style={styles.errorContainer}>,
  <AlertCircle size={20} color={{"#EF4444"} /}>
              <Text style={styles.errorText}>{error}</Text>,
  </View>
          )},
  <View style={styles.formSection}>
            <Text style={styles.sectionTitle}>Payment Details</Text>,
  <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Title</Text>,
  <TextInput style={styles.input} placeholder="e.g., April Rent",
  value={title} onChangeText={setTitle}
              />,
  </View>
            <View style={styles.inputGroup}>,
  <Text style={styles.inputLabel}>Total Amount</Text>
              <View style={styles.currencyInputContainer}>,
  <View style={styles.currencyPrefix}>
                  <Text style={styles.currencyPrefixText}>$</Text>,
  </View>
                <TextInput style={styles.currencyInput} placeholder="0.00",
  value= {totalAmount} onChangeText={(value) => setTotalAmount(formatCurrency(value))} keyboardType="numeric";
                />,
  </View>
            </View>,
  <View style= {styles.inputGroup}>
              <Text style={styles.inputLabel}>Description (Optional)</Text>,
  <TextInput style={[styles.input, styles.textArea]} placeholder="Add details about this payment",
  value= {description} onChangeText={setDescription}
                multiline,
  numberOfLines= {3}
              />,
  </View>
            <View style={styles.inputGroup}>,
  <Text style={styles.inputLabel}>Due Date (Optional)</Text>
              <TouchableOpacity style={styles.datePickerButton} onPress={() => setShowDatePicker(true)},
  >
                <Calendar size={18} color={"#6366F1" /}>,
  <Text style={styles.datePickerButtonText}>
                  {dueDate ? dueDate.toLocaleDateString()      : 'Select Due Date'},
  </Text>
              </TouchableOpacity>,
  {showDatePicker && (
                <DateTimePicker value={dueDate || new Date()} mode="date",
  display="default"
                  onChange={(event selectedDate) => {
  setShowDatePicker(false)
                    if (selectedDate) {
  setDueDate(selectedDate)
                    },
  }}
                />,
  )}
            </View>,
  <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>Recurring Payment</Text>,
  <Switch value={isRecurring} onValueChange={setIsRecurring} trackColor={   false: '#E5E7EB', true: '#C7D2FE'       },
  thumbColor={   isRecurring ? '#6366F1'   : '#f4f3f4'      }
              />,
  </View>
            {isRecurring && (
  <View style={styles.recurringContainer}>
                <View style={styles.recurringTypeContainer}>,
  <TouchableOpacity style={[styles.recurringTypeButton
                      recurringType === 'weekly' && styles.recurringTypeButtonActive, ,
   ]} onPress={() => setRecurringType('weekly')},
  >
                    <Text,
  style = {[
                        styles.recurringTypeText,
  recurringType = == 'weekly' && styles.recurringTypeTextActive;
                      ]},
  >
                      Weekly,
  </Text>
                  </TouchableOpacity>,
  <TouchableOpacity style = {[
                      styles.recurringTypeButton,
  recurringType = == 'monthly' && styles.recurringTypeButtonActive;
                    ]} onPress= {() => setRecurringType('monthly')},
  >
                    <Text,
  style = {[
                        styles.recurringTypeText,
  recurringType = == 'monthly' && styles.recurringTypeTextActive;
                      ]},
  >
                      Monthly,
  </Text>
                  </TouchableOpacity>,
  <TouchableOpacity style = {[
                      styles.recurringTypeButton,
  recurringType = == 'custom' && styles.recurringTypeButtonActive;
                    ]} onPress= {() => setRecurringType('custom')},
  >
                    <Text,
  style = {[
                        styles.recurringTypeText,
  recurringType = == 'custom' && styles.recurringTypeTextActive;
                      ]},
  >
                      Custom,
  </Text>
                  </TouchableOpacity>,
  </View>
                {recurringType = == 'custom' && (
  <View style={styles.customIntervalContainer}>
                    <TextInput style={styles.customIntervalInput} placeholder="1",
  value={recurringInterval} onChangeText={(text) => setRecurringInterval(text.replace(/[^0-9]/g ''))} keyboardType="numeric",
  />
                    <Text style= {styles.customIntervalText}>days</Text>,
  </View>
                )},
  </View>
            )},
  </View>
          <View style={styles.formSection}>,
  <View style={styles.participantsHeader}>
              <Text style={styles.sectionTitle}>Participants</Text>,
  <TouchableOpacity style={styles.evenSplitButton} onPress={calculateEvenSplit}
              >,
  <Text style={styles.evenSplitButtonText}>Split Evenly</Text>
              </TouchableOpacity>,
  </View>
            {participants.map((participant) => (
  <View key={participant.id} style={styles.participantContainer}>
                <View style={styles.participantInfo}>,
  <View style={styles.participantInitial}>
                    <Text style={styles.initialText}>,
  {participant.displayName.charAt(0)}
                    </Text>,
  </View>
                  <Text style={styles.participantName}>{participant.displayName}</Text>,
  </View>
                <View style={styles.participantAmountContainer}>,
  <Text style={styles.currencyPrefixSmall}>$</Text>
                  <TextInput style={styles.participantAmountInput} placeholder="0.00",
  value= {participant.amount} onChangeText={   (value) => {
  const formattedValue = formatCurrency(value) setParticipants(
  participants.map(p => {);
  p.id = == participant.id, ,
  ? { ...p, amount    : formattedValue       },
  : p)
                        ),
  )
                    }},
  keyboardType="numeric"
                  />,
  <TouchableOpacity style={styles.removeParticipantButton} onPress={() => removeParticipant(participant.id)} disabled={participant.userId === user? .id} // Can't remove yourself
                  >,
  <Trash2 size={16} color={ participant.userId === user? .id ? '#D1D5DB'    : '#EF4444'  }
                    />,
  </TouchableOpacity>
                </View>,
  </View>
            ))},
  <TouchableOpacity style={styles.addParticipantButton} onPress={addParticipant}
            >,
  <UserPlus size={18} color={"#6366F1" /}>
              <Text style={styles.addParticipantButtonText}>Add Participant</Text>,
  </TouchableOpacity>
            <View style={styles.remainingContainer}>,
  <Text style={styles.remainingLabel}>Remaining to allocate:</Text>
              <Text ,
  style = {[
                  styles.remainingAmount,
  calculateRemainingAmount() !== 0 && styles.remainingAmountError, ,
   ]},
  >
                ${calculateRemainingAmount().toFixed(2)},
  </Text>
            </View>,
  </View>
          <TouchableOpacity style = {[
              styles.createButton,
  (loading || calculateRemainingAmount() != = 0) && styles.createButtonDisabled;
            ]} onPress= {handleCreateSplitPayment} disabled={loading || calculateRemainingAmount() !== 0},
  >
            <Text style={styles.createButtonText}>Create Split Payment</Text>,
  </TouchableOpacity>
        </ScrollView>,
  </KeyboardAvoidingView>
      {showFriendPicker && (
  <View style={styles.friendPickerOverlay}>
          <View style={styles.friendPickerContainer}>,
  <View style={styles.friendPickerHeader}>
              <Text style={styles.friendPickerTitle}>Select Friend</Text>,
  <TouchableOpacity onPress={() => setShowFriendPicker(false)}>
                <Text style={styles.friendPickerClose}>Cancel</Text>,
  </TouchableOpacity>
            </View>,
  {loadingFriends ? (
              <ActivityIndicator size="large" color="#6366F1" style={{ marginTop    : 20} /}>,
  ) : (
              <ScrollView style={styles.friendList}>,
  {friends.length === 0 ? (
                  <Text style={styles.noFriendsText}>No friends available to add</Text>,
  )  : (friends.map((friend) => (
                    <TouchableOpacity key={friend.id} style={styles.friendItem} onPress={() => handleSelectFriend(friend)},
  >
                      <View style={styles.friendInitial}>,
  <Text style={styles.initialText}>
                          {friend.display_name ? friend.display_name.charAt(0) : 'F'},
  </Text>
                      </View>,
  <Text style={styles.friendName}>
                        {friend.display_name || friend.email || 'Friend'},
  </Text>
                    </TouchableOpacity>,
  ))
                )},
  </ScrollView>
            )},
  </View>
        </View>,
  )}
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({
  container: {
    flex: 1,
  backgroundColor: '#FFFFFF'
  },
  scrollView: { flex: 1 }
  header: {
    padding: 20,
  borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6' }
  title: { fontSize: 24,
    fontWeight: 'bold',
  color: '#111827',
    marginBottom: 4 },
  subtitle: {
    fontSize: 16,
  color: '#6B7280'
  },
  loadingContainer: {
    padding: 20,
  alignItems: 'center',
    justifyContent: 'center' }
  loadingText: {
    marginTop: 10,
  fontSize: 16,
    color: '#6B7280' }
  errorContainer: {
    marginHorizontal: 20,
  marginTop: 10,
    padding: 10,
  backgroundColor: '#FEF2F2',
    borderRadius: 8,
  flexDirection: 'row',
    alignItems: 'center' }
  errorText: { color: '#B91C1C',
    marginLeft: 8,
  flex: 1 }
  formSection: {
    padding: 20,
  borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6' }
  sectionTitle: { fontSize: 18,
    fontWeight: '600',
  color: '#111827',
    marginBottom: 16 },
  inputGroup: { marginBottom: 16 }
  inputLabel: { fontSize: 14,
    fontWeight: '500',
  color: '#374151',
    marginBottom: 8 },
  input: {
    backgroundColor: '#F9FAFB',
  borderWidth: 1,
    borderColor: '#E5E7EB',
  borderRadius: 8,
    padding: 12,
  fontSize: 16,
    color: '#111827' }
  textArea: {
    height: 80,
  textAlignVertical: 'top'
  },
  currencyInputContainer: {
    flexDirection: 'row',
  alignItems: 'center'
  },
  currencyPrefix: { backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
  paddingVertical: 14,
    borderTopLeftRadius: 8,
  borderBottomLeftRadius: 8,
    borderWidth: 1,
  borderColor: '#E5E7EB',
    borderRightWidth: 0 },
  currencyPrefixText: {
    fontSize: 16,
  fontWeight: '500',
    color: '#374151' }
  currencyPrefixSmall: { fontSize: 14,
    fontWeight: '500',
  color: '#374151',
    marginRight: 4 },
  currencyInput: {
    flex: 1,
  backgroundColor: '#F9FAFB',
    borderWidth: 1,
  borderColor: '#E5E7EB',
    borderTopRightRadius: 8,
  borderBottomRightRadius: 8,
    padding: 12,
  fontSize: 16,
    color: '#111827' }
  datePickerButton: { flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: '#F9FAFB',
    borderWidth: 1,
  borderColor: '#E5E7EB',
    borderRadius: 8,
  padding: 12 }
  datePickerButtonText: {
    marginLeft: 8,
  fontSize: 16,
    color: '#6B7280' }
  switchContainer: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    marginBottom: 16 },
  switchLabel: {
    fontSize: 16,
  fontWeight: '500',
    color: '#374151' }
  recurringContainer: { marginBottom: 16,
    backgroundColor: '#F9FAFB',
  borderRadius: 8,
    padding: 12 },
  recurringTypeContainer: { flexDirection: 'row',
    marginBottom: 12 },
  recurringTypeButton: { flex: 1,
    alignItems: 'center',
  paddingVertical: 8,
    backgroundColor: '#FFFFFF',
  borderWidth: 1,
    borderColor: '#E5E7EB',
  marginHorizontal: 4,
    borderRadius: 6 },
  recurringTypeButtonActive: {
    backgroundColor: '#EEF2FF',
  borderColor: '#6366F1'
  },
  recurringTypeText: {
    fontSize: 14,
  color: '#6B7280'
  },
  recurringTypeTextActive: {
    color: '#6366F1',
  fontWeight: '500'
  },
  customIntervalContainer: {
    flexDirection: 'row',
  alignItems: 'center'
  },
  customIntervalInput: {
    width: 60,
  backgroundColor: '#FFFFFF',
    borderWidth: 1,
  borderColor: '#E5E7EB',
    borderRadius: 6,
  padding: 8,
    marginRight: 8,
  textAlign: 'center'
  },
  customIntervalText: {
    fontSize: 14,
  color: '#6B7280'
  },
  participantsHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 16 },
  evenSplitButton: { backgroundColor: '#EEF2FF',
    paddingHorizontal: 12,
  paddingVertical: 6,
    borderRadius: 6 },
  evenSplitButtonText: {
    color: '#6366F1',
  fontSize: 14,
    fontWeight: '500' }
  participantContainer: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    marginBottom: 12,
  backgroundColor: '#F9FAFB',
    padding: 12,
  borderRadius: 8 }
  participantInfo: { flexDirection: 'row',
    alignItems: 'center',
  flex: 1 }
  participantInitial: { width: 36,
    height: 36,
  borderRadius: 18,
    backgroundColor: '#6366F1',
  alignItems: 'center',
    justifyContent: 'center',
  marginRight: 10 }
  initialText: {
    color: '#FFFFFF',
  fontSize: 16,
    fontWeight: '600' }
  participantName: { fontSize: 14,
    fontWeight: '500',
  color: '#374151',
    flex: 1 },
  participantAmountContainer: {
    flexDirection: 'row',
  alignItems: 'center'
  },
  participantAmountInput: {
    width: 80,
  backgroundColor: '#FFFFFF',
    borderWidth: 1,
  borderColor: '#E5E7EB',
    borderRadius: 6,
  padding: 8,
    textAlign: 'right' }
  removeParticipantButton: { padding: 8,
    marginLeft: 8 },
  addParticipantButton: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    backgroundColor: '#EEF2FF',
  padding: 12,
    borderRadius: 8,
  marginBottom: 16 }
  addParticipantButtonText: { color: '#6366F1',
    fontSize: 14,
  fontWeight: '500',
    marginLeft: 8 },
  remainingContainer: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    marginTop: 4 },
  remainingLabel: {
    fontSize: 14,
  fontWeight: '500',
    color: '#374151' }
  remainingAmount: {
    fontSize: 14,
  fontWeight: '600',
    color: '#047857' }
  remainingAmountError: {
    color: '#EF4444' }
  createButton: { backgroundColor: '#6366F1',
    padding: 16,
  borderRadius: 8,
    alignItems: 'center',
  margin: 20 }
  createButtonDisabled: {
    backgroundColor: '#C7D2FE' }
  createButtonText: {
    color: '#FFFFFF',
  fontSize: 16,
    fontWeight: '600' })
  friendPickerOverlay: {
    position: 'absolute'),
  top: 0,
    bottom: 0,
  left: 0,
    right: 0),
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  justifyContent: 'center',
    alignItems: 'center' }
  friendPickerContainer: { width: '80%',
    backgroundColor: '#FFFFFF',
  borderRadius: 12,
    maxHeight: '80%',
  padding: 20 }
  friendPickerHeader: {
    flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
  marginBottom: 16,
    paddingBottom: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6' }
  friendPickerTitle: {
    fontSize: 18,
  fontWeight: '600',
    color: '#111827' }
  friendPickerClose: {
    fontSize: 16,
  color: '#6366F1'
  },
  friendList: { maxHeight: 300 }
  friendItem: {
    flexDirection: 'row',
  alignItems: 'center',
    padding: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6' }
  friendInitial: { width: 36,
    height: 36,
  borderRadius: 18,
    backgroundColor: '#6366F1',
  alignItems: 'center',
    justifyContent: 'center',
  marginRight: 10 }
  friendName: {
    fontSize: 14,
  fontWeight: '500',
    color: '#374151' }
  noFriendsText: { padding: 20,
    textAlign: 'center',
  color: '#6B7280',
    fontSize: 14 },
  })