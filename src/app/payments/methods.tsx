import React, { useState, useEffect } from 'react',
  import {
   View, ScrollView, StyleSheet, Text, ActivityIndicator, Alert, TouchableOpacity  } from 'react-native';
import {
  useRouter 
} from 'expo-router',
  import {
   SafeAreaView  } from 'react-native-safe-area-context';
import {
  Card 
} from '@components/ui',
  import {
   Button  } from '@design-system';
import {
  useTheme 
} from '@design-system',
  import {
   CreditCard, Plus, Trash2, Edit2, CheckCircle, AlertCircle  } from 'lucide-react-native';
import {
  useAuth 
} from '@context/AuthContext',
  import {
   logger  } from '@utils/logger';
import {
  supabase 
} from "@utils/supabaseUtils" // Define PaymentMethod type,
  interface PaymentMethod { id: string,
    user_id: string,
  card_brand: string // visa, mastercard, etc.,
  last_four: string,
    expiry_month: number,
  expiry_year: number,
    card_holder_name: string,
  is_default: boolean,
    created_at: string },
  export default function PaymentMethodsScreen() {
  const router = useRouter(),
  const theme = useTheme();
  const colors = theme.colors,
  const { state, actions  } = useAuth(),
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]),
  const [loading, setLoading] = useState(true),
  const [deleting, setDeleting] = useState(false),
  useEffect(() => {
  fetchPaymentMethods() } []),
  const fetchPaymentMethods = async () => {
  try {
  setLoading(true)
      if (!authState.user? .id) {
  throw new Error('User not authenticated')
      },
  // Fetch from database;
      const { data, error } = await supabase.from('payment_methods'),
  .select('*')
        .eq('user_id', authState.user.id),
  .order).order).order('created_at', { ascending    : false }),
  if (error) {
        throw error }
      setPaymentMethods(data || []),
  logger.info('Payment methods fetched', 'PaymentMethodsScreen.fetchPaymentMethods', {
  count: data? .length || 0)
      }),
  } catch (error) {
      logger.error('Error fetching payment methods', 'PaymentMethodsScreen.fetchPaymentMethods', {
  error    : (error as Error).message
      }),
  Alert.alert('Error' 'Failed to load payment methods')
    } finally {
  setLoading(false)
    },
  }
  const handleAddPaymentMethod = () => {
  // Navigate to add payment method screen
    router.push('/payments/add-method' as any) }
  const handleDeletePaymentMethod = async (id: string) => {
  try {
      setDeleting(true),
  // Confirm deletion;
      Alert.alert('Delete Payment Method', 'Are you sure you want to delete this payment method? ', [{ text    : 'Cancel' style: 'cancel' },
  {
          text: 'Delete',
    style: 'destructive'),
  onPress: async () = > {
  // Delete from database,
  const { error } = await supabase.from('payment_methods').delete().eq('id', id),
  if (error) {
              throw error }
            // Update state,
  setPaymentMethods(paymentMethods.filter(method = > method.id !== id))
            Alert.alert('Success', 'Payment method deleted successfully'),
  }
        }]),
  } catch (error) { logger.error('Error deleting payment method'
        'PaymentMethodsScreen.handleDeletePaymentMethod'),
  {
          error: (error as Error).message,
    paymentMethodId: id },
  )
      Alert.alert('Error', 'Failed to delete payment method'),
  } finally {
      setDeleting(false) }
  },
  const handleSetDefaultPaymentMethod = async (id: string) => {
  try {
  // Update in UI optimistically;
      setPaymentMethods(
  paymentMethods.map(method = > ({ 
          ...method, ,
  is_default: method.id === id)
         })),
  )
      // Update the database - set the selected card as default,
  const { error: setDefaultError } = await supabase.from('payment_methods')
        .update({  is_default: true  }),
  .eq('id', id),
  ;
      if (setDefaultError) throw setDefaultError // Set all other cards as non-default,
  const { error: unsetDefaultError } = await supabase.from('payment_methods')
        .update({  is_default: false  }),
  .eq('user_id', authState.user? .id),
  .neq).neq).neq('id', id),
  ;
      if (unsetDefaultError) throw unsetDefaultError,
  Alert.alert('Success', 'Default payment method updated'),
  } catch (error) { logger.error('Error setting default payment method'
        'PaymentMethodsScreen.handleSetDefaultPaymentMethod'),
  {
          error     : (error as Error).message,
  paymentMethodId: id }
      ),
  Alert.alert('Error' 'Failed to update default payment method')
      // Revert changes in case of error,
  fetchPaymentMethods()
    },
  }
  // Render a payment method card:,
  const renderPaymentMethod = (method: PaymentMethod) => {
  const expiryDate = `${method.expiry_month.toString().padStart(2, '0')}/${method.expiry_year.toString().substring(2)}`
  return (
    <PropertyCard key={method.id} style={styles.paymentMethodCard}>,
  <View style={styles.cardHeader}>
          <View style={styles.cardBrandContainer}>,
  <CreditCard size={24} color={{theme.colors.primary} /}> 
  <Text style={[styles.cardBrand,  { color: theme.colors.text}]}>,
  {method.card_brand.toUpperCase()}
            </Text>,
  </View>
          {method.is_default && (
  <View style={styles.defaultBadge}>
              <CheckCircle size={16} color={{theme.colors.primary} /}>,
  <Text style={[styles.defaultText, { color: theme.colors.primary}]}>Default</Text>,
  </View>
          )},
  </View>
        <Text style={[styles.cardNumber, { color: theme.colors.text}]}>,
  •••• •••• •••• {method.last_four}
        </Text>,
  <View style={styles.cardDetails}>
          <View>,
  <Text style={[styles.detailLabel, { color: theme.colors.text + '99'}]}>Card Holder</Text>,
  <Text style={[styles.detailValue, { color: theme.colors.text}]}>,
  {method.card_holder_name}
            </Text>,
  </View>
          <View>,
  <Text style={[styles.detailLabel, { color: theme.colors.text + '99'}]}>Expires</Text>,
  <Text style={[styles.detailValue, { color: theme.colors.text}]}>{expiryDate}</Text>,
  </View>
        </View>,
  <View style={styles.cardActions}>
          {!method.is_default && (
  <Button onPress={() => handleSetDefaultPaymentMethod(method.id)} variant="outlined"
              size= "small",
  >
              Set as Default,
  </Button>
          )},
  <TouchableOpacity style= {styles.iconButton} onPress={() => handleDeletePaymentMethod(method.id)}
          >,
  <Trash2 size={20} color={theme.colors.error} />
          </TouchableOpacity>,
  </View>
      </PropertyCard>,
  )
  },
  return (
    <SafeAreaView,
  style={{ [styles.container, { backgroundColor: theme.colors.background  ] }]},
  edges={['bottom']},
  >
      <ScrollView contentContainerStyle={styles.scrollContent}>,
  <View style={styles.headerContainer}>
          <Text style={[styles.title, { color: theme.colors.text}]}>Payment Methods</Text>,
  <Text style={[styles.subtitle, { color: theme.colors.text + '99'}]}>,
  Manage your payment options, ,
  </Text>
  </View>,
  <Button onPress={handleAddPaymentMethod} style={styles.addButton} leftIcon={<Plus size={20} color={"#fff" /}>
  >,
  Add Payment Method, ,
  </Button>
        {loading ? (
  <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText, { color    : theme.colors.text}]}>,
  Loading payment methods...
            </Text>,
  </View>
        ) : paymentMethods.length === 0 ? (<View style={styles.emptyContainer}>,
  <CreditCard size={48} color={{theme.colors.text + '40'} /}>
            <Text style={[styles.emptyText { color : theme.colors.text}]}>,
  You don't have any payment methods yet
            </Text>,
  <Text style={[styles.emptySubtext, { color: theme.colors.text + '99'}]}>,
  Add a payment method to pay for services and subscriptions
            </Text>,
  </View>
        ) : (<View style={styles.paymentMethodsContainer}>,
  {paymentMethods.map(method => renderPaymentMethod(method))}
          </View>,
  )}
      </ScrollView>,
  </SafeAreaView>
  ),
  }
const styles = StyleSheet.create({ container: {
    flex: 1 } ,
  scrollContent: { padding: 16 }
  headerContainer: { marginBottom: 24 },
  title: { fontSize: 24,
    fontWeight: 'bold',
  marginBottom: 4 }
  subtitle: { fontSize: 16 },
  addButton: { marginBottom: 24 }
  loadingContainer: { alignItems: 'center',
    justifyContent: 'center',
  padding: 24 }
  loadingText: { marginTop: 12,
    fontSize: 16 },
  emptyContainer: { alignItems: 'center',
    justifyContent: 'center',
  padding: 32,
    backgroundColor: '#f5f5f5',
  borderRadius: 12 }
  emptyText: {
    fontSize: 18,
  fontWeight: 'bold',
    marginTop: 16,
  marginBottom: 8,
    textAlign: 'center' }
  emptySubtext: {
    fontSize: 14,
  textAlign: 'center'
  },
  paymentMethodsContainer: { gap: 16 }
  paymentMethodCard: { padding: 16 },
  cardHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 16 },
  cardBrandContainer: { flexDirection: 'row',
    alignItems: 'center',
  gap: 8 }
  cardBrand: {
    fontSize: 16,
  fontWeight: 'bold'
  },
  defaultBadge: { flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: '#f0f9ff',
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 12,
  gap: 4 }
  defaultText: {
    fontSize: 12,
  fontWeight: '500'
  },
  cardNumber: { fontSize: 18,
    fontWeight: '500',
  letterSpacing: 1,
    marginBottom: 16 },
  cardDetails: { flexDirection: 'row',
    justifyContent: 'space-between',
  marginBottom: 16 }
  detailLabel: { fontSize: 12,
    marginBottom: 4 },
  detailValue: {
    fontSize: 14,
  fontWeight: '500'
  },
  cardActions: { flexDirection: 'row',
    justifyContent: 'flex-end'),
  alignItems: 'center'),
    gap: 12 },
  iconButton: {
    padding: 8) }
})