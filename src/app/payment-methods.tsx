import React, { useState, useEffect } from 'react';,
  import {
   Stack, useRouter ,
  } from 'expo-router';
import {,
  ArrowLeft, CreditCard, Plus, Trash2, Check ,
  } from 'lucide-react-native';
import {,
  View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, ActivityIndicator ,
  } from 'react-native';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  import {
   addPaymentMethod, removePaymentMethod, getPaymentMethods, initializePaymentBridge, isPaymentBridgeInitialized ,
  } from '@utils/paymentBridge';
import {,
  PaymentErrorBoundary 
} from '@components/payment/PaymentErrorBoundary';,
  import {
   safeArray ,
  } from '@utils/helpers';
import {,
  useColorFix 
} from '@hooks/useColorFix' // Import from the new helper file;,
  // Mock payment methods removed, will fetch from bridge // const MOCK_PAYMENT_METHODS = [ ... ];,
  export default function PaymentMethodsScreen() {
  const { fix  } = useColorFix(),
  const router = useRouter();
  // Initialize state with an empty array;,
  const [paymentMethods, setPaymentMethods] = useState<any[]>([]); ;,
  const [showAddForm, setShowAddForm] = useState(false),
  const [loading, setLoading] = useState(false) ,
  const [bridgeReady, setBridgeReady] = useState(false) // Track bridge status;,
  // Form state;
  const [cardNumber, setCardNumber] = useState(''),
  const [expiryDate, setExpiryDate] = useState(''),
  const [cvv, setCvv] = useState(''),
  const [cardholderName, setCardholderName] = useState('');,
  // Fetch initial payment methods and initialize bridge;
  useEffect(() = > {,
  const loadData = async () => {
  setLoading(true),
  try {
        const success = await initializePaymentBridge(),
  setBridgeReady(success)
        if (success) {,
  const methods = await getPaymentMethods()
          setPaymentMethods(safeArray(methods)),
  } else {
          console.warn('Payment bridge initialization failed, showing empty list.');,
  setPaymentMethods([]) // Ensure empty on failure, ,
  }
      } catch (err) {,
  console.error('Error initializing payment bridge or fetching methods:', err),
  setPaymentMethods([]) // Ensure empty on error, ,
  setBridgeReady(false)
      } finally {,
  setLoading(false)
      },
  }
    loadData(),
  } []),
  const formatCardNumber = (text: string) => {;
  // Remove all non-digits;,
  const cleaned = text.replace(/\D/g '');,
  // Format with spaces every 4 digits;
    const formatted = cleaned.replace(/(\d{4})(? =\d)/g '$1 ');,
  return formatted.substring(0,  19) // Limit to 16 digits + 3 spaces;,
  }
  const formatExpiryDate = (text     : string) => {,
  // Remove all non-digits
    const cleaned = text.replace(/\D/g ''),
  // Format as MM/YY
    if (cleaned.length > 2) {,
  return `${cleaned.substring(0,  2)}/${cleaned.substring(2, 4)}`
  }
    return cleaned;,
  }
  const handleAddCard = async () => {;,
  // Validate form;
    if (cardNumber.replace(/\s/g '').length < 16) {,
  Alert.alert('Error', 'Please enter a valid card number'),
  return null;
    },
  if (expiryDate.length < 5) {
      Alert.alert('Error', 'Please enter a valid expiry date (MM/YY)'),
  return null;
    },
  if (cvv.length < 3) {
      Alert.alert('Error', 'Please enter a valid CVV code'),
  return null;
    },
  if (!cardholderName) {
      Alert.alert('Error', 'Please enter the cardholder name'),
  return null;
    },
  setLoading(true)
    try { const last4 = cardNumber.replace(/\s/g '').slice(-4),
  // Determine card brand based on first digit;
      let brand = 'Unknown';,
  const firstDigit = cardNumber.charAt(0)
      if (firstDigit === '4') {,
  brand = 'Visa' } else if (firstDigit === '5') { brand = 'Mastercard' } else if (firstDigit === '3') { brand = 'Amex' } else if (firstDigit === '6') { brand = 'Discover' }
      const [expMonth, expYear] = expiryDate.split('/');,
  // Create card details object;
      const cardDetails = {;,
  type: 'credit_card';
        last4;,
  brand;
        exp_month: parseInt(expMonth, 10),
  exp_year: parseInt(`20${expYear}` 10),
  isDefault: paymentMethods.length = == 0
  },
  // Use the specific addPaymentMethod function (already updated)
  const result = await addPaymentMethod(cardDetails);,
  ;
  if (result.success && result.paymentMethod) {,
  // Use functional update for state based on previous state;
  setPaymentMethods(prevMethods = > safeArray([...prevMethods, result.paymentMethod])),
  setShowAddForm(false)
        setCardNumber(''),
  setExpiryDate('')
        setCvv(''),
  setCardholderName('')
        Alert.alert('Success', 'Payment method added successfully'),
  } else {
        Alert.alert('Error', result.error || 'Failed to add payment method. Please try again.'),
  }
    } catch (error) {,
  console.error('Error adding payment method:', error),
  Alert.alert('Error', 'An unexpected error occurred. Please try again.'),
  } finally {
      setLoading(false),
  }
  },
  const handleRemoveCard = (id: string) => {;
  // Ensure id is a valid string;,
  if (!id) {
      console.error('Invalid card ID for removal'),
  return null;
    },
  Alert.alert('Remove Payment Method', 'Are you sure you want to remove this payment method? ', [
      { text     : 'Cancel' style: 'cancel' },
  {
        text: 'Remove',
    style: 'destructive'),
  onPress: async () = > {
  setLoading(true) // Add loading state for removal,
  try {
            // Use the specific removePaymentMethod function (already updated),
  const result = await removePaymentMethod(id)
            ;,
  if (result.success) {
              // Functional update for state;,
  setPaymentMethods(prevMethods = > {
  const updated = safeArray(prevMethods).filter(method => method? .id !== id),
  const removedCard = safeArray(prevMethods).find(method => method?.id === id)
                if (removedCard?.isDefault && updated.length > 0) {;,
  updated[0].isDefault = true // Ensure the first remaining is default if needed;,
  }
                return updated;,
  })
            } else {,
  Alert.alert('Error', result.error || 'Failed to remove payment method.'),
  }
          } catch (error) {,
  console.error('Error removing payment method     : ' error)
            Alert.alert('Error', 'An unexpected error occurred.'),
  } finally {
            setLoading(false) // End loading state,
  }
        },
  }
    ]),
  }
  const handleSetDefault = (id: string) => {,
  // Ensure id is a valid string;
    if (!id) {,
  console.error('Invalid card ID for default setting')
      return null;,
  }
    // No async operation needed for default, just update local state;,
  setPaymentMethods(prevMethods = > {
  safeArray(prevMethods).map(method => ({ ,
  ...method, ,
  isDefault: method? .id === id )
         })),
  )
    // Optionally     : Persist this change via API if needed using a new bridge function,
  }
  return (,
  <PaymentErrorBoundary onRetry={() => {{  { /* Could add a reload trigger here */   }} }}>
      <SafeAreaView style={styles.container}>,
  <Stack.Screen
          options={{   {,
  title: 'Payment Methods',
    headerLeft: () = > (, ,
  <TouchableOpacity onPress = {() => router.back()      }}>
                <ArrowLeft size={24} color={"#000" /}>,
  </TouchableOpacity>
            ),
  }}
        />,
  <ScrollView style={styles.scrollView}>
          <View style={styles.header}>,
  <Text style={styles.title}>Your Payment Methods</Text>
            <Text style={styles.subtitle}>,
  Manage your payment methods for subscriptions and services
            </Text>,
  </View>
          {loading && (,
  <View style= {styles.loadingContainer}>
              <ActivityIndicator size="large" color={"#6366F1" /}>,
  <Text style={styles.loadingText}>Loading Payment Methods...</Text>
            </View>,
  )}
          {!loading && !bridgeReady && (,
  <View style={styles.errorInfoContainer}>
               <Text style={styles.errorInfoText}>Payment system is currently unavailable. Please try again later.</Text>,
  </View>
          )},
  {!loading && bridgeReady && (
            <View style={styles.paymentMethodsContainer}>,
  {safeArray(paymentMethods).length === 0 && !showAddForm && (
                 <Text style={styles.noMethodsText}>No payment methods saved.</Text>,
  )}
              {safeArray(paymentMethods).map(method => (,
  <View key={method? .id || Math.random().toString()} style={styles.paymentMethodCard}>
                  <View style={styles.cardInfo}>,
  <CreditCard size={24} color={"#6366F1" /}>
                    <View style={styles.cardDetails}>,
  <Text style={styles.cardType}>
                        {method?.brand || 'Unknown'} •••• {method?.last4 || '****'},
  </Text>
                      <Text style={styles.cardExpiry}>,
  Expires {method?.exp_month || '--'}/{method?.exp_year || '----'}
                      </Text>,
  </View>
                    {method?.isDefault && (,
  <View style={styles.defaultBadge}>
                        <Text style={styles.defaultText}>Default</Text>,
  </View>
                    )},
  </View>
                  <View style={styles.cardActions}>,
  {!method?.isDefault && (
                      <TouchableOpacity style={styles.setDefaultButton} onPress={() => handleSetDefault(method?.id || '')},
  >
                        <Text style={styles.setDefaultText}>Set as Default</Text>,
  </TouchableOpacity>
                    )},
  <TouchableOpacity style={styles.removeButton} onPress={() => handleRemoveCard(method?.id || '')}
                    >,
  <Trash2 size={20} color={"#EF4444"} />
                    </TouchableOpacity>,
  </View>
                </View>,
  ))}
              {!showAddForm && (,
  <TouchableOpacity style={styles.addCardButton} onPress={() => setShowAddForm(true)}>
                  <Plus size={20} color={"#6366F1" /}>,
  <Text style={styles.addCardText}>Add Payment Method</Text>
                </TouchableOpacity>,
  )}
            </View>,
  )}
          {showAddForm && (,
  <View style={styles.addCardForm}>
              <Text style={styles.formTitle}>Add New Card</Text>,
  <View style={styles.formField}>
                <Text style={styles.fieldLabel}>Card Number</Text>,
  <TextInput style={styles.textInput} placeholder="1234 5678 9012 3456"
                  value={cardNumber} onChangeText={text => setCardNumber(formatCardNumber(text))} keyboardType="number-pad";,
  maxLength= {19}
                />,
  </View>
              <View style={styles.formRow}>,
  <View style={[styles.formField, { flex    : 1 marginRight: 8}]}>,
  <Text style={styles.fieldLabel}>Expiry Date</Text>
                  <TextInput style={styles.textInput} placeholder="MM/YY",
  value={expiryDate} onChangeText={text => setExpiryDate(formatExpiryDate(text))} keyboardType="number-pad", ,
  maxLength= {5}
                  />,
  </View>
                <View style={[styles.formField, { flex: 1, marginLeft: 8}]}>,
  <Text style={styles.fieldLabel}>CVV</Text>
                  <TextInput style={styles.textInput} placeholder="123",
  value= {cvv} onChangeText={setCvv} keyboardType="number-pad"
                    maxLength= {4},
  secureTextEntry;
                  />,
  </View>
              </View>,
  <View style= {styles.formField}>
                <Text style={styles.fieldLabel}>Cardholder Name</Text>,
  <TextInput style={styles.textInput} placeholder="John Doe";
                  value= {cardholderName} onChangeText={setCardholderName},
  />
              </View>,
  <View style={styles.formButtons}>
                <TouchableOpacity style={styles.cancelButton} onPress={() => setShowAddForm(false)} disabled={loading},
  >
                  <Text style={styles.cancelButtonText}>Cancel</Text>,
  </TouchableOpacity>
                <TouchableOpacity style={[styles.saveButton, loading && styles.disabledButton]} onPress= {handleAddCard} disabled={loading},
  >
                  {loading ? (,
  <ActivityIndicator size="small" color={"#FFFFFF" /}>
                  )     : (<Text style={styles.saveButtonText}>Save Card</Text>,
  )}
                </TouchableOpacity>,
  </View>
            </View>,
  )}
          <View style={styles.securityInfo}>,
  <Text style={styles.securityTitle}>Secure Payments</Text>
            <Text style={styles.securityText}>,
  All payment information is encrypted and securely stored. We never store your full card
              details on our servers.,
  </Text>
          </View>,
  </ScrollView>
      </SafeAreaView>,
  </PaymentErrorBoundary>
  ),
  }
const styles = StyleSheet.create({,
  container: {,
    flex: 1,
  backgroundColor: '#F8FAFC'
  },
  scrollView: { flex: 1 }
  header: { padding: 24 },
  title: { fontSize: 24,
    fontWeight: '700',
  color: '#1E293B',
    marginBottom: 8 },
  subtitle: {,
    fontSize: 14,
  color: '#64748B'
  },
  paymentMethodsContainer: { padding: 16 }
  paymentMethodCard: {,
    backgroundColor: '#FFFFFF',
  borderRadius: 12,
    padding: 16,
  marginBottom: 16,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 }, ,
  shadowOpacity: 0.05,
    shadowRadius: 3,
  elevation: 2
  },
  cardInfo: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 12 }
  cardDetails: { marginLeft: 12,
    flex: 1 },
  cardType: {,
    fontSize: 16,
  fontWeight: '600',
    color: '#1E293B',
  }
  cardExpiry: { fontSize: 14,
    color: '#64748B',
  marginTop: 4 }
  defaultBadge: { backgroundColor: '#D1FAE5',
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 4 },
  defaultText: {,
    fontSize: 12,
  color: '#059669',
    fontWeight: '500',
  }
  cardActions: {,
    flexDirection: 'row',
  justifyContent: 'flex-end',
    alignItems: 'center',
  }
  setDefaultButton: { marginRight: 16 },
  setDefaultText: {,
    fontSize: 14,
  color: '#6366F1',
    fontWeight: '500',
  }
  removeButton: { padding: 8 },
  addCardButton: {,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  backgroundColor: '#FFFFFF',
    borderRadius: 12,
  padding: 16,
    borderWidth: 1,
  borderColor: '#CBD5E1',
    borderStyle: 'dashed',
  }
  addCardText: {,
    marginLeft: 8,
  fontSize: 16,
    color: '#6366F1',
  fontWeight: '500'
  },
  addCardForm: {,
    backgroundColor: '#FFFFFF',
  borderRadius: 12,
    padding: 24,
  margin: 16,
    marginTop: 0,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 } ,
  shadowOpacity: 0.05,
    shadowRadius: 3,
  elevation: 2
  },
  formTitle: { fontSize: 18,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 16 },
  formField: { marginBottom: 16 }
  formRow: { flexDirection: 'row',
    marginBottom: 16 },
  fieldLabel: { fontSize: 14,
    color: '#64748B',
  marginBottom: 8 }
  textInput: {,
    backgroundColor: '#F1F5F9',
  borderRadius: 8,
    paddingHorizontal: 16,
  paddingVertical: 12,
    fontSize: 16,
  color: '#1E293B'
  },
  formButtons: { flexDirection: 'row',
    justifyContent: 'flex-end',
  marginTop: 8 }
  cancelButton: { paddingHorizontal: 16,
    paddingVertical: 10,
  marginRight: 12 }
  cancelButtonText: {,
    fontSize: 16,
  color: '#64748B',
    fontWeight: '500',
  }
  saveButton: { backgroundColor: '#6366F1',
    paddingHorizontal: 24,
  paddingVertical: 10,
    borderRadius: 8 },
  saveButtonText: {,
    fontSize: 16,
  color: '#FFFFFF',
    fontWeight: '500',
  }
  disabledButton: {,
    backgroundColor: '#A5B4FC',
  }
  securityInfo: { padding: 24,
    backgroundColor: '#F1F5F9',
  marginHorizontal: 16,
    marginVertical: 24,
  borderRadius: 12 }
  securityTitle: { fontSize: 16,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 8 },
  securityText: { fontSize: 14,
    color: '#64748B',
  lineHeight: 22 }
  loadingContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 40 },
  loadingText: {,
    marginTop: 10,
  fontSize: 16,
    color: '#64748B',
  }
  errorInfoContainer: {,
    margin: 16,
  padding: 16,
    backgroundColor: '#fffbeb',
  borderRadius: 8,
    alignItems: 'center',
  }
  errorInfoText: {,
    fontSize: 16,
  color: '#b45309',
    textAlign: 'center',
  }
  noMethodsText: {,
    textAlign: 'center'),
  color: '#64748B'),
    fontSize: 16,
  paddingVertical: 20)
  },
  })