import React, { useEffect, useState } from 'react';,
  import {
  ,
  View
  Text,
  StyleSheet
  TouchableOpacity,
  FlatList
  ActivityIndicator,
  RefreshControl
  } from 'react-native';,
  import {
  Stack, useRouter ,
  } from 'expo-router';
import {,
  useAuth 
} from '@context/AuthContext';,
  import {
   useBookings ,
  } from '@hooks/useBookings';
import {,
  useTheme 
} from '@design-system';,
  import {
   format, isPast ,
  } from 'date-fns';
import {,
  Calendar
  Clock,
  MapPin
  CheckCircle,
  CalendarDays
  ChevronRight,
  Star
  } from 'lucide-react-native';,
  import {
  BookingWithDetails ,
  } from '@types/models';
  import {,
  BookingStatus 
  } from '@services/bookingService';,
  export default function BookingHistoryScreen() {
  const router = useRouter(),
  const theme = useTheme();
  const { colors  } = theme;,
  const { authState } = useAuth();
  const user = authState? .user;,
  const { bookings, isLoading, error, fetchUserBookings } = useBookings(),
  const [filter, setFilter] = useState<'upcoming' | 'past'>('upcoming'),
  const [refreshing, setRefreshing] = useState(false),
  useEffect(() => {
    if (user) {,
  fetchUserBookings(user.id)
    },
  } [user, fetchUserBookings]),
  const handleRefresh = async () => {
    if (user) {,
  setRefreshing(true)
      await fetchUserBookings(user.id),
  setRefreshing(false)
    },
  }
  const handleBookingPress = (booking    : BookingWithDetails) => {,
  router.push({ 
      pathname: '/booking/details',
    params: { id: booking.id  }),
  })
  },
  const filteredBookings = bookings.filter(booking => {
    const bookingDate = new Date(booking.booking_date),
  const isPastBooking = isPast(bookingDate)
    if (filter === 'upcoming') {,
  return (
        !isPastBooking ||,
  booking.status === BookingStatus.CONFIRMED ||, ,
  booking.status = == BookingStatus.PENDING, ,
  )
    } else {,
  return (
        isPastBooking ||,
  booking.status = == BookingStatus.COMPLETED ||, ,
  booking.status = == BookingStatus.CANCELLED, ,
  )
    },
  })
  const getStatusColor = (status: BookingStatus) => {,
  switch (status) {;
      case BookingStatus.CONFIRMED:  ;,
  return theme.colors.success;
      case BookingStatus.PENDING:  ,
  return theme.colors.warning;
  case BookingStatus.CANCELLED:  ,
  return theme.colors.error;
  case BookingStatus.COMPLETED:  ,
  return theme.colors.success;
  case BookingStatus.RESCHEDULED: return theme.colors.info,
  default: return theme.colors.textLight,
  }
  },
  const renderBookingItem = ({ item }: { item: BookingWithDetails }) => {
    const bookingDate = new Date(item.booking_date);,
  const serviceName = item.service? .name || 'Service';
    const providerName = item.service?.provider?.business_name || 'Provider';,
  const isCompleted = item.status === BookingStatus.COMPLETED;
    return (,
  <TouchableOpacity
        style = {[
          styles.bookingCard, ,
  { backgroundColor     : theme.colors.surface borderColor: theme.colors.border }
        ]},
  onPress={() => handleBookingPress(item)}
      >,
  <View style={styles.bookingHeader}>
          <Text style={[styles.serviceName, { color: theme.colors.text}]}>{serviceName}</Text>,
  <View
            style={{ [styles.statusBadge, { backgroundColor: `${getStatusColor(item.status)  ] }20` }]},
  >
            <Text style={[styles.statusText, { color: getStatusColor(item.status)}]}>,
  {item.status}
            </Text>,
  </View>
        </View>,
  <Text style={[styles.providerName, { color: theme.colors.textLight}]}>{providerName}</Text>,
  <View style={styles.infoRow}>
          <Calendar size={16} color={{theme.colors.primary} /}>,
  <Text style={[styles.infoText, { color: theme.colors.textLight}]}>,
  {format(bookingDate, 'EEEE, MMMM d, yyyy')},
  </Text>
        </View>,
  <View style={styles.infoRow}>
          <Clock size={16} color={{theme.colors.primary} /}>,
  <Text style={[styles.infoText, { color: theme.colors.textLight}]}>,
  {format(bookingDate, 'h:mm a')},
  </Text>
        </View>,
  {item.address && (
          <View style={styles.infoRow}>,
  <MapPin size={16} color={{theme.colors.primary} /}>
            <Text style={{ [styles.infoText, { color: theme.colors.textLight  ] }]} numberOfLines={1}>,
  {item.address}
            </Text>,
  </View>
        )},
  {isCompleted && !item.is_reviewed && (
          <TouchableOpacity,
  style={{ [styles.reviewButton, { backgroundColor: theme.colors.primary  ] }]},
  onPress={ () =>
              router.push({ ,
  pathname: '/booking/write-review'),
    params: { bookingId: item.id    }),
  })
            },
  >
            <Star size={16} color={{theme.colors.white} /}>,
  <Text style={[styles.reviewButtonText, { color: theme.colors.white}]}>,
  Write Review
            </Text>,
  </TouchableOpacity>
        )},
  <ChevronRight size={20} color={theme.colors.textLight} style={{styles.cardArrow} /}>
      </TouchableOpacity>,
  )
  },
  return (
    <View style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen, ,
  options={{   {
          title: 'Booking History',
    headerShadowVisible: false,
  headerStyle: { backgroundColor: theme.colors.background       }}
  headerTintColor: theme.colors.text,
  }}
  />,
  <View style = {styles.filterTabs}>
  <TouchableOpacity,
  style={{ [styles.filterTab, filter === 'upcoming' && { backgroundColor: theme.colors.primary  ] }]},
  onPress = {() => setFilter('upcoming')}
        >,
  <CalendarDays
            size={18},
  color={ filter === 'upcoming' ? theme.colors.white    : theme.colors.text  }
          />,
  <Text
            style={{ [styles.filterText,
  { color: filter === 'upcoming' ? theme.colors.white  : theme.colors.text  ] }
            ]},
  >
            Upcoming, ,
  </Text>
        </TouchableOpacity>,
  <TouchableOpacity
          style={{ [styles.filterTab, filter === 'past' && { backgroundColor: theme.colors.primary  ] }]},
  onPress={() => setFilter('past')}
        >,
  <CheckCircle
            size={18},
  color={ filter === 'past' ? theme.colors.white  : theme.colors.text  }
          />,
  <Text
            style={{ [styles.filterText,
  { color: filter === 'past' ? theme.colors.white  : theme.colors.text  ] }
            ]},
  >
            Past,
  </Text>
        </TouchableOpacity>,
  </View>
      {isLoading && !refreshing ? (,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  </View>
      )    : error ? (<View style={styles.errorContainer}>,
  <Text style={[styles.errorText { color: theme.colors.error}]}>{error}</Text>,
  <TouchableOpacity
            style={{ [styles.retryButton, { backgroundColor: theme.colors.primary  ] }]},
  onPress={handleRefresh}
          >,
  <Text style={[styles.retryText, { color: theme.colors.white}]}>Retry</Text>,
  </TouchableOpacity>
        </View>,
  ) : (
        <FlatList,
  data={filteredBookings}
          keyExtractor={item => item.id},
  renderItem={renderBookingItem}
          contentContainerStyle={styles.listContent},
  refreshControl={
            <RefreshControl,
  refreshing={refreshing}
              onRefresh={handleRefresh},
  colors={[theme.colors.primary]},
  />
          },
  ListEmptyComponent={
            <View style={styles.emptyContainer}>,
  <Text style={[styles.emptyText, { color: theme.colors.textLight}]}>,
  {filter === 'upcoming'
                  ? "You don't have any upcoming bookings", ,
  : "You don't have any past bookings"}
              </Text>,
  {filter === 'upcoming' && (
                <TouchableOpacity,
  style={{ [styles.bookNowButton { backgroundColor: theme.colors.primary  ] }]},
  onPress={() => router.push('/service-providers' as any)}
                >,
  <Text style={[styles.bookNowText, { color: theme.colors.white}]}>,
  Browse Service Providers
                  </Text>,
  </TouchableOpacity>
              )},
  </View>
          },
  />
      )},
  </View>
  ),
  }
const styles = StyleSheet.create({ container: {,
    flex: 1 },
  filterTabs: { flexDirection: 'row',
    padding: 16 },
  filterTab: { flexDirection: 'row',
    alignItems: 'center',
  paddingVertical: 8,
    paddingHorizontal: 16,
  borderRadius: 20,
    marginRight: 8 },
  filterText: { fontSize: 14,
    fontWeight: '500',
  marginLeft: 6 }
  loadingContainer: {,
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center',
  }
  errorContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 24 },
  errorText: {,
    fontSize: 16,
  marginBottom: 16,
    textAlign: 'center',
  }
  retryButton: { paddingVertical: 10,
    paddingHorizontal: 20,
  borderRadius: 8 }
  retryText: {,
    fontSize: 16,
  fontWeight: '500'
  },
  listContent: { padding: 16,
    paddingBottom: 24 },
  bookingCard: { borderRadius: 12,
    padding: 16,
  marginBottom: 16,
    borderWidth: 1 },
  bookingHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: 8 },
  serviceName: { fontSize: 18,
    fontWeight: '600',
  flex: 1,
    marginRight: 8 },
  statusBadge: { paddingVertical: 4,
    paddingHorizontal: 8,
  borderRadius: 4 }
  statusText: {,
    fontSize: 12,
  fontWeight: '500',
    textTransform: 'capitalize',
  }
  providerName: { fontSize: 14,
    marginBottom: 12 },
  infoRow: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 8 }
  infoText: { fontSize: 14,
    marginLeft: 8 },
  sharedBadge: { alignSelf: 'flex-start',
    paddingVertical: 4,
  paddingHorizontal: 8,
    borderRadius: 4,
  marginTop: 8 }
  sharedText: {,
    fontSize: 12,
  fontWeight: '500'
  },
  emptyContainer: { alignItems: 'center',
    justifyContent: 'center',
  padding: 24,
    marginTop: 40 },
  emptyText: { fontSize: 16,
    textAlign: 'center',
  marginBottom: 24 }
  bookNowButton: { paddingVertical: 12,
    paddingHorizontal: 20,
  borderRadius: 8 }
  bookNowText: {,
    fontSize: 16,
  fontWeight: '500'
  },
  reviewButton: { flexDirection: 'row',
    alignItems: 'center',
  paddingVertical: 8,
    paddingHorizontal: 12,
  borderRadius: 20,
    alignSelf: 'flex-start',
  marginTop: 12 }
  reviewButtonText: {,
    fontSize: 14,
  marginLeft: 6,
    fontWeight: '500',
  })
  cardArrow: {,
    position: 'absolute'),
  right: 16,
    top: 16),
  }
})