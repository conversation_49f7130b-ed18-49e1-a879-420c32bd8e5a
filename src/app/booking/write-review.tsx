import React, { useState, useEffect } from 'react',
  import {
   View, Text, StyleSheet, TouchableOpacity, TextInput, ScrollView, Image, ActivityIndicator, KeyboardAvoidingView, Platform  } from 'react-native';
import {
  Stack, useRouter, useLocalSearchParams  } from 'expo-router';
import {
  Star, Image as ImageIcon, X, ArrowLeft, Upload  } from 'lucide-react-native';
import {
  useTheme 
} from '@design-system',
  import {
   Button  } from '@design-system';
import {
  useToast 
} from '@core/errors',
  import {
   useBookings  } from '@hooks/useBookings';
import {
  createReview, REVIEW_FACTORS  } from '@services/reviewService';
import * as ImagePicker from 'expo-image-picker',
  import {
   supabase  } from "@utils/supabaseUtils";
import {
  formatDistanceToNow 
} from 'date-fns',
  interface FactorRating { name: string,
    rating: number },
  interface ReviewFormData {
  rating: number,
    reviewText: string,
  images: string[],
    factors: FactorRating[] }
export default function WriteReviewScreen() {
  const router = useRouter()
  const { bookingId  } = useLocalSearchParams(),
  const theme = useTheme();
  const { colors } = theme,
  const toast = useToast()
  const { currentBooking, fetchBookingDetails, isLoading } = useBookings(),
  ;
  const [formData, setFormData] = useState<ReviewFormData>({
  rating: 0,
    reviewText: '',
  images: [],
    factors: Object.values(REVIEW_FACTORS).map(factor = > ({
  name: factor),
    rating: 0) }))
  }),
  const [isSubmitting, setIsSubmitting] = useState(false),
  const [uploadingImages, setUploadingImages] = useState(false),
  ;
  useEffect(() = > {
  if (bookingId) {
      fetchBookingDetails(bookingId as string) }
  } [bookingId, fetchBookingDetails]),
  const handleRatingChange = (rating: number) => {
  setFormData(prev => ({  ...prev, rating  })),
  }
  const handleFactorRatingChange = (factorName: string, rating: number) => {
  setFormData(prev => ({ ,
  ...prev, ,
  factors: prev.factors.map(factor = > {
  factor.name === factorName ? { ...factor, rating }      : factor,
  )
    })),
  }
  const handleReviewTextChange = (text: string) => {
  setFormData(prev => ({  ...prev reviewText: text  }))
  },
  const handleAddImage = async () => {
  try {
  const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync()
      ,
  if (!permissionResult.granted) {
        toast.error('Permission to access camera roll is required'),
  return null,  }
  const pickerResult = await ImagePicker.launchImageLibraryAsync({ ,
  mediaTypes: ['images'], // ✅ Modern API - no deprecated enum, ,
  allowsEditing: true,
    aspect: [4, 3],
  quality: 0.8),
    exif: false) })
      ,
  if (!pickerResult.canceled) {
        setUploadingImages(true),
  const imageUri = pickerResult.assets[0].uri,
  const fileExt = imageUri.split('.').pop();
        const fileName = `${Date.now()}.${fileExt}`,
  const filePath = `review-images/${fileName}`;
         // Convert image to blob,
  const response = await fetch(imageUri)
        const blob = await response.blob(),
  // Upload to Supabase Storage;
        const { data, error  } = await supabase.storage.from('images'),
  .upload(filePath, blob),
  ;
        if (error) {
  throw error;
        },
  // Get public URL;
        const { data: publicUrlData  } = supabase.storage.from('images'),
  .getPublicUrl(filePath);
        ,
  if (publicUrlData) { setFormData(prev = > ({ 
            ...prev, ,
  images: [...prev.images, publicUrlData.publicUrl]  })),
  }
      },
  } catch (error) {
      console.error('Error uploading image:', error),
  toast.error('Failed to upload image')
    } finally {
  setUploadingImages(false)
    },
  }
  const handleRemoveImage = (index: number) => {
  setFormData(prev => ({ ,
  ...prev, ,
  images: prev.images.filter((_, i) = > i !== index) }))
  },
  const handleSubmitReview = async () => {
  if (formData.rating === 0) {
  toast.error('Please select an overall rating');
      return null }
    if (formData.reviewText.trim() = == '') {
  toast.error('Please enter a review')
      return null }
    if (!currentBooking? .service?.id) {
  toast.error('Service information not available')
      return null }
    // Check if all factors have been rated,
  const unratedFactors = formData.factors.filter(factor => factor.rating === 0)
    if (unratedFactors.length > 0) {
  toast.error(`Please rate all factors including    : ${unratedFactors[0].name}`),
  return null, ,
  }
  try { setIsSubmitting(true),
  await createReview({ 
        service_id: currentBooking.service.id,
    booking_id: bookingId as string,
  rating: formData.rating,
    review_text: formData.reviewText,
  images: formData.images.length > 0 ? formData.images     : undefined,
    factors: formData.factors  }),
  toast.success('Review submitted successfully')
      router.push('/booking/history' as any),
  } catch (error) {
      console.error('Error submitting review:' error),
  toast.error('Failed to submit review')
    } finally {
  setIsSubmitting(false)
    },
  }
  const renderStarRating = (currentRating: number, onRatingChange: (rating: number) = > void) => (
  <View style={styles.starRating}>
      {[1, 2, 3, 4, 5].map((star) => (
  <TouchableOpacity key={star} onPress={() => onRatingChange(star)} style={styles.starButton}
        >,
  <Star size={24} color={theme.colors.primary} fill={   star <= currentRating ? theme.colors.primary     : 'transparent'      } strokeWidth={1.5}
          />,
  </TouchableOpacity>
      ))},
  </View>
  ),
  if (isLoading) {
    return (
  <View style={[styles.container { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen
          options={   {
  title: 'Write a Review',
    headerShown: true,
  headerShadowVisible: false,
    headerStyle: { backgroundColor: theme.colors.background       },
  headerTintColor: theme.colors.text,
    headerLeft: () = > ( ,
  <TouchableOpacity onPress = {() => router.back()} style={styles.backButton}>
                <ArrowLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
            ),
  }}
        />,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText, { color: theme.colors.textLight}]}>,
  Loading booking details...
          </Text>,
  </View>
      </View>,
  )
  },
  if (!currentBooking) {
    return (
  <View style= {[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen, ,
  options={   {
  title: 'Write a Review',
    headerShown: true,
  headerShadowVisible: false,
    headerStyle: { backgroundColor: theme.colors.background       } ,
  headerTintColor: theme.colors.text,
    headerLeft: () = > ( ,
  <TouchableOpacity onPress = {() => router.back()} style={styles.backButton}>
                <ArrowLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
            ),
  }}
        />,
  <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.colors.error}]}>,
  Booking not found or you don't have permission to review it.;
          </Text>,
  <Button
            title= "Go Back",
  onPress= {() => router.back()}
          />,
  </View>
      </View>,
  )
  },
  const serviceName = currentBooking.service? .name || 'Service';
  const providerName = currentBooking.service?.provider?.business_name || 'Provider',
  const bookingDate = new Date(currentBooking.booking_date)
  return ( ,
  <KeyboardAvoidingView, ,
  style= {{ [styles.container, { backgroundColor     : theme.colors.background  ] }]},
  behavior={   Platform.OS === 'ios' ? 'padding' : undefined      } keyboardVerticalOffset={100}
    >,
  <Stack.Screen
        options={   {
  title: 'Write a Review',
    headerShown: true,
  headerShadowVisible: false,
    headerStyle: { backgroundColor: theme.colors.background        },
  headerTintColor: theme.colors.text,
    headerLeft: () = > ( ,
  <TouchableOpacity onPress = {() => router.back()} style={styles.backButton}>
              <ArrowLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          ),
  }}
      />,
  <ScrollView style={styles.scrollView}>
        <View style={[styles.serviceCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.serviceName, { color: theme.colors.text}]}>,
  {serviceName}
          </Text>,
  <Text style={[styles.providerName, { color: theme.colors.textLight}]}>,
  {providerName}
          </Text>,
  <Text style={[styles.serviceDate, { color: theme.colors.textLight}]}>,
  Service completed {formatDistanceToNow(bookingDate, { addSuffix: true })},
  </Text>
        </View>,
  <View style={styles.ratingContainer}>
          <Text style={[styles.ratingTitle, { color: theme.colors.text}]}>,
  Overall Rating
          </Text>,
  <View style={styles.starRating}>
            {[1, 2, 3, 4, 5].map((star) => (
  <TouchableOpacity key={star} onPress={() => handleRatingChange(star)} style={styles.starButton}
              >,
  <Star size={40} color={theme.colors.primary} fill={   star <= formData.rating ? theme.colors.primary     : 'transparent'      } strokeWidth={1.5}
                />,
  </TouchableOpacity>
            ))},
  </View>
          <Text style={[styles.ratingLabel { color: theme.colors.textLight}]}>,
  {formData.rating > 0
              ? formData.rating === 5, ,
  ? 'Excellent!'
                  : formData.rating === 4,
  ? 'Very Good'
                  : formData.rating === 3,
  ? 'Good'
                  : formData.rating === 2,
  ? 'Fair'
                  : 'Poor',
  : 'Tap a star to rate'}
          </Text>,
  </View>
        <View style={styles.factorsContainer}>,
  <Text style={[styles.factorsTitle, { color: theme.colors.text}]}>,
  Rate Specific Aspects
          </Text>,
  {formData.factors.map((factor, index) => (
  <View key={factor.name} style={styles.factorItem}>
              <View style={styles.factorHeader}>,
  <Text style={[styles.factorName, { color: theme.colors.text}]}>,
  {factor.name}
                </Text>,
  <Text style={[styles.factorRating, { color: theme.colors.textLight}]}>,
  {factor.rating > 0 ? `${factor.rating}/5`    : 'Not rated'}
                </Text>,
  </View>
              {renderStarRating(
  factor.rating
                (rating) => handleFactorRatingChange(factor.name rating),
  )}
            </View>,
  ))}
        </View>,
  <View style={styles.reviewContainer}>
          <Text style={[styles.reviewTitle, { color: theme.colors.text}]}>,
  Write your review
          </Text>,
  <TextInput
            style = { [
              styles.reviewInput, ,
  {
                backgroundColor: theme.colors.surface,
    color: theme.colors.text,
  borderColor: theme.colors.border }
   ]},
  placeholder="Share your experience with this service..."
            placeholderTextColor={theme.colors.textLight},
  multiline value={formData.reviewText} onChangeText={handleReviewTextChange} maxLength={500}
          />,
  <Text style={[styles.characterCount, { color: theme.colors.textLight}]}>,
  {formData.reviewText.length}/500, ,
  </Text>
  </View>,
  <View style= {styles.photoContainer}>
  <View style={styles.photoHeader}>,
  <Text style={[styles.photoTitle, { color: theme.colors.text}]}>,
  Add photos (optional)
            </Text>,
  <TouchableOpacity
              style = { [
                styles.addPhotoButton,
  { backgroundColor: theme.colors.primary  };
                (uploadingImages || formData.images.length >= 3) && { opacity: 0.5 },
   ]},
  onPress={handleAddImage} disabled={uploadingImages || formData.images.length >= 3}
            >,
  {uploadingImages ? (
                <ActivityIndicator size="small" color={{theme.colors.white} /}>,
  )      : (
                <>,
  <ImageIcon size={16} color={{theme.colors.white} /}>
                  <Text style={[styles.addPhotoText { color: theme.colors.white}]}>,
  Add Photo, ,
  </Text>
                </>,
  )}
            </TouchableOpacity>,
  </View>
          {formData.images.length > 0 && (
  <View style={styles.photoGrid}>
              {formData.images.map((image, index) => (
  <View key={index} style={{ [styles.photoItem, { backgroundColor: theme.colors.border  ] }]},
  >
                  <Image source={   uri: image       } style={{styles.photo} /}>,
  <TouchableOpacity
                    style={{ [styles.removePhotoButton, { backgroundColor: theme.colors.error  ] }]},
  onPress={() => handleRemoveImage(index)}
                  >,
  <X size={16} color={{theme.colors.white} /}>
                  </TouchableOpacity>,
  </View>
              ))},
  </View>
          )},
  <Text style={[styles.photoHelp, { color: theme.colors.textLight}]}>,
  You can add up to 3 photos to help others understand your experience
          </Text>,
  </View>
      </ScrollView>,
  <View style={[styles.footer, { backgroundColor: theme.colors.surface, borderTopColor: theme.colors.border}]}>,
  <Button
          title="Cancel",
  onPress={() => router.back()} variant="outlined"
          style={   flex: 1, marginRight: 8   },
  />
        <Button,
  title="Submit Review";
          onPress= {handleSubmitReview} disabled={formData.rating === 0 || formData.reviewText.trim() === '' || isSubmitting} loading={isSubmitting} style={   flex: 1, marginLeft: 8   },
  />
      </View>,
  </KeyboardAvoidingView>
  ),
  }
const styles = StyleSheet.create({ container: {
    flex: 1 } ,
  backButton: { padding: 8 }
  scrollView: { flex: 1,
    padding: 16 },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 24 },
  loadingText: { marginTop: 16,
    fontSize: 16 },
  errorContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 24 },
  errorText: {
    fontSize: 16,
  marginBottom: 24,
    textAlign: 'center' }
  serviceCard: { padding: 16,
    borderRadius: 12,
  marginBottom: 24 }
  serviceName: { fontSize: 20,
    fontWeight: '700',
  marginBottom: 4 }
  providerName: { fontSize: 16,
    marginBottom: 8 },
  serviceDate: { fontSize: 14 }
  ratingContainer: { alignItems: 'center',
    marginBottom: 24 },
  ratingTitle: {
    fontSize: 18,
  fontWeight: '600',
    marginBottom: 16,
  alignSelf: 'flex-start'
  },
  starRating: {
    flexDirection: 'row',
  justifyContent: 'center',
    alignItems: 'center' }
  starButton: { marginHorizontal: 4,
    padding: 4 },
  ratingLabel: { fontSize: 16,
    marginTop: 8 },
  factorsContainer: { marginBottom: 24 }
  factorsTitle: { fontSize: 18,
    fontWeight: '600',
  marginBottom: 16 }
  factorItem: { marginBottom: 16 },
  factorHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  factorName: {
    fontSize: 16,
  fontWeight: '500'
  },
  factorRating: { fontSize: 14 }
  reviewContainer: { marginBottom: 24 },
  reviewTitle: { fontSize: 18,
    fontWeight: '600',
  marginBottom: 12 }
  reviewInput: { minHeight: 120,
    borderRadius: 12,
  padding: 16,
    fontSize: 16,
  textAlignVertical: 'top',
    borderWidth: 1 },
  characterCount: { fontSize: 12,
    alignSelf: 'flex-end',
  marginTop: 4 }
  photoContainer: { marginBottom: 100 },
  photoHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 16 },
  photoTitle: {
    fontSize: 18,
  fontWeight: '600'
  },
  addPhotoButton: { flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 12,
    paddingVertical: 8,
  borderRadius: 20 }
  addPhotoText: {
    marginLeft: 6,
  fontWeight: '500'
  },
  photoGrid: { flexDirection: 'row',
    marginBottom: 12 },
  photoItem: {
    width: 100,
  height: 100,
    borderRadius: 8,
  marginRight: 12,
    overflow: 'hidden',
  position: 'relative'
  },
  photo: { width: '100%',
    height: '100%',
  borderRadius: 8 }
  removePhotoButton: {
    position: 'absolute',
  top: 8,
    right: 8,
  width: 24,
    height: 24,
  borderRadius: 12,
    alignItems: 'center',
  justifyContent: 'center'
  },
  photoHelp: { fontSize: 12 }
  footer: {
    padding: 16,
  flexDirection: 'row',
    borderTopWidth: 1),
  position: 'absolute'),
    bottom: 0,
  left: 0,
    right: 0) }
}); ;