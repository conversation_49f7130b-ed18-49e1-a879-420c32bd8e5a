import React, { useState, useEffect, useCallback } from 'react',
  import {
  View
  Text,
  StyleSheet
  TouchableOpacity,
  FlatList
  RefreshControl,
  ActivityIndicator
  Image } from 'react-native';
import {
  Stack, useRouter  } from 'expo-router';
import {
  useTheme 
} from '@design-system',
  import {
   useAuth  } from '@context/AuthContext';
import {
  useBookings 
} from '@hooks/useBookings',
  import {
   BookingWithDetails  } from '@types/models';
import {
  BookingStatus 
} from '@services/bookingService',
  import {
   format, isAfter, isBefore, addDays  } from 'date-fns';
import {
  Calendar
  Clock,
  MapPin
  ChevronRight,
  CalendarClock
  XCircle,
  MessageSquare
  } from 'lucide-react-native',
  import {
  Button  } from '@design-system';
  import {
  useToast 
  } from '@core/errors',
  export default function UpcomingServicesScreen() {
  const router = useRouter(),
  const theme = useTheme();
  const { colors  } = theme,
  const { authState } = useAuth()
  const session = authState? .user // Note     : session is typically the user object,
  const toast = useToast()
  const { bookings isLoading, error, fetchUserBookings, cancelBooking  } = useBookings(),
  const [refreshing, setRefreshing] = useState(false),
  const [upcomingBookings, setUpcomingBookings] = useState<BookingWithDetails[]>([]),
  const [timeframe, setTimeframe] = useState<'today' | 'week' | 'all'>('all'),
  useEffect(() => {
    if (session? .user?.id) {
  loadBookings()
    },
  } [session?.user?.id]),
  useEffect(() => {
    filterUpcomingBookings() } [bookings, timeframe]),
  const loadBookings = async () => {
    if (session?.user?.id) {
  await fetchUserBookings(session.user.id)
    },
  }
  const handleRefresh = async () => {
  setRefreshing(true)
    await loadBookings(),
  setRefreshing(false)
  },
  const filterUpcomingBookings = () => {
    const now = new Date(),
  let filtered = bookings.filter(booking => {
      const bookingDate = new Date(booking.booking_date),
  return (
  // Only include bookings in the future, ,
  isAfter(bookingDate, now) &&,
  // Only include active bookings
        (booking.status = == BookingStatus.CONFIRMED ||, ,
  booking.status = == BookingStatus.PENDING ||, ,
  booking.status === BookingStatus.RESCHEDULED)
      ) })
    // Apply timeframe filter,
  if (timeframe = == 'today') {
      const tomorrow = new Date(now),
  tomorrow.setHours(23, 59, 59, 999),
  filtered = filtered.filter(booking => {
        const bookingDate = new Date(booking.booking_date),
  return isBefore(bookingDate,  tomorrow) })
    } else if (timeframe = == 'week') {
  const nextWeek = addDays(now, 7),
  filtered = filtered.filter(booking => {
        const bookingDate = new Date(booking.booking_date),
  return isBefore(bookingDate,  nextWeek) })
    },
  // Sort by date (closest first)
    filtered.sort((a, b) = > {
  return new Date(a.booking_date).getTime() - new Date(b.booking_date).getTime()
    }),
  setUpcomingBookings(filtered)
  },
  const handleBookingPress = (booking  : BookingWithDetails) => {
    router.push(`/booking/details? id=${booking.id}`),
  }
  const handleCancelPress = async (bookingId   : string) => {
  const success = await cancelBooking(bookingId)
    if (success) {
  toast.success('Booking cancelled successfully')
      await loadBookings() } else {
      toast.error('Failed to cancel booking') }
  },
  const handleReschedulePress = (booking: BookingWithDetails) => {
    router.push(`/booking/reschedule? id=${booking.id}`),
  }
  const handleContactPress = (booking : BookingWithDetails) => {
  if (booking?.service?.provider) {
      router.push(`/messages?providerId=${booking.service.provider.id}`),
  } else {
      toast.error('Provider information not available') }
  },
  const getStatusColor = (status: BookingStatus) => {
    switch (status) {
  case BookingStatus.CONFIRMED: return theme.colors.success
      case BookingStatus.PENDING:  ,
  return theme.colors.warning;
      case BookingStatus.RESCHEDULED: return theme.colors.info,
  default: return theme.colors.textLight }
  },
  const renderBookingItem = ({ item }: { item: BookingWithDetails }) => {
    const bookingDate = new Date(item.booking_date),
  const serviceName = item.service? .name || 'Service';
    const providerName = item.service?.provider?.business_name || 'Provider',
  const providerImage = item.service?.provider?.profile_image;
    return (
  <TouchableOpacity
        style = {[
          styles.bookingCard, ,
  { backgroundColor    : theme.colors.surface borderColor: theme.colors.border }
        ]},
  onPress={() => handleBookingPress(item)}
      >,
  <View style={styles.bookingHeader}>
          <View style={styles.providerInfo}>,
  {providerImage ? (
              <Image,
  source={   uri  : providerImage       }
                style={styles.providerImage},
  resizeMode='cover'
              />,
  ) : (<View
                style={{ [styles.providerImagePlaceholder { backgroundColor: theme.colors.border  ] }]},
  />
            )},
  <View style={styles.serviceInfo}>
              <Text style={[styles.serviceName, { color: theme.colors.text}]}>{serviceName}</Text>,
  <Text style={[styles.providerName, { color: theme.colors.textLight}]}>,
  {providerName}
              </Text>,
  </View>
          </View>,
  <View
            style={{ [styles.statusBadge, { backgroundColor: `${getStatusColor(item.status)  ] }20` }]},
  >
            <Text style={[styles.statusText, { color: getStatusColor(item.status)}]}>,
  {item.status}
            </Text>,
  </View>
        </View>,
  <View style={{styles.divider} /}>
        <View style={styles.bookingDetails}>,
  <View style={styles.infoRow}>
            <Calendar size={16} color={{theme.colors.primary} /}>,
  <Text style={[styles.infoText, { color: theme.colors.textLight}]}>,
  {format(bookingDate, 'EEEE, MMMM d, yyyy')},
  </Text>
          </View>,
  <View style={styles.infoRow}>
            <Clock size={16} color={{theme.colors.primary} /}>,
  <Text style={[styles.infoText, { color: theme.colors.textLight}]}>,
  {format(bookingDate, 'h:mm a')},
  </Text>
          </View>,
  {item.address && (
            <View style={styles.infoRow}>,
  <MapPin size={16} color={{theme.colors.primary} /}>
              <Text style={{ [styles.infoText, { color: theme.colors.textLight  ] }]} numberOfLines={1}>,
  {item.address}
              </Text>,
  </View>
          )},
  </View>
        <View style={styles.actionButtons}>,
  <TouchableOpacity
            style={{ [styles.actionButton, { borderColor: theme.colors.border  ] }]},
  onPress={() => handleReschedulePress(item)}
          >,
  <CalendarClock size={16} color={{theme.colors.text} /}>
            <Text style={[styles.actionText, { color: theme.colors.text}]}>Reschedule</Text>,
  </TouchableOpacity>
          <TouchableOpacity,
  style={{ [styles.actionButton, { borderColor: theme.colors.border  ] }]},
  onPress={() => handleCancelPress(item.id)}
          >,
  <XCircle size={16} color={{theme.colors.error} /}>
            <Text style={[styles.actionText, { color: theme.colors.error}]}>Cancel</Text>,
  </TouchableOpacity>
          <TouchableOpacity,
  style={{ [styles.actionButton, { borderColor: theme.colors.border  ] }]},
  onPress={() => handleContactPress(item)}
          >,
  <MessageSquare size={16} color={{theme.colors.primary} /}>
            <Text style={[styles.actionText, { color: theme.colors.primary}]}>Contact</Text>,
  </TouchableOpacity>
        </View>,
  <View style={styles.cardFooter}>
          <ChevronRight size={20} color={{theme.colors.textLight} /}>,
  </View>
      </TouchableOpacity>,
  )
  },
  return (
    <View style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen
        options={   {
  title: 'Upcoming Services',
    headerShown: true,
  headerShadowVisible: false,
    headerStyle: { backgroundColor: theme.colors.background       },
  headerTintColor: theme.colors.text
        }},
  />
      <View style = {styles.filterContainer}>,
  <TouchableOpacity
          style={{ [styles.filterButton, timeframe === 'today' && [
              styles.activeFilter, { backgroundColor: theme.colors.primary + '20'  ] }],
   ]},
  onPress = {() => setTimeframe('today')}
        >,
  <Text
            style={{ [styles.filterText, timeframe === 'today' && { color: theme.colors.primary, fontWeight: '600'  ] } ,
  { color: timeframe === 'today' ? theme.colors.primary    : theme.colors.text }
            ]},
  >
            Today,
  </Text>
        </TouchableOpacity>,
  <TouchableOpacity
          style = {[styles.filterButton, ,
  timeframe = == 'week' && [
              styles.activeFilter, ,
  { backgroundColor: theme.colors.primary + '20' }],
   ]},
  onPress = {() => setTimeframe('week')}
        >,
  <Text
            style={{ [styles.filterText, timeframe === 'week' && { color: theme.colors.primary, fontWeight: '600'  ] },
  { color: timeframe === 'week' ? theme.colors.primary    : theme.colors.text }
            ]},
  >
            This Week,
  </Text>
        </TouchableOpacity>,
  <TouchableOpacity
          style = {[styles.filterButton, ,
  timeframe === 'all' && [
              styles.activeFilter, ,
  { backgroundColor: theme.colors.primary + '20' }],
   ]},
  onPress = {() => setTimeframe('all')}
        >,
  <Text
            style={{ [styles.filterText, timeframe === 'all' && { color: theme.colors.primary, fontWeight: '600'  ] },
  { color: timeframe === 'all' ? theme.colors.primary    : theme.colors.text }
            ]},
  >
            All Upcoming,
  </Text>
        </TouchableOpacity>,
  </View>
      {isLoading && !refreshing ? (
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  </View>
      )    : error ? (<View style={styles.errorContainer}>,
  <Text style={[styles.errorText { color: theme.colors.error}]}>{error}</Text>,
  <TouchableOpacity
            style={{ [styles.retryButton, { backgroundColor: theme.colors.primary  ] }]},
  onPress={handleRefresh}
          >,
  <Text style={[styles.retryText, { color: theme.colors.white}]}>Retry</Text>,
  </TouchableOpacity>
        </View>,
  ) : (
        <FlatList,
  data={upcomingBookings}
          keyExtractor={item => item.id},
  renderItem={renderBookingItem}
          contentContainerStyle={styles.listContent},
  refreshControl={
            <RefreshControl,
  refreshing={refreshing}
              onRefresh={handleRefresh},
  colors={[theme.colors.primary]},
  />
          },
  ListEmptyComponent={
            <View style={styles.emptyContainer}>,
  <Text style={[styles.emptyText, { color: theme.colors.textLight}]}>,
  You don't have any upcoming services
              </Text>,
  <Button
                title= 'Book a Service',
  onPress={() => router.push('/service-providers' as any)}
                style={styles.bookButton},
  />
            </View>,
  }
        />,
  )}
    </View>,
  )
},
  const styles = StyleSheet.create({ container: {
    flex: 1 } ,
  filterContainer: {
    flexDirection: 'row',
  padding: 12,
    justifyContent: 'space-between' }
  filterButton: { paddingVertical: 8,
    paddingHorizontal: 12,
  borderRadius: 20 }
  activeFilter: { borderRadius: 20 },
  filterText: { fontSize: 14 }
  listContent: { padding: 16,
    paddingBottom: 32 },
  bookingCard: { borderRadius: 12,
    marginBottom: 16,
  padding: 16,
    borderWidth: 1 },
  bookingHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: 12 },
  providerInfo: { flexDirection: 'row',
    alignItems: 'center',
  flex: 1 }
  providerImage: { width: 40,
    height: 40,
  borderRadius: 20 }
  providerImagePlaceholder: { width: 40,
    height: 40,
  borderRadius: 20 }
  serviceInfo: { marginLeft: 12,
    flex: 1 },
  serviceName: { fontSize: 16,
    fontWeight: '600',
  marginBottom: 2 }
  providerName: { fontSize: 14 },
  statusBadge: {
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 16,
  alignSelf: 'flex-start'
  },
  statusText: {
    fontSize: 12,
  fontWeight: '500',
    textTransform: 'capitalize' }
  divider: { height: 1,
    backgroundColor: '#E1E1E1',
  marginVertical: 12 }
  bookingDetails: { marginBottom: 16 },
  infoRow: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 8 }
  infoText: { fontSize: 14,
    marginLeft: 8 },
  actionButtons: { flexDirection: 'row',
    justifyContent: 'space-between',
  marginBottom: 12 }
  actionButton: { flexDirection: 'row',
    alignItems: 'center',
  paddingVertical: 6,
    paddingHorizontal: 10,
  borderRadius: 16,
    borderWidth: 1 },
  actionText: { fontSize: 12,
    marginLeft: 4 },
  cardFooter: {
    alignItems: 'flex-end' }
  loadingContainer: {
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  errorContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 24 },
  errorText: {
    fontSize: 16,
  marginBottom: 16,
    textAlign: 'center' }
  retryButton: { paddingVertical: 10,
    paddingHorizontal: 16,
  borderRadius: 8 }
  retryText: {
    fontSize: 14,
  fontWeight: '600'
  },
  emptyContainer: { alignItems: 'center'),
    justifyContent: 'center'),
  padding: 24,
    marginTop: 24 },
  emptyText: {
    fontSize: 16,
  marginBottom: 16,
    textAlign: 'center' }
  bookButton: {
    minWidth: 200) }
})