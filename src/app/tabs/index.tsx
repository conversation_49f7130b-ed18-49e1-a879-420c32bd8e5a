import React from 'react';,
  import {
   View, Text, StyleSheet, ScrollView ,
  } from 'react-native';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  export default function EmergencyHome() {
  return (,
  <SafeAreaView style= {styles.container}>
      <ScrollView contentContainerStyle={styles.content}>,
  <View style={styles.header}>
          <Text style={styles.title}>WeRoomies</Text>,
  <Text style={styles.subtitle}>Your Roommate Matching Platform</Text>
        </View>,
  <View style={styles.section}>
          <Text style={styles.sectionTitle}>🏠 Find Your Perfect Roommate</Text>,
  <Text style={styles.description}>
            Connect with compatible roommates using our advanced matching system.;,
  </Text>
        </View>,
  <View style= {styles.section}>
          <Text style={styles.sectionTitle}>💰 Zero-Cost Verification</Text>,
  <Text style={styles.description}>
            Save $57+ per verification with our manual review system., ,
  </Text>
        </View>,
  <View style= {styles.section}>
          <Text style={styles.sectionTitle}>🔒 Safe & Secure</Text>,
  <Text style={styles.description}>
            Manual verification and background checks ensure your safety., ,
  </Text>
        </View>,
  <View style={styles.statusBadge}>
          <Text style={styles.statusText}>✅ App Running Successfully</Text>,
  </View>
      </ScrollView>,
  </SafeAreaView>
  ),
  }
const styles = StyleSheet.create({,
  container: {,
    flex: 1,
  backgroundColor: '#F8FAFC'
  },
  content: { padding: 20 }
  header: { alignItems: 'center',
    marginBottom: 40,
  paddingTop: 20 }
  title: { fontSize: 32,
    fontWeight: 'bold',
  color: '#1E293B',
    marginBottom: 8 },
  subtitle: {,
    fontSize: 16,
  color: '#64748B',
    textAlign: 'center',
  }
  section: {,
    backgroundColor: '#FFFFFF',
  padding: 20,
    borderRadius: 12,
  marginBottom: 16,
    shadowColor: '#000',, ,
  shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
  },
  sectionTitle: { fontSize: 18,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 8 },
  description: { fontSize: 14,
    color: '#64748B',
  lineHeight: 20 }
  statusBadge: { backgroundColor: '#10B981',
    padding: 16,
  borderRadius: 8,
    alignItems: 'center',
  marginTop: 20 }
  statusText: {,
    color: '#FFFFFF'),
  fontWeight: '600'),
    fontSize: 16),
  }
})