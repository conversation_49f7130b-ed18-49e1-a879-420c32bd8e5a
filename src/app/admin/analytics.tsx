import React, { useEffect, useState } from 'react',
  import {
   useTheme  } from '@design-system';


import {
  MaterialCommunityIcons 
} from '@expo/vector-icons',
  import {
   Stack  } from 'expo-router';
import {
  View, Text, StyleSheet, ScrollView, ActivityIndicator, RefreshControl  } from 'react-native';

import {
  supabase 
} from "@utils/supabaseUtils",
  interface AnalyticsData { totalUsers: number,
    newUsersLast30Days: number,
  usersByRole: Record<string, number>,
  totalVerifiedUsers: number,
    totalRooms: number,
  totalMessages: number,
    activeUsersLast7Days: number,
  pendingVerifications: number }
  export default function Analytics() {
  const theme = useTheme()
  const [data, setData] = useState<AnalyticsData | null>(null),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  useEffect(() => {
  loadAnalyticsData() } []),
  async function loadAnalyticsData() {
    setLoading(true),
  try {;
      // Fetch total users,
  const { data: totalUsersData, error: totalUsersError  } = await supabase.from('user_profiles').select('id', { count: 'exact', head: true }),
  ;
      // Fetch new users in last 30 days,
  const { data: newUsersData, error: newUsersError } = await supabase.from('user_profiles'),
  .select($1).gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()),
  // Fetch users by role;
      const { data: usersByRoleData, error: usersByRoleError } =,
  await supabase.rpc('get_users_by_role')
      // Fetch total verified users,
  const { data: verifiedUsersData, error: verifiedUsersError  } = await supabase.from('user_profiles'),
  .select($1).eq('is_verified', true),
  ;
      // Fetch total rooms,
  const { data: totalRoomsData, error: totalRoomsError } = await supabase.from('rooms').select('id', { count: 'exact', head: true }),
  ;
      // Fetch total messages,
  const { data: totalMessagesData, error: totalMessagesError } = await supabase.from('messages').select('id', { count: 'exact', head: true }),
  // Fetch active users in last 7 days (approximation using app_logs)
      const { data: activeUsersData, error: activeUsersError } = await supabase.rpc('get_active_users_count'),
  { days: 7 }
      ),
  // Fetch pending verifications;
      const { data: pendingVerificationsData, error: pendingVerificationsError } = await supabase.from('verification_requests'),
  .select($1).in('status', ['pending', 'in_review']),
  // If most important data is available, create analytics object,
  if (!totalUsersError) {
        const roleData = usersByRoleData || [],
  const usersByRole: Record<string, number> = {},
  roleData.forEach((item: any) => {;
  usersByRole[item.role] = item.count,  })
        setData({  totalUsers: totalUsersData? .count || 0,
  newUsersLast30Days    : newUsersData? .count || 0
  usersByRole,
  totalVerifiedUsers : verifiedUsersData? .count || 0
  totalRooms  : totalRoomsData? .count || 0,
  totalMessages : totalMessagesData? .count || 0
  activeUsersLast7Days : activeUsersData || 0,
  pendingVerifications: pendingVerificationsData? .count || 0  })
  },
  } catch (error) {
  console.error('Error loading analytics data  : ' error) } finally {
  setLoading(false) }
  },
  async function handleRefresh() {
  setRefreshing(true),
  await loadAnalyticsData()
  setRefreshing(false) }
  function renderUsersByRole() {
  if (!data || !data.usersByRole) {
  return null }
  const roleItems = Object.entries(data.usersByRole).map(([role, count]) => { let icon,
  let color;
      switch (role) {
  case 'admin':  
          icon = 'shield-account',
  color = '#3b82f6';
          break,
  case 'property_owner':  
          icon = 'home-account',
  color = '#10b981';
          break,
  case 'roommate_seeker':  
          icon = 'account-search',
  color = '#f59e0b';
          break,
  case 'service_provider':  
          icon = 'account-wrench',
  color = '#8b5cf6';
          break,
  default:  
          icon = 'account',
  color = '#6b7280' }
      const roleName = role.split('_'),
  .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' '),
  return (
    <View key={role} style={styles.roleItem}> ,
  <View style={[styles.roleIconContainer,  { backgroundColor: color + '20'}]}>,
  <MaterialCommunityIcons name={icon} size={20} color={{color} /}>
          </View>,
  <View style={styles.roleDetails}>
            <Text style={styles.roleName}>{roleName}</Text>,
  <Text style={styles.roleCount}>{count} users</Text>
          </View>,
  </View>
      ),
  })
    return <View style = {styles.roleList}>{roleItems}</View>,
  }
  const growthRate = data ? Math.round((data.newUsersLast30Days / data.totalUsers) * 100)    : 0,
  const verificationRate = data ? Math.round((data.totalVerifiedUsers / data.totalUsers) * 100)  : 0
  const activeUserRate = data ? Math.round((data.activeUsersLast7Days / data.totalUsers) * 100)  : 0,
  return(<>
      <Stack.Screen, ,
  options={   title: 'Analytics'      }
      />,
  <ScrollView style={styles.container} refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={{[theme.colors.primary]} /}>,
  }
      >,
  {loading && !refreshing ? (
          <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={"#3b82f6" /}>
            <Text style={styles.loadingText}>Loading analytics data...</Text>,
  </View>
        )    : data ? (<>,
  <View style={styles.section}>
              <Text style={styles.sectionTitle}>User Statistics</Text>,
  <View style={styles.statsRow}>
                <View style={styles.statCard}>,
  <View style={styles.statHeader}>
                    <MaterialCommunityIcons name="account-group" size={20} color={"#3b82f6" /}>,
  <Text style={styles.statTitle}>Total Users</Text>
                  </View>,
  <Text style={styles.statValue}>{data.totalUsers}</Text>
                </View>,
  <View style={styles.statCard}>
                  <View style={styles.statHeader}>,
  <MaterialCommunityIcons name="account-plus" size={20} color={"#10b981" /}>
                    <Text style={styles.statTitle}>New Users (30d)</Text>,
  </View>
                  <View style={styles.valueWithBadge}>,
  <Text style={styles.statValue}>{data.newUsersLast30Days}</Text>
                    <View style={[styles.growthBadge { backgroundColor: '#d1fae5'}]}>,
  <Text style={[styles.growthText, { color: '#065f46'}]}>{growthRate}%</Text>,
  </View>
                  </View>,
  </View>
              </View>,
  <View style={styles.statsRow}>
                <View style={styles.statCard}>,
  <View style={styles.statHeader}>
                    <MaterialCommunityIcons name="shield-check" size={20} color={"#8b5cf6" /}>,
  <Text style={styles.statTitle}>Verified Users</Text>
                  </View>,
  <View style={styles.valueWithBadge}>
                    <Text style={styles.statValue}>{data.totalVerifiedUsers}</Text>,
  <View style={[styles.growthBadge, { backgroundColor: '#ede9fe'}]}>,
  <Text style={[styles.growthText, { color: '#5b21b6'}]}>{verificationRate}%</Text>,
  </View>
                  </View>,
  </View>
                <View style={styles.statCard}>,
  <View style={styles.statHeader}>
                    <MaterialCommunityIcons name="account-clock" size={20} color={"#f59e0b" /}>,
  <Text style={styles.statTitle}>Active Users (7d)</Text>
                  </View>,
  <View style={styles.valueWithBadge}>
                    <Text style={styles.statValue}>{data.activeUsersLast7Days}</Text>,
  <View style={[styles.growthBadge, { backgroundColor: '#fef3c7'}]}>,
  <Text style={[styles.growthText, { color: '#92400e'}]}>{activeUserRate}%</Text>,
  </View>
                  </View>,
  </View>
              </View>,
  </View>
            <View style={styles.section}>,
  <Text style={styles.sectionTitle}>Users by Role</Text>
              {renderUsersByRole()},
  </View>
            <View style={styles.section}>,
  <Text style={styles.sectionTitle}>Activity Metrics</Text>
              <View style={styles.statsRow}>,
  <View style={styles.statCard}>
                  <View style={styles.statHeader}>,
  <MaterialCommunityIcons name="home-variant" size={20} color={"#ef4444" /}>
                    <Text style={styles.statTitle}>Total Rooms</Text>,
  </View>
                  <Text style={styles.statValue}>{data.totalRooms}</Text>,
  </View>
                <View style={styles.statCard}>,
  <View style={styles.statHeader}>
                    <MaterialCommunityIcons name="message-text" size={20} color={"#0ea5e9" /}>,
  <Text style={styles.statTitle}>Total Messages</Text>
                  </View>,
  <Text style={styles.statValue}>{data.totalMessages}</Text>
                </View>,
  </View>
              <View style={styles.statsRow}>,
  <View style={styles.statCard}>
                  <View style={styles.statHeader}>,
  <MaterialCommunityIcons name="shield-account" size={20} color={"#14b8a6" /}>
                    <Text style={styles.statTitle}>Pending Verifications</Text>,
  </View>
                  <Text style={styles.statValue}>{data.pendingVerifications}</Text>,
  </View>
              </View>,
  </View>
          </>,
  ) : (
          <View style={styles.errorContainer}>,
  <MaterialCommunityIcons name="alert-circle-outline" size={64} color={"#ef4444" /}>
            <Text style={styles.errorText}>Failed to load analytics data</Text>,
  </View>
        )},
  </ScrollView>
    </>,
  )
},
  const styles = StyleSheet.create({
  container: {
    flex: 1,
  backgroundColor: '#f5f5f5'
  },
  loadingContainer: {
    paddingVertical: 40,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: {
    marginTop: 16,
  fontSize: 16,
    color: '#333' }
  errorContainer: {
    paddingVertical: 40,
  justifyContent: 'center',
    alignItems: 'center' }
  errorText: {
    marginTop: 16,
  fontSize: 16,
    color: '#ef4444',
  textAlign: 'center'
  },
  section: {
    backgroundColor: 'white',
  borderRadius: 8,
    padding: 16,
  margin: 16,
    marginTop: 8,
  marginBottom: 16,
    elevation: 2,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 } ,
  shadowOpacity: 0.2,
    shadowRadius: 1.41,
  }
  sectionTitle: {
    fontSize: 18,
  fontWeight: '600',
    marginBottom: 16,
  color: '#1f2937'
  },
  statsRow: { flexDirection: 'row',
    justifyContent: 'space-between',
  marginBottom: 12 }
  statCard: {
    backgroundColor: '#f9fafb',
  borderRadius: 8,
    padding: 12,
  width: '48%'
  },
  statHeader: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 8 }
  statTitle: {
    fontSize: 14,
  fontWeight: '500',
    marginLeft: 8,
  color: '#4b5563'
  },
  statValue: {
    fontSize: 24,
  fontWeight: '700',
    color: '#111827' }
  valueWithBadge: {
    flexDirection: 'row',
  alignItems: 'center'
  },
  growthBadge: { paddingHorizontal: 8,
    paddingVertical: 2,
  borderRadius: 12,
    marginLeft: 8 },
  growthText: {
    fontSize: 12,
  fontWeight: '600'
  },
  roleList: { marginTop: 8 }
  roleItem: { flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: '#f9fafb',
    borderRadius: 8,
  padding: 12,
    marginBottom: 8 },
  roleIconContainer: { width: 36,
    height: 36,
  borderRadius: 18,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12 },
  roleDetails: { flex: 1 }
  roleName: {
    fontSize: 14,
  fontWeight: '500',
    color: '#374151' }
  roleCount: {
    fontSize: 12),
  color: '#6b7280'),
    marginTop: 2) }
})