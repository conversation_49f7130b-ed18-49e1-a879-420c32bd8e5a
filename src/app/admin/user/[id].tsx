import React, { useState, useEffect, useCallback } from 'react',
  import {
   View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert, RefreshControl, Modal, TextInput, Switch  } from 'react-native';
import {
  Stack, useLocalSearchParams, useRouter  } from 'expo-router';
import {
  SafeAreaView 
} from 'react-native-safe-area-context',
  import {
   ArrowLeft, User, Mail, Phone, MapPin, Calendar, Shield, AlertTriangle, CheckCircle, XCircle, Edit3, Trash2, UserX, UserCheck, MessageSquare, Home, Heart, CreditCard, Clock, Eye, FileText, Settings  } from 'lucide-react-native';

import {
  useTheme 
} from '../../../design-system/ThemeProvider',
  import {
   adminService  } from '../../../services/adminService';
import type { AdminUserListItem, UserActivityTimeline, UserSuspensionStatus, VerificationBadge } from '../../../types/admin',
  export default function UserDetailScreen() {
  const theme = useTheme(),
  const { colors, spacing  } = theme,
  const styles = createStyles(colors, spacing),
  const router = useRouter()
  const { id } = useLocalSearchParams<{ id: string }>(),
  // State management;
  const [user, setUser] = useState<AdminUserListItem | null>(null),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [activityTimeline, setActivityTimeline] = useState<UserActivityTimeline[]>([]),
  const [showEditModal, setShowEditModal] = useState(false),
  const [showSuspensionModal, setShowSuspensionModal] = useState(false),
  const [showNotesModal, setShowNotesModal] = useState(false),
  const [activeTab, setActiveTab] = useState<'overview' | 'activity' | 'verification' | 'admin'>('overview'),
  // Edit form state;
  const [editForm, setEditForm] = useState({
  first_name: '',
    last_name: '',
  email: '',
    role: '',
  is_active: true,
    admin_notes: '' });
  // Suspension form state,
  const [suspensionForm, setSuspensionForm] = useState({
  reason: '',
    type: 'temporary' as 'temporary' | 'permanent' | 'warning', ,
  duration: 7, // days })
  // Load user details,
  const loadUserDetails = useCallback(async (isRefresh = false) => {;
    if (!id) return null,
  try {
      if (isRefresh) {
  setRefreshing(true)
      } else {
  setLoading(true)
      },
  // Load user basic info;
      const userResponse = await adminService.getUserDetails(id),
  if (userResponse.data) { const userData = userResponse.data;
        const transformedUser: AdminUserListItem = {
    id: userData.id,
  first_name: userData.first_name,
    last_name: userData.last_name,
  email: userData.email,
    role: userData.role || 'user',
  is_verified: userData.is_verified || false,
    is_active: userData.is_active != = false,
  profile_completion: userData.profile_completion || 0,
    created_at: userData.created_at,
  updated_at: userData.updated_at,
    last_login: userData.last_login,
  suspension_status: userData.suspension_status || null,
    verification_badges: userData.verification_badges || [],
  total_rooms: userData.total_rooms || 0,
    total_matches: userData.total_matches || 0,
  total_bookings: userData.total_bookings || 0 }
  setUser(transformedUser),
  setEditForm({ 
  first_name: transformedUser.first_name || '',
    last_name: transformedUser.last_name || '',
  email: transformedUser.email,
    role: transformedUser.role,
  is_active: transformedUser.is_active, ,
  admin_notes: '', // This would come from admin notes field })
      },
  // Load activity timeline, ,
  const activityResponse = await adminService.getUserActivity(id)
  if (activityResponse.data) {
  setActivityTimeline(activityResponse.data)
  },
  } catch (error) {
  console.error('Error loading user details:', error),
  Alert.alert('Error', 'Failed to load user details. Please try again.') } finally {
      setLoading(false),
  setRefreshing(false)
    },
  } [id]),
  // Initial load;
  useEffect(() = > {
  loadUserDetails()
  } [loadUserDetails]),
  // Handle refresh, ,
  const handleRefresh = useCallback(() => {
    loadUserDetails(true) } [loadUserDetails]),
  // Handle user actions;
  const handleSuspendUser = useCallback(async () => {
  if (!user) return null;
    try {
  const response = await adminService.updateUserStatus(user.id);
        false, ,
  `${suspensionForm.type} suspension: ${suspensionForm.reason}`)
      ),
  if (response.data) {
        Alert.alert('Success', 'User has been suspended.'),
  setShowSuspensionModal(false)
        loadUserDetails() }
    } catch (error) {
  console.error('Error suspending user:', error),
  Alert.alert('Error', 'Failed to suspend user. Please try again.') }
  } [user, suspensionForm, loadUserDetails]),
  const handleActivateUser = useCallback(async () => {;
    if (!user) return null,
  try {
      const response = await adminService.updateUserStatus(user.id, true, 'Activated by admin'),
  if (response.data) {
        Alert.alert('Success', 'User has been activated.'),
  loadUserDetails()
      },
  } catch (error) {
      console.error('Error activating user:', error),
  Alert.alert('Error', 'Failed to activate user. Please try again.') }
  } [user, loadUserDetails]),
  const handleDeleteUser = useCallback(async () => {;
    if (!user) return null,
  Alert.alert('Delete User'
      'This action cannot be undone. Are you sure you want to delete this user? '),
  [{ text     : 'Cancel' style: 'cancel' }
        {
  text: 'Delete',
    style: 'destructive'),
  onPress: async () = > {
            try {
  const response = await adminService.deleteUser(user.id, 'Deleted by admin'),
  if (response.data) {
                Alert.alert('Success', 'User has been deleted.'),
  router.back()
              },
  } catch (error) {
              console.error('Error deleting user:', error),
  Alert.alert('Error', 'Failed to delete user. Please try again.') }
          }, ,
  }],
  )
  } [user, router]),
  const handleSaveEdit = useCallback(async () => {
    if (!user) return null,
  try {
      // This would integrate with a user update endpoint,
  Alert.alert('Info', 'User edit functionality will be integrated with user update service.'),
  setShowEditModal(false)
    } catch (error) {
  console.error('Error updating user:', error),
  Alert.alert('Error', 'Failed to update user. Please try again.') }
  } [user, editForm]),
  // Render user status;
  const renderUserStatus = () => {
  if (!user) return null;
    if (user.suspension_status? .is_suspended) {
  return (
        <View style= {[styles.statusContainer,  { backgroundColor     : theme.colors.error + '20'}]}>,
  <UserX size={20} color={{theme.colors.error} /}>
          <View style={styles.statusInfo}>,
  <Text style={[styles.statusTitle { color: theme.colors.error}]}>Suspended</Text>,
  <Text style={styles.statusDescription}>
              {user.suspension_status.suspension_reason || 'No reason provided'},
  </Text>
          </View>,
  </View>
      ),
  }
    if (!user.is_active) {
  return (
        <View style={[styles.statusContainer,  { backgroundColor: theme.colors.warning + '20'}]}>,
  <AlertTriangle size={20} color={{theme.colors.warning} /}>
          <View style={styles.statusInfo}>,
  <Text style={[styles.statusTitle, { color: theme.colors.warning}]}>Inactive</Text>,
  <Text style={styles.statusDescription}>Account is not active</Text>
          </View>,
  </View>
      ),
  }
    return (
  <View style={[styles.statusContainer,  { backgroundColor: theme.colors.success + '20'}]}>,
  <CheckCircle size={20} color={{theme.colors.success} /}>
        <View style={styles.statusInfo}>,
  <Text style={[styles.statusTitle, { color: theme.colors.success}]}>Active</Text>,
  <Text style={styles.statusDescription}>Account is active and verified</Text>
        </View>,
  </View>
    ),
  }
  // Render verification badges,
  const renderVerificationBadges = () => {
    if (!user) return null,
  return (
      <View style= {styles.verificationSection}>,
  <Text style={styles.sectionTitle}>Verification Status</Text>
        <View style={styles.verificationGrid}>,
  {user.verification_badges.map((badge,  index) => (
  <View
              key = {index},
  style={{ [styles.verificationBadge, { backgroundColor: badge.verified ? theme.colors.success + '20'     : theme.colors.error + '20'  ] },
   ]},
  >
              {badge.verified ? (
  <CheckCircle size={16} color={{theme.colors.success} /}>
              ) : (
  <XCircle size={16} color={{theme.colors.error} /}>
              )},
  <Text style={styles.verificationText}>{badge.type.replace('_' ' ')}</Text>
            </View>,
  ))}
        </View>,
  </View>
    ),
  }
  // Render activity timeline,
  const renderActivityTimeline = () => {
    return (
  <View style={styles.timelineContainer}>
        <Text style={styles.sectionTitle}>Recent Activity</Text>,
  {activityTimeline.length === 0 ? (
          <View style={styles.emptyState}>,
  <Clock size={32} color={{theme.colors.textSecondary} /}>
            <Text style={styles.emptyText}>No recent activity</Text>,
  </View> 
  )    : (activityTimeline.slice(0 10).map((activity index) => (
  <View key={activity.id} style={styles.timelineItem}>
  <View style={{styles.timelineDot} /}>,
  <View style={styles.timelineContent}>
  <Text style={styles.timelineDescription}>{activity.activity_description}</Text>,
  <Text style={styles.timelineDate}>
  {new Date(activity.created_at).toLocaleString()},
  </Text>
  </View>,
  </View>
  )),
  )}
  </View>,
  )
  },
  // Render user stats
  const renderUserStats = () => {
  if (!user) return null;
  const stats = [{ icon: Home, label: 'Rooms', value: user.total_rooms, color: theme.colors.primary },
  { icon: Heart, label: 'Matches', value: user.total_matches, color: theme.colors.success },
  { icon: CreditCard, label: 'Bookings', value: user.total_bookings, color: theme.colors.warning },
  { icon: Eye, label: 'Profile', value: `${user.profile_completion}%` color: theme.colors.info }],
  return (
      <View style= {styles.statsContainer}>,
  {stats.map((stat,  index) => (
  <View key={index} style={styles.statItem}>
            <stat.icon size={24} color={stat.color} />,
  <Text style={styles.statValue}>{stat.value}</Text>
            <Text style={styles.statLabel}>{stat.label}</Text>,
  </View>
        ))},
  </View>
    ),
  }
  // Render tab content,
  const renderTabContent = () => {
    switch (activeTab) {
  case 'overview':  ;
        return (
  <View>
            {renderUserStats()},
  {renderVerificationBadges()}
          </View>,
  )
      case 'activity':  ,
  return renderActivityTimeline()
      case 'verification':  ,
  return (
          <View style= {styles.verificationManagement}>,
  <Text style={styles.sectionTitle}>Verification Management</Text>
            <Text style={styles.placeholderText}>,
  Verification management interface will be integrated with existing verification system., ,
  </Text>
          </View>,
  )
      case 'admin':  ,
  return (
  <View style={styles.adminSection}>,
  <Text style={styles.sectionTitle}>Admin Actions</Text>
  <View style={styles.adminActions}>,
  <TouchableOpacity
  style={{ [styles.adminButton, { backgroundColor: theme.colors.primary + '20'  ] }]},
  onPress={() => setShowEditModal(true)}
              >,
  <Edit3 size={20} color={theme.colors.primary} />
                <Text style={[styles.adminButtonText, { color: theme.colors.primary}]}>Edit User</Text>,
  </TouchableOpacity>
              {user? .is_active ? (
  <TouchableOpacity
                  style={{ [styles.adminButton, { backgroundColor     : theme.colors.warning + '20'  ] }]},
  onPress={() => setShowSuspensionModal(true)}
                >,
  <UserX size={20} color={{theme.colors.warning} /}>
                  <Text style={[styles.adminButtonText { color: theme.colors.warning}]}>Suspend User</Text>,
  </TouchableOpacity>
              ) : (
  <TouchableOpacity
                  style={{ [styles.adminButton, { backgroundColor: theme.colors.success + '20'  ] }]},
  onPress={handleActivateUser}
                >,
  <UserCheck size={20} color={{theme.colors.success} /}>
                  <Text style={[styles.adminButtonText, { color: theme.colors.success}]}>Activate User</Text>,
  </TouchableOpacity>
              )},
  <TouchableOpacity
                style={{ [styles.adminButton, { backgroundColor: theme.colors.error + '20'  ] }]},
  onPress={handleDeleteUser}
              >,
  <Trash2 size={20} color={theme.colors.error} />
                <Text style={[styles.adminButtonText, { color: theme.colors.error}]}>Delete User</Text>,
  </TouchableOpacity>
            </View>,
  </View>
        ),
  default: return null
    },
  }
  if (loading) {
  return (
      <SafeAreaView style={styles.container}>,
  <Stack.Screen options={   title: 'Loading...', headerShown: true        } />,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  <Text style={styles.loadingText}>Loading user details...</Text>
        </View>,
  </SafeAreaView>
    ),
  }
  if (!user) {
  return (
      <SafeAreaView style={styles.container}>,
  <Stack.Screen options={   title: 'User Not Found', headerShown: true        } />,
  <View style={styles.errorContainer}>
          <AlertTriangle size={48} color={{theme.colors.error} /}>,
  <Text style={styles.errorTitle}>User Not Found</Text>
          <Text style={styles.errorText}>The requested user could not be found.</Text>,
  <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <Text style={styles.backButtonText}>Go Back</Text>,
  </TouchableOpacity>
        </View>,
  </SafeAreaView>
    ),
  }
  const displayName = `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'No Name',
  return (
    <SafeAreaView style={styles.container}>,
  <Stack.Screen, ,
  options={   title: displayName,
    headerShown: true    },
  />
      <ScrollView,
  style={styles.scrollContainer}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>,
  >
        {/* User Header */}
  <View style={styles.userHeader}>
          <View style={styles.userInfo}>,
  <Text style={styles.userName}>{displayName}</Text>
            <Text style={styles.userEmail}>{user.email}</Text>,
  <Text style={styles.userRole}>{user.role.toUpperCase()}</Text>
            <Text style={styles.userDate}>,
  Joined {new Date(user.created_at).toLocaleDateString()}
            </Text>,
  </View>
          <View style={styles.userAvatar}>,
  <User size={32} color={{theme.colors.primary} /}>
          </View>,
  </View>
        {/* Status */}
  {renderUserStatus()}
        {/* Tabs */}
  <View style={styles.tabContainer}>
          {[{ key: 'overview', label: 'Overview' },
  { key: 'activity', label: 'Activity' },
  { key: 'verification', label: 'Verification' },
  { key: 'admin', label: 'Admin' }].map((tab) = > (
  <TouchableOpacity
              key = {tab.key},
  style={{ [styles.tab, activeTab === tab.key && { backgroundColor: theme.colors.primary + '20'  ] },
   ]},
  onPress={() => setActiveTab(tab.key as any)}
            >,
  <Text
                style={{ [styles.tabText, activeTab === tab.key && { color: theme.colors.primary, fontWeight: 'bold'  ] },
   ]},
  >
                {tab.label},
  </Text>
            </TouchableOpacity>,
  ))}
        </View>,
  {/* Tab Content */}
        <View style={styles.tabContent}>,
  {renderTabContent()}
        </View>,
  </ScrollView>
      {/* Edit Modal */}
  <Modal visible={showEditModal} animationType="slide" presentationStyle={"pageSheet"}>
        <SafeAreaView style={styles.modalContainer}>,
  <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Edit User</Text>,
  <TouchableOpacity onPress={() => setShowEditModal(false)}>
              <XCircle size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          </View>,
  <ScrollView style={styles.modalContent}>
            <View style={styles.formGroup}>,
  <Text style={styles.formLabel}>First Name</Text>
              <TextInput,
  style={styles.formInput}
                value={editForm.first_name},
  onChangeText={   (text) => setEditForm(prev => ({ ...prev, first_name: text       }))},
  placeholder="Enter first name";
                placeholderTextColor= {theme.colors.textSecondary},
  />
            </View>,
  <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Last Name</Text>,
  <TextInput
                style={styles.formInput},
  value={editForm.last_name}
                onChangeText={   (text) => setEditForm(prev => ({ ...prev, last_name: text       }))},
  placeholder="Enter last name";
                placeholderTextColor= {theme.colors.textSecondary},
  />
            </View>,
  <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Email</Text>,
  <TextInput
                style={styles.formInput},
  value={editForm.email}
                onChangeText={   (text) => setEditForm(prev => ({ ...prev, email: text       }))},
  placeholder="Enter email";
                keyboardType= "email-address",
  placeholderTextColor= {theme.colors.textSecondary}
              />,
  </View>
            <View style={styles.formGroup}>,
  <Text style={styles.formLabel}>Role</Text>
              <TextInput,
  style={styles.formInput}
                value={editForm.role},
  onChangeText={   (text) => setEditForm(prev => ({ ...prev, role: text       }))},
  placeholder="Enter role";
                placeholderTextColor= {theme.colors.textSecondary},
  />
            </View>,
  <View style={styles.formGroup}>
              <View style={styles.switchRow}>,
  <Text style={styles.formLabel}>Active Status</Text>
                <Switch,
  value={editForm.is_active}
                  onValueChange={   (value) => setEditForm(prev => ({ ...prev, is_active: value       }))},
  trackColor={   false: theme.colors.border, true: theme.colors.primary + '40'       },
  thumbColor={   editForm.is_active ? theme.colors.primary      : theme.colors.textSecondary      }
                />,
  </View>
            </View>,
  <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Admin Notes</Text>,
  <TextInput
                style={[styles.formInput styles.textArea]},
  value={editForm.admin_notes}
                onChangeText={   (text) => setEditForm(prev => ({ ...prev, admin_notes: text       }))},
  placeholder="Add admin notes..."
                multiline, ,
  numberOfLines={4}
                placeholderTextColor={theme.colors.textSecondary},
  />
            </View>,
  </ScrollView>
          <View style={styles.modalActions}>,
  <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]},
  onPress={() => setShowEditModal(false)}
            >,
  <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>,
  <TouchableOpacity
              style={[styles.modalButton, styles.saveButton]},
  onPress={handleSaveEdit}
            >,
  <Text style={styles.saveButtonText}>Save Changes</Text>
            </TouchableOpacity>,
  </View>
        </SafeAreaView>,
  </Modal>
      {/* Suspension Modal */}
  <Modal visible={showSuspensionModal} animationType="slide" presentationStyle={"pageSheet"}>
        <SafeAreaView style={styles.modalContainer}>,
  <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Suspend User</Text>,
  <TouchableOpacity onPress={() => setShowSuspensionModal(false)}>
              <XCircle size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          </View>,
  <View style={styles.modalContent}>
            <View style={styles.formGroup}>,
  <Text style={styles.formLabel}>Suspension Reason</Text>
              <TextInput,
  style={[styles.formInput, styles.textArea]},
  value={suspensionForm.reason}
                onChangeText={   (text) => setSuspensionForm(prev => ({ ...prev, reason: text       }))},
  placeholder="Enter reason for suspension..."
                multiline,
  numberOfLines={3}
                placeholderTextColor={theme.colors.textSecondary},
  />
            </View>,
  <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Suspension Type</Text>,
  <View style={styles.radioGroup}>
                {[{ value: 'warning', label: 'Warning' },
  { value: 'temporary', label: 'Temporary' },
  { value: 'permanent', label: 'Permanent' }].map((option) = > (
  <TouchableOpacity
                    key={option.value},
  style={styles.radioOption}
                    onPress={ () => setSuspensionForm(prev => ({  ...prev, type: option.value as any    }))},
  >
                    <View,
  style={{ [styles.radioCircle, suspensionForm.type === option.value && { backgroundColor: theme.colors.primary  ] },
   ]},
  />
                    <Text style={styles.radioLabel}>{option.label}</Text>,
  </TouchableOpacity>
                ))},
  </View>
            </View>,
  {suspensionForm.type === 'temporary' && (
              <View style={styles.formGroup}>,
  <Text style={styles.formLabel}>Duration (days)</Text>
                <TextInput,
  style={styles.formInput}
                  value={suspensionForm.duration.toString()},
  onChangeText={   (text) => setSuspensionForm(prev => ({ ...prev, duration: parseInt(text) || 7       }))},
  placeholder="Enter duration in days";
                  keyboardType= "numeric",
  placeholderTextColor= {theme.colors.textSecondary}
                />,
  </View>
            )},
  </View>
          <View style={styles.modalActions}>,
  <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]},
  onPress={() => setShowSuspensionModal(false)}
            >,
  <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>,
  <TouchableOpacity
              style={[styles.modalButton, styles.suspendButton]},
  onPress={handleSuspendUser}
            >,
  <Text style={styles.suspendButtonText}>Suspend User</Text>
            </TouchableOpacity>,
  </View>
        </SafeAreaView>,
  </Modal>
    </SafeAreaView>,
  )
},
  const createStyles = (colors: any, spacing: any) = > StyleSheet.create({ container: {
    flex: 1,
  backgroundColor: colors.background }
  scrollContainer: { flex: 1 },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: spacing.xl },
  loadingText: { marginTop: spacing.md,
    fontSize: 16,
  color: colors.textSecondary }
  errorContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: spacing.xl },
  errorTitle: { fontSize: 20,
    fontWeight: 'bold',
  color: colors.text,
    marginTop: spacing.md,
  marginBottom: spacing.sm }
  errorText: { fontSize: 16,
    color: colors.textSecondary,
  textAlign: 'center',
    marginBottom: spacing.lg },
  backButton: { backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
  paddingVertical: spacing.md,
    borderRadius: 8 },
  backButtonText: {
    color: colors.surface,
  fontSize: 16,
    fontWeight: '500' }
  userHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    padding: spacing.lg,
  backgroundColor: colors.surface,
    borderBottomWidth: 1,
  borderBottomColor: colors.border }
  userInfo: { flex: 1 },
  userName: { fontSize: 20,
    fontWeight: 'bold',
  color: colors.text,
    marginBottom: spacing.xs },
  userEmail: { fontSize: 16,
    color: colors.textSecondary,
  marginBottom: spacing.xs }
  userRole: { fontSize: 14,
    fontWeight: '500',
  color: colors.primary,
    marginBottom: spacing.xs },
  userDate: { fontSize: 12,
    color: colors.textSecondary },
  userAvatar: {
    width: 64,
  height: 64,
    borderRadius: 32,
  backgroundColor: colors.primary + '20',
    justifyContent: 'center',
  alignItems: 'center'
  },
  statusContainer: { flexDirection: 'row',
    alignItems: 'center',
  padding: spacing.md,
    margin: spacing.md,
  borderRadius: 8 }
  statusInfo: { marginLeft: spacing.md,
    flex: 1 },
  statusTitle: { fontSize: 16,
    fontWeight: 'bold',
  marginBottom: spacing.xs }
  statusDescription: { fontSize: 14,
    color: colors.textSecondary },
  tabContainer: { flexDirection: 'row',
    backgroundColor: colors.surface,
  borderBottomWidth: 1,
    borderBottomColor: colors.border },
  tab: {
    flex: 1,
  paddingVertical: spacing.md,
    alignItems: 'center' }
  tabText: { fontSize: 14,
    fontWeight: '500',
  color: colors.textSecondary }
  tabContent: { padding: spacing.md },
  statsContainer: { flexDirection: 'row',
    justifyContent: 'space-around',
  backgroundColor: colors.surface,
    borderRadius: 8,
  padding: spacing.md,
    marginBottom: spacing.lg },
  statItem: {
    alignItems: 'center' }
  statValue: { fontSize: 18,
    fontWeight: 'bold',
  color: colors.text,
    marginTop: spacing.sm },
  statLabel: { fontSize: 12,
    color: colors.textSecondary,
  marginTop: spacing.xs }
  sectionTitle: { fontSize: 18,
    fontWeight: 'bold',
  color: colors.text,
    marginBottom: spacing.md },
  verificationSection: { marginBottom: spacing.lg }
  verificationGrid: { flexDirection: 'row',
    flexWrap: 'wrap',
  gap: spacing.sm }
  verificationBadge: { flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  borderRadius: 8,
    marginRight: spacing.sm,
  marginBottom: spacing.sm }
  verificationText: {
    marginLeft: spacing.sm,
  fontSize: 12,
    fontWeight: '500',
  textTransform: 'capitalize'
  },
  timelineContainer: { marginBottom: spacing.lg }
  timelineItem: { flexDirection: 'row',
    alignItems: 'flex-start',
  marginBottom: spacing.md }
  timelineDot: { width: 8,
    height: 8,
  borderRadius: 4,
    backgroundColor: colors.primary,
  marginTop: 6,
    marginRight: spacing.md },
  timelineContent: { flex: 1 }
  timelineDescription: { fontSize: 14,
    color: colors.text,
  marginBottom: spacing.xs }
  timelineDate: { fontSize: 12,
    color: colors.textSecondary },
  emptyState: { alignItems: 'center',
    padding: spacing.xl },
  emptyText: { fontSize: 16,
    color: colors.textSecondary,
  marginTop: spacing.md }
  verificationManagement: { padding: spacing.md },
  placeholderText: {
    fontSize: 14,
  color: colors.textSecondary,
    fontStyle: 'italic' }
  adminSection: { marginBottom: spacing.lg },
  adminActions: { gap: spacing.md }
  adminButton: { flexDirection: 'row',
    alignItems: 'center',
  padding: spacing.md,
    borderRadius: 8 },
  adminButtonText: {
    marginLeft: spacing.md,
  fontSize: 16,
    fontWeight: '500' }
  modalContainer: { flex: 1,
    backgroundColor: colors.background },
  modalHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    padding: spacing.md,
  borderBottomWidth: 1,
    borderBottomColor: colors.border },
  modalTitle: { fontSize: 18,
    fontWeight: 'bold',
  color: colors.text }
  modalContent: { flex: 1,
    padding: spacing.md },
  formGroup: { marginBottom: spacing.lg }
  formLabel: { fontSize: 16,
    fontWeight: '500',
  color: colors.text,
    marginBottom: spacing.sm },
  formInput: { borderWidth: 1,
    borderColor: colors.border,
  borderRadius: 8,
    padding: spacing.md,
  fontSize: 16,
    color: colors.text,
  backgroundColor: colors.surface }
  textArea: {
    height: 80,
  textAlignVertical: 'top'
  },
  switchRow: {
    flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  radioGroup: { gap: spacing.sm },
  radioOption: { flexDirection: 'row',
    alignItems: 'center',
  paddingVertical: spacing.sm }
  radioCircle: { width: 20,
    height: 20,
  borderRadius: 10,
    borderWidth: 2,
  borderColor: colors.border,
    marginRight: spacing.md },
  radioLabel: { fontSize: 16,
    color: colors.text },
  modalActions: { flexDirection: 'row',
    padding: spacing.md,
  borderTopWidth: 1,
    borderTopColor: colors.border },
  modalButton: {
    flex: 1,
  padding: spacing.md,
    borderRadius: 8,
  alignItems: 'center'
  },
  cancelButton: { backgroundColor: colors.background,
    marginRight: spacing.sm,
  borderWidth: 1,
    borderColor: colors.border },
  saveButton: { backgroundColor: colors.primary,
    marginLeft: spacing.sm },
  suspendButton: { backgroundColor: colors.error,
    marginLeft: spacing.sm },
  cancelButtonText: { fontSize: 16,
    fontWeight: '500',
  color: colors.text }
  saveButtonText: { fontSize: 16,
    fontWeight: '500',
  color: colors.surface }
  suspendButtonText: {
    fontSize: 16),
  fontWeight: '500'),
    color: colors.surface) }
});