import React, { useState, useEffect } from 'react',
  import {
   Stack, useRouter  } from 'expo-router';
import {
  ArrowLeft, Shield, AlertCircle, Check, X, User, Flag, Brain  } from 'lucide-react-native';
import {
  View
  Text,
  FlatList
  StyleSheet,
  TouchableOpacity
  ActivityIndicator,
  Alert
  RefreshControl } from 'react-native';
import {
  SafeAreaView 
} from 'react-native-safe-area-context',
  import {
   Avatar  } from '@components/common/Avatar';
import {
  supabase 
} from '@utils/supabaseUtils',
  import {
   unifiedProfileService  } from '@services/unified-profile';
import {
  sendPushNotification
  registerForPushNotifications,
  savePushToken
  getUserNotifications,
  markNotificationAsRead
  markAllNotificationsAsRead,
  deleteNotification
  getNotificationSettings,
  updateNotificationSettings
  } from '@utils/notificationUtils',
  import * as Notifications from 'expo-notifications';
  type SuspiciousProfile = { id: string,
    user_id: string,
  fraud_score: number,
    flags: Array<{
  category: string,
    severity: 'low' | 'medium' | 'high',
  description: string }>
  detected_at: string,
    status: string,
  ml_analysis?: { score: number,
    reasoning: string[],
  detected_patterns: string[],
    confidence: number },
  user: { id: string,
    first_name: string | null,
  last_name: string | null,
    avatar_url: string | null,
  created_at: string }
  },
  export default function SuspiciousProfilesScreen() {
  const router = useRouter(),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [profiles, setProfiles] = useState<SuspiciousProfile[]>([]),
  const [error, setError] = useState<string | null>(null),
  // Subscribe to suspicious profile notifications;
  useEffect(() = > {
  // Set up listener for notifications;
    const subscription = Notifications.addNotificationReceivedListener(notification => {
  // Check if this is a suspicious profile notification, ,
  if (
        notification.request.content.data? .type = == 'suspicious_profile' ||),
  notification.request.content.categoryIdentifier = == 'suspicious_profile')
      ) {
  // Refresh the list of suspicious profiles;
        loadProfiles() }
    }),
  // Set up real-time listener for new suspicious profiles;
    const profilesSubscription = supabase,
  .channel('suspicious_profiles_changes')
      .on('postgres_changes'),
  {
          event     : 'INSERT',
  schema: 'public',
    table: 'suspicious_profiles') }
        () = > {
  loadProfiles()
        },
  )
      .subscribe(),
  // Load profiles and mark notifications as read
    loadProfiles(),
  markNotificationsAsRead()
    // Cleanup,
  return () => {
      subscription.remove(),
  profilesSubscription.unsubscribe()
    },
  }; []),
  // Mark suspicious profile notifications as read;
  const markNotificationsAsRead = async () => {
  try {
      const { data: user  } = await supabase.auth.getUser(),
  if (!user || !user.user) return null;
      const { error } = await supabase,
  .from('notifications')
        .update({  is_read: true  }),
  .eq('user_id', user.user.id),
  .eq('type', 'suspicious_profile'),
  .eq('is_read', false),
  if (error) {
        console.error('Error marking notifications as read:', error) }
    } catch (err) {
  console.error('Failed to mark notifications as read:', err) }
  },
  const loadProfiles = async () => {
    try {
  setLoading(true)
      const { data, error } = await unifiedProfileService.getSuspiciousProfiles(50, 0),
  if (error) {
        throw new Error(error) }
      setProfiles(data || []),
  markNotificationsAsRead()
    } catch (err) {
  setError(err instanceof Error ? err.message      : 'Failed to load suspicious profiles')
      Alert.alert('Error', 'Failed to load suspicious profiles') } finally {
      setLoading(false),
  setRefreshing(false)
    },
  }
  const onRefresh = () => {
  setRefreshing(true)
    loadProfiles() }
  const getSeverityColor = (severity: string) => { switch (severity) {
  case 'high': return '#ef4444'
      case 'medium':  ,
  return '#f59e0b';
  case 'low':  ,
  return '#3b82f6';
  default:  ,
  return '#6b7280' }
  },
  const getStatusColor = (status: string) => { switch (status) {;
  case 'pending_review':  ,
  return '#f59e0b';
  case 'confirmed_fraud':  ,
  return '#ef4444';
  case 'false_positive':  ,
  return '#10b981';
  case 'under_monitoring':  ,
  return '#3b82f6';
  case 'resolved':  ,
  return '#6b7280';
  default:  ,
  return '#6b7280' }
  },
  const formatDate = (dateString: string) => {
  const date = new Date(dateString),
  return (
  date.toLocaleDateString() +;
      ' ' +,
  date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
  )
  },
  const handleViewProfile = (profileId: string) => {
    router.push(`/profile/${profileId}`),
  }
  const renderItem = ({ item }: { item: SuspiciousProfile }) => {
  // Get ML-detected flags;
    const mlFlags = item.flags.filter(flag => flag.category === 'ml_detection'),
  const hasMLAnalysis = !!item.ml_analysis || mlFlags.length > 0;
    return (
  <View style = {styles.profileCard}>
        <View style={styles.profileHeader}>,
  <View style={styles.profileInfo}>
            <Avatar,
  uri={item.user? .avatar_url || ''}
              size={50},
  fallback={<User size={24} color={'#6366f1' /}>
            />,
  <View style={styles.nameContainer}>
              <Text style={styles.profileName}>,
  {item.user?.first_name || ''} {item.user?.last_name || ''}
              </Text>,
  <Text style={styles.profileDate}>Created    : {formatDate(item.user?.created_at)}</Text>
            </View>,
  </View>
          <View,
  style={{ [styles.scoreBadge
              {
  backgroundColor:  
                  item.fraud_score > 70 ? '#ef4444'   : item.fraud_score > 40 ? '#f59e0b' : '#10b981'  ] }]},
  >
            <Text style={styles.scoreText}>{item.fraud_score}</Text>,
  </View>
        </View>,
  <View style={styles.statusContainer}>
          <Text style={styles.statusLabel}>Status:</Text>,
  <View style={[styles.statusBadge { backgroundColor: getStatusColor(item.status)}]}>,
  <Text style={styles.statusText}>{item.status.replace('_',  ' ')}</Text>,
  </View>
          {hasMLAnalysis && (
  <View style={styles.mlBadge}>
              <Brain size={14} color={'#6366f1' /}>,
  <Text style={styles.mlBadgeText}>AI Analyzed</Text>
            </View>,
  )}
        </View>,
  <Text style={styles.sectionTitle}>Detected Issues:</Text>
        {item.flags.map((flag, index) => (
  <View key = {index} style={styles.flagItem}>
            <View,
  style={{ [styles.severityIndicator
                { backgroundColor: getSeverityColor(flag.severity)  ] }]},
  />
            <View style={styles.flagContent}>,
  <Text style={styles.flagCategory}>
                {flag.category === 'ml_detection' ? (
  <View style={styles.mlCategoryContainer}>
                    <Brain size={12} color={'#6366f1' /}>,
  <Text style={styles.mlCategoryText}>AI Detection</Text>
                  </View>,
  )   : (flag.category.replace('_' ' ')
                )},
  </Text>
              <Text style={styles.flagDescription}>{flag.description}</Text>,
  </View>
          </View>,
  ))}
        {/* ML Analysis Section */}
  {item.ml_analysis && (
          <>,
  <Text style={[styles.sectionTitle, { marginTop: 16}]}>,
  <Brain size={16} color='#6366f1' style={{ marginRight: 8} /}>
              AI Analysis: ,
  </Text>
            <View style={styles.mlContainer}>,
  <View style={styles.mlScoreRow}>
                <View style={styles.mlMetric}>,
  <Text style={styles.mlMetricLabel}>Fraud Score:</Text>
                  <Text,
  style={{ [styles.mlMetricValue, ,
  {
                        color:  ,
  item.ml_analysis.score > 70, ? '#ef4444',
  : item.ml_analysis.score > 40
  ? '#f59e0b',
  : '#10b981'  ] }]},
  >
                    {item.ml_analysis.score}/100,
  </Text>
                </View>,
  <View style={styles.mlMetric}>
                  <Text style={styles.mlMetricLabel}>Confidence:</Text>,
  <Text style={styles.mlMetricValue}>{item.ml_analysis.confidence}%</Text>
                </View>,
  </View>
              {item.ml_analysis.reasoning && item.ml_analysis.reasoning.length > 0 && (
  <View style={styles.mlReasoningContainer}>
                  <Text style={styles.mlReasoningTitle}>Reasoning:</Text>,
  {item.ml_analysis.reasoning.map((reason, idx) => (
  <Text key={idx} style={styles.mlReasoningText}>
                      • {reason},
  </Text>
                  ))},
  </View>
              )},
  </View>
          </>,
  )}
        <View style={styles.actionButtons}>,
  <TouchableOpacity
            style={[styles.actionButton, styles.viewButton]},
  onPress={() => handleViewProfile(item.user_id)}
          >,
  <User size={16} color={'#fff' /}>
            <Text style={styles.buttonText}>View Profile</Text>,
  </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, styles.markFraudButton]}>,
  <Flag size={16} color={'#fff' /}>
            <Text style={styles.buttonText}>Mark as Fraud</Text>,
  </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, styles.clearButton]}>,
  <Check size={16} color={'#fff' /}>
            <Text style={styles.buttonText}>Clear Flag</Text>,
  </TouchableOpacity>
        </View>,
  </View>
    ),
  }
  return (
  <SafeAreaView style={styles.container}>
      <Stack.Screen, ,
  options={   {
          headerShown: true,
    title: 'Suspicious Profiles',
  headerLeft: () = > (
  <TouchableOpacity onPress = {() => router.back()      }>
              <ArrowLeft color='#000' size={{24} /}>,
  </TouchableOpacity>
          ),
  headerRight: () => (
            <TouchableOpacity onPress = {loadProfiles}>,
  <AlertCircle color='#000' size={{24} /}>
            </TouchableOpacity>,
  )
        }},
  />
      {loading && profiles.length === 0 ? (
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={'#6366f1' /}>,
  <Text style={styles.loadingText}>Loading suspicious profiles...</Text>
        </View>,
  )     : error ? (<View style={styles.errorContainer}>
          <AlertCircle size={48} color={'#ef4444' /}>,
  <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadProfiles}>,
  <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>,
  </View>
      ) : profiles.length === 0 ? (<View style={styles.noDataContainer}>,
  <Shield size={48} color={'#6b7280' /}>
          <Text style={styles.noDataText}>No suspicious profiles found</Text>,
  </View>
      ) : (<FlatList,
  data={profiles}
          renderItem={renderItem},
  keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer},
  refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{onRefresh} /}>
        />,
  )}
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({
  container: {
    flex: 1,
  backgroundColor: '#f9fafb'
  },
  header: {
    flexDirection: 'row',
  alignItems: 'center',
    paddingHorizontal: 16,
  paddingVertical: 12,
    borderBottomWidth: 1,
  borderBottomColor: '#e5e7eb',
    backgroundColor: '#fff' }
  title: {
    fontSize: 18,
  fontWeight: '600',
    marginLeft: 8,
  color: '#111827'
  },
  listContent: { padding: 16 }
  profileCard: {
    backgroundColor: '#fff',
  borderRadius: 12,
    padding: 16,
  marginBottom: 16,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 } ,
  shadowOpacity: 0.1,
    shadowRadius: 3,
  elevation: 2
  },
  profileHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 12 },
  profileInfo: {
    flexDirection: 'row',
  alignItems: 'center'
  },
  nameContainer: { marginLeft: 12 }
  profileName: {
    fontSize: 16,
  fontWeight: '600',
    color: '#111827' }
  profileDate: { fontSize: 12,
    color: '#6b7280',
  marginTop: 2 }
  scoreBadge: { paddingHorizontal: 10,
    paddingVertical: 4,
  borderRadius: 12 }
  scoreText: { color: '#fff',
    fontWeight: '700',
  fontSize: 16 }
  statusContainer: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 16 }
  statusLabel: { fontSize: 14,
    color: '#6b7280',
  marginRight: 8 }
  statusBadge: { paddingHorizontal: 8,
    paddingVertical: 2,
  borderRadius: 8 }
  statusText: {
    color: '#fff',
  fontWeight: '500',
    fontSize: 12,
  textTransform: 'capitalize'
  },
  sectionTitle: { fontSize: 15,
    fontWeight: '600',
  color: '#374151',
    marginBottom: 8 },
  flagItem: {
    flexDirection: 'row',
  marginBottom: 8,
    backgroundColor: '#f3f4f6',
  borderRadius: 8,
    overflow: 'hidden' }
  severityIndicator: { width: 4 },
  flagContent: { padding: 10 }
  flagCategory: {
    fontSize: 14,
  fontWeight: '500',
    color: '#374151',
  textTransform: 'capitalize'
  },
  flagDescription: { fontSize: 12,
    color: '#6b7280',
  marginTop: 2 }
  actionButtons: { flexDirection: 'row',
    justifyContent: 'space-between',
  marginTop: 12 }
  actionButton: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    paddingVertical: 8,
  paddingHorizontal: 12,
    borderRadius: 8,
  flex: 1,
    marginHorizontal: 4 },
  viewButton: {
    backgroundColor: '#6366f1' }
  markFraudButton: {
    backgroundColor: '#ef4444' }
  clearButton: {
    backgroundColor: '#10b981' }
  buttonText: { color: '#fff',
    fontWeight: '500',
  fontSize: 12,
    marginLeft: 4 },
  loadingContainer: {
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: {
    marginTop: 12,
  fontSize: 14,
    color: '#6b7280' }
  errorContainer: {
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  errorText: { marginTop: 12,
    fontSize: 15,
  color: '#ef4444',
    textAlign: 'center',
  marginBottom: 16 }
  retryButton: { backgroundColor: '#6366f1',
    paddingHorizontal: 16,
  paddingVertical: 8,
    borderRadius: 8 },
  retryButtonText: {
    color: '#fff',
  fontWeight: '500'
  },
  emptyContainer: {
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  emptyText: {
    marginTop: 12,
  fontSize: 15,
    color: '#6b7280',
  textAlign: 'center'
  },
  mlBadge: { flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: '#ede9fe',
    paddingHorizontal: 8,
  paddingVertical: 2,
    borderRadius: 12,
  marginLeft: 8 }
  mlBadgeText: { color: '#6366f1',
    fontSize: 12,
  fontWeight: '500',
    marginLeft: 4 },
  mlCategoryContainer: {
    flexDirection: 'row',
  alignItems: 'center'
  },
  mlCategoryText: { textTransform: 'capitalize',
    marginLeft: 4 },
  mlContainer: { backgroundColor: '#f8fafc',
    borderRadius: 8,
  padding: 12,
    marginBottom: 16 },
  mlScoreRow: { flexDirection: 'row',
    justifyContent: 'space-between',
  marginBottom: 12 }
  mlMetric: {
    flexDirection: 'row',
  alignItems: 'center'
  },
  mlMetricLabel: { fontSize: 14,
    color: '#64748b',
  marginRight: 4 }
  mlMetricValue: {
    fontSize: 16,
  fontWeight: '600',
    color: '#0f172a' }
  mlReasoningContainer: { marginTop: 8 },
  mlReasoningTitle: { fontSize: 14,
    fontWeight: '600',
  color: '#0f172a',
    marginBottom: 4 },
  mlReasoningText: { fontSize: 14,
    color: '#334155',
  marginBottom: 4,
    lineHeight: 20 },
  listContainer: { padding: 16 }
  noDataContainer: {
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  noDataText: {
    marginTop: 12,
  fontSize: 15),
    color: '#6b7280'),
  textAlign: 'center')
  },
  })