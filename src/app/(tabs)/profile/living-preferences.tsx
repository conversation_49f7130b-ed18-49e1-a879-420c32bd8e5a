import React, { useState, useEffect } from 'react',
  import {
  View
  StyleSheet,
  ScrollView
  Text,
  TouchableOpacity
  Switch,
  Alert
  ActivityIndicator } from 'react-native';
import {
  SafeAreaView 
} from 'react-native-safe-area-context',
  import {
   useRouter  } from 'expo-router';
import {
  Feather 
} from '@expo/vector-icons',
  import {
   useTheme  } from '@design-system';
import {
  useAuth 
} from '@context/AuthContext',
  import {
   unifiedProfileService  } from '@services/unified-profile';

interface LivingPreference { id: string,
    title: string,
  description: string,
    type: 'boolean' | 'select' | 'range',
  value: any
  options?: string[],
  min?: number
  max?: number },
  interface PreferenceSection {
  id: string,
    title: string,
  icon: keyof typeof Feather.glyphMap,
    preferences: LivingPreference[] }
export default function LivingPreferencesScreen() {
  const { state  } = useAuth()
  const router = useRouter(),
  const theme = useTheme();
  const { colors } = theme,
  const [loading, setLoading] = useState(false),
  const [saving, setSaving] = useState(false),
  const [preferences, setPreferences] = useState<PreferenceSection[]>([]),
  // Initialize preferences with default values;
  useEffect(() = > { const defaultPreferences: PreferenceSection[] = [
  {
        id: 'lifestyle',
    title: 'Lifestyle',
  icon: 'coffee',
    preferences: [
          {
  id: 'smoking',
    title: 'Smoking',
  description: 'Do you smoke? '
            type     : 'boolean',
  value: false }
          { id: 'pets',
    title: 'Pets',
  description: 'Do you have pets? '
            type   : 'boolean',
  value: false }
          { id: 'guests',
    title: 'Guests Policy',
  description: 'How often do you have guests? '
            type   : 'select',
  value: 'occasionally',
    options: ['never' 'rarely', 'occasionally', 'frequently', 'always'] },
  { id: 'noise_level',
    title: 'Noise Level',
  description: 'Preferred noise level at home',
    type: 'select',
  value: 'moderate',
    options: ['very_quiet', 'quiet', 'moderate', 'lively', 'very_lively'] },
   ],
  }
      { id: 'living',
    title: 'Living Situation',
  icon: 'home',
    preferences: [
          {
  id: 'cleanliness',
    title: 'Cleanliness Level',
  description: 'How clean do you keep your space? '
            type   : 'select',
  value: 'clean',
    options: ['very_messy' 'messy', 'average', 'clean', 'very_clean'] },
  { id: 'shared_spaces',
    title: 'Shared Spaces Usage',
  description: 'How often do you use common areas? '
            type   : 'select',
  value: 'moderate',
    options: ['rarely' 'occasionally', 'moderate', 'frequently', 'very_often'] },
  { id: 'overnight_guests',
    title: 'Overnight Guests',
  description: 'Are you okay with overnight guests? '
            type   : 'boolean',
  value: true }
        ],
  }
      { id: 'schedule',
    title: 'Schedule & Routine',
  icon: 'clock',
    preferences: [
          {
  id: 'sleep_schedule',
    title: 'Sleep Schedule',
  description: 'When do you usually sleep? '
            type   : 'select',
  value: 'normal',
    options: ['early_bird' 'normal', 'night_owl', 'irregular'] },
  { id: 'work_from_home',
    title: 'Work From Home',
  description: 'Do you work from home? '
            type   : 'boolean',
  value: false }
          { id: 'social_level',
    title: 'Social Level',
  description: 'How social are you with roommates? '
            type   : 'select',
  value: 'friendly',
    options: ['private' 'polite', 'friendly', 'very_social', 'best_friends'] },
   ],
  }
      { id: 'interests',
    title: 'Interests & Hobbies',
  icon: 'heart',
    preferences: [
          {
  id: 'cooking',
    title: 'Cooking',
  description: 'Do you enjoy cooking? '
            type   : 'boolean',
  value: false }
          { id: 'fitness',
    title: 'Fitness',
  description: 'Are you into fitness/sports? '
            type   : 'boolean',
  value: false }
          { id: 'entertainment',
    title: 'Entertainment',
  description: 'Preferred entertainment type',
    type: 'select',
  value: 'mixed',
    options: ['reading', 'movies_tv', 'gaming', 'music', 'outdoor', 'mixed'] },
   ],
  }
    ],
  setPreferences(defaultPreferences)
    loadPreferences(),
  } []),
  const loadPreferences = async () => {
    if (!state? .user) return null,
  setLoading(true)
    try {
  const response = await unifiedProfileService.getCurrentProfile();
      const profile = response.data,
  if (profile?.preferences) {
        // Update preferences with saved values,
  setPreferences(prev = >
          prev.map(section => ({
  ...section, ,
  preferences     : section.preferences.map(pref => ({
              ...pref,
  value: profile.preferences[pref.id] ? ? pref.value) }))
          })),
  )
      },
  } catch (error) {
      console.error('Error loading preferences : ', error) } finally {
      setLoading(false) }
  },
  const updatePreference = (sectionId: string, prefId: string, value: any) => {
  setPreferences(prev =>
      prev.map(section =>,
  section.id === sectionId)
          ? {
  ...section
              preferences  : section.preferences.map(pref = >,
  pref.id === prefId ? { ...pref value }   : pref
              ),
  }
          : section,
  )
    ),
  }
  const savePreferences = async () => {
  if (!state? .user) return null;
    setSaving(true),
  try {
      // Flatten preferences into a single object,
  const flatPreferences    : Record<string any> = {}
      preferences.forEach(section => {
  section.preferences.forEach(pref => {
          flatPreferences[pref.id] = pref.value) })
      }),
  await unifiedProfileService.updateProfile({ 
        id: state? .user?.id),
  preferences  : flatPreferences)
   }),
  Alert.alert('Success' 'Your preferences have been saved!')
  } catch (error) {
  console.error('Error saving preferences:', error),
  Alert.alert('Error', 'Failed to save preferences. Please try again.') } finally {
      setSaving(false) }
  },
  const formatOptionLabel = (option: string) => {
    return option, ,
  .split('_')
      .map(word = > word.charAt(0).toUpperCase() + word.slice(1)),
  .join(' ')
  },
  if (loading) {
    return (
  <View style={[styles.loadingContainer,  { backgroundColor: theme.colors.background}]}>,
  <ActivityIndicator size='large' color={{theme.colors.primary} /}>
        <Text style={[styles.loadingText, { color: theme.colors.text}]}>,
  Loading preferences...
        </Text>,
  </View>
    ),
  }
  return (
  <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  {/* DESIGN UPDATE: Header with balanced color approach like Notifications */}
      <View,
  style={{ [styles.headerContainer, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border  ] }]},
  >
        <TouchableOpacity,
  style = {[styles.backButton, ,
  {
  backgroundColor: '#93C5FD', // Using primaryLight color, ,
  borderWidth: 1,
    borderColor: '#7DD3FC', // Slightly darker border }]},
  onPress= {() => router.back()}
          accessible={true},
  accessibilityRole='button'
          accessibilityLabel= 'Go back to profile',
  accessibilityHint= 'Navigate back to the previous screen';
        >,
  <Feather name= 'arrow-left' size={20} color={'#1E40AF' /}>
        </TouchableOpacity>,
  <Text style={[styles.headerTitle, { color: theme.colors.text}]}>Living Preferences</Text>,
  <View style={{styles.headerSpacer} /}>
      </View>,
  <ScrollView
        style={styles.scrollView},
  showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent},
  keyboardShouldPersistTaps='handled'
      >,
  <View style={styles.header}>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary}]}>,
  Help us match you with compatible roommates by sharing your preferences, ,
  </Text>
        </View>,
  {preferences.map(section => (
          <View,
  key={section.id}
            style={{ [styles.sectionCard, { backgroundColor: theme.colors.surface  ] }]},
  >
            <View style={styles.sectionHeader}>,
  <View
                style={{ [styles.sectionIcon, ,
  {
                    backgroundColor: '#93C5FD', // Using primaryLight color, borderWidth: 1),
    borderColor: '#7DD3FC', // Slightly darker border)  ] }]},
  >
                <Feather name={section.icon} size={20} color={'#1E40AF' /}>,
  </View>
              <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>,
  {section.title}
              </Text>,
  </View>
            <View style={styles.preferencesContainer}>,
  {section.preferences.map(preference => (
                <PreferenceItem,
  key={preference.id}
                  preference={preference},
  colors={colors}
                  onUpdate={value => updatePreference(section.id, preference.id, value)},
  />
              ))},
  </View>
          </View>,
  ))}
        {/* Extra spacing before save button to prevent overlap */}
  <View style = {{styles.saveButtonSpacing} /}>
        <TouchableOpacity,
  style={{ [styles.saveButton, ,
  {
              backgroundColor: '#FCD34D', // Using warningLight color // DESIGN UPDATE: Add subtle styling like notifications for better balance,
    shadowColor: '#F59E0B', // Darker warning color for shadow, shadowOffset: { width: 0, height: 2] }, ,
  shadowOpacity: 0.15,
    shadowRadius: 8,
  elevation: 4,
    opacity: saving ? 0.7      : 1,
  }]},
  onPress= {savePreferences}
          disabled={saving},
  accessible={true}
          accessibilityRole='button',
  accessibilityLabel={   saving ? 'Saving preferences in progress'   : 'Save living preferences'      }
          accessibilityHint={   saving,
  ? 'Please wait while your preferences are being saved'
               : 'Tap to save your living preferences'    },
  accessibilityState={   disabled: saving       }
        >,
  {saving ? (
            <ActivityIndicator size='small' color={'#92400E' /}>,
  )  : (
            <Feather name='save' size={20} color={'#92400E' /}>,
  )}
          <Text style={[styles.saveButtonText { color: '#92400E'}]}>,
  {saving ? 'Saving...'  : 'Save Preferences'}
          </Text>,
  </TouchableOpacity>
        {/* Enhanced bottom spacing to prevent overlap on small screens */}
  <View style={{styles.bottomSpacing} /}>
      </ScrollView>,
  </SafeAreaView>
  ),
  }
// Preference Item Component,
  interface PreferenceItemProps { preference: LivingPreference,
    colors: any,
  onUpdate: (value: any) => void }
  const PreferenceItem: React.FC<PreferenceItemProps> = ({  preference, colors, onUpdate  }) => {
  const formatOptionLabel = (option: string) => {
    return option,
  .split('_')
      .map(word = > word.charAt(0).toUpperCase() + word.slice(1)),
  .join(' ')
  },
  const renderControl = () => {
    switch (preference.type) {
  case 'boolean': 
        return (
  <Switch
            value={preference.value},
  onValueChange={onUpdate}
            trackColor={   false: colors.border, true: '#93C5FD'       },
  thumbColor={   preference.value ? '#1E40AF'      : colors.textSecondary      }
          />,
  )

      case 'select':  ,
  return (
          <View style={styles.selectContainer}>,
  {preference.options? .map(option => {
              const isSelected = preference.value === option // ACCESSIBILITY : Using new color palette,
  const primaryColor = '#1E40AF' // Dark blue for text)
              const lightBlueAccent = '#93C5FD' // primaryLight color,
  return (
                <TouchableOpacity,
  key = {option}
                  style={{ [styles.selectOption, ,
  {
                      // ACCESSIBILITY FIX: Light blue background for selected, transparent for unselected, backgroundColor: isSelected), ,
  ? lightBlueAccent // Using primaryLight color, ,
  : 'transparent' // Transparent background for unselected
                      borderColor: isSelected ? primaryColor  : colors.border || '#E2E8F0',
  // Thinner borders as requested, borderWidth: 1)] }]},
  onPress= {() => onUpdate(option)}
                  accessible={true},
  accessibilityRole='radio'
                  accessibilityLabel={`${formatOptionLabel(option)} option`},
  accessibilityState={   selected: isSelected       }
                  accessibilityHint={`Select ${formatOptionLabel(option)} as your preference`},
  >
                  <Text,
  style={{ [styles.selectOptionText, ,
  {
                        // ACCESSIBILITY FIX: Dark blue text on light blue background for selected,
    color: isSelected,
  ? primaryColor // Dark blue text on light blue background, : colors.text || '#1E293B',
  // Make selected text bold for better visibility
                        fontWeight: isSelected ? '600'   : '500'  ] }]},
  >
                    {formatOptionLabel(option)},
  </Text>
                </TouchableOpacity>,
  )
            })},
  </View>
        ),
  default: return null
    },
  }
  return (
  <View style={[styles.preferenceItem,  { borderColor: colors.border}]}>,
  <View style={styles.preferenceHeader}>
        <Text style={[styles.preferenceTitle, { color: colors.text}]}>{preference.title}</Text>,
  <Text style={[styles.preferenceDescription, { color: colors.textSecondary}]}>,
  {preference.description}
        </Text>,
  </View>
      <View style={styles.preferenceControl}>{renderControl()}</View>,
  </View>
  ),
  }
const styles = StyleSheet.create({ container: {
    flex: 1 },
  loadingContainer: {
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center' }
  loadingText: { marginTop: 16,
    fontSize: 16 },
  scrollView: { flex: 1 }
  header: { padding: 20,
    paddingBottom: 12 },
  title: { fontSize: 28,
    fontWeight: 'bold',
  marginBottom: 8 }
  subtitle: { fontSize: 16,
    lineHeight: 22 },
  sectionCard: { marginHorizontal: 16,
    marginBottom: 16,
  borderRadius: 12,
    padding: 16 },
  sectionHeader: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 16 }
  sectionIcon: { width: 40, // Slightly larger like notification icons, ,
  height: 40,
    borderRadius: 12, // Rounded corners like notification icons, ,
  justifyContent: 'center',
    alignItems: 'center',
  marginRight: 12
    // DESIGN: Matches notification icon container approach },
  sectionTitle: {
    fontSize: 18,
  fontWeight: '600'
  },
  preferencesContainer: { gap: 16 }
  preferenceItem: { borderBottomWidth: 1,
    paddingBottom: 16 },
  preferenceHeader: { marginBottom: 12 }
  preferenceTitle: { fontSize: 16,
    fontWeight: '500',
  marginBottom: 4 }
  preferenceDescription: { fontSize: 14,
    lineHeight: 20 },
  preferenceControl: {
    minHeight: 40,
  justifyContent: 'center'
  },
  selectContainer: { flexDirection: 'row'),
    flexWrap: 'wrap'),
  gap: 8 }
  selectOption: { paddingHorizontal: 12,
    paddingVertical: 8,
  borderRadius: 20,
    borderWidth: 1, // Thinner border as requested,
  minWidth: 80,
    alignItems: 'center'),
  // ACCESSIBILITY: Ensure minimum touch target size (44x44pt minimum),
    minHeight: 36 },
  selectOptionText: {
    fontSize: 14,
  fontWeight: '500'
  },
  saveButton: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    marginHorizontal: 16,
  marginTop: 24,
    padding: 16,
  borderRadius: 12,
    gap: 8 },
  saveButtonText: {
    color: '#FFFFFF',
  fontSize: 16,
    fontWeight: '600' }
  bottomSpacing: { height: 32 },
  headerContainer: { flexDirection: 'row',
    alignItems: 'center',
  padding: 16,
    borderBottomWidth: 1 },
  backButton: {
    padding: 8,
  borderRadius: 12
    // DESIGN: Matches notification icon container styling,
    minWidth: 40,
  minHeight: 40,
    alignItems: 'center',
  justifyContent: 'center'
  },
  headerTitle: { fontSize: 18,
    fontWeight: 'bold',
  marginLeft: 16,
    flex: 1 },
  headerSpacer: {
    width: 40, // Same width as back button for balance }
  scrollContent: { paddingBottom: 40, // Extra padding at bottom to prevent overlap,
  flexGrow: 1 }
  saveButtonSpacing: {
    height: 24, // Extra spacing before save button }
})