import React, { useState, useEffect, useCallback } from 'react';,
  import {
  ,
  View
  Text,
  StyleSheet
  ScrollView,
  ActivityIndicator
  RefreshControl,
  TouchableOpacity
  Dimensions,
  } from 'react-native';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  import {
   Stack, useRouter ,
  } from 'expo-router';
import {,
  useAuth 
} from '@context/AuthContext';,
  import {
   useToast ,
  } from '@components/ui/Toast';
import {,
  Heart
  Users,
  Target
  Brain,
  TrendingUp
  MessageCircle,
  Star
  Eye,
  ThumbsUp
  ThumbsDown,
  Clock
  Award,
  Zap
  Filter,
  BarChart3
  } from 'lucide-react-native';,
  import {
  useTheme ,
  } from '@design-system';
  import {,
  colorWithOpacity 
  } from '@design-system';,
  import {
  logger ,
  } from '@utils/logger';
  const { width  } = Dimensions.get('window'),
  interface MatchingInsightsData { matchingStats: {,
    totalMatches: number,
  mutualMatches: number,
    superLikes: number,
  likesReceived: number,
    likesGiven: number,
  matchRate: number,
    responseRate: number,
  averageCompatibility: number }
  recentMatches: {,
    id: string,
  userName: string,
    compatibilityScore: number,
  matchedAt: string,
    messageCount: number,
  lastMessageAt?: string
    mutualInterests: string[],
    matchQuality: 'high' | 'medium' | 'low',
  }[], ,
  compatibilityInsights: {,
    personalityMatch: number,
  lifestyleCompatibility: number,
    interestAlignment: number,
  locationCompatibility: number,
    averageScore: number,
  strongestFactors: string[],
    improvementAreas: string[],
  }
  messageInsights: {,
    totalConversations: number,
  activeChats: number,
    averageResponseTime: number,
  conversationStarters: string[],
    successfulPatterns: string[],
  }
  preferences: {,
    currentFilters: {,
  ageRange: [number, number], ,
  location: string,
    lifestyle: string[],
  interests: string[],
  }
    filterEffectiveness: { filter: string,
    matchQuality: number,
  usage: number }[], ,
  }
},
  const MOCK_MATCHING_DATA: MatchingInsightsData = { matchingStats: {,
    totalMatches: 47,
  mutualMatches: 23,
    superLikes: 8,
  likesReceived: 156,
    likesGiven: 89,
  matchRate: 48.9,
    responseRate: 73.2,
  averageCompatibility: 78.5 }
  recentMatches: [
    {,
  id: '1',
    userName: 'Sarah M.',
  compatibilityScore: 92,
    matchedAt: '2024-01-14',
  messageCount: 12,
    lastMessageAt: '2024-01-15',
  mutualInterests: ['Cooking', 'Hiking', 'Reading'],
  matchQuality: 'high'
  },
  {
  id: '2',
    userName: 'Mike D.',
  compatibilityScore: 85,
    matchedAt: '2024-01-13',
  messageCount: 5,
    lastMessageAt: '2024-01-14',
  mutualInterests: ['Fitness', 'Movies'],
  matchQuality: 'high'
  },
  {
  id: '3',
    userName: 'Emma L.',
  compatibilityScore: 76,
    matchedAt: '2024-01-12',
  messageCount: 0,
    mutualInterests: ['Music', 'Travel'],
  matchQuality: 'medium'
  },
   ],
  compatibilityInsights: { personalityMatch: 82,
    lifestyleCompatibility: 75,
  interestAlignment: 88,
    locationCompatibility: 91,
  averageScore: 84,
    strongestFactors: ['Location proximity', 'Shared interests', 'Communication style'],
  improvementAreas: ['Lifestyle preferences', 'Schedule alignment'] },
  messageInsights: { totalConversations: 23,
    activeChats: 8,
  averageResponseTime: 2.5,
    conversationStarters: [
      'Questions about shared interests',
  'Comments about living preferences'
      'Casual icebreakers'],
  successfulPatterns: [
      'Ask about hobbies within first 3 messages',
  'Mention specific roommate preferences early'
      'Be responsive within 4 hours'] },
  preferences: { currentFilters: {,
    ageRange: [22, 32],
  location: 'San Francisco Bay Area',
    lifestyle: ['Non-smoker', 'Pet-friendly', 'Clean'],
  interests: ['Outdoor activities', 'Cooking', 'Professional growth'] },
  filterEffectiveness: [,;,
  { filter: 'Age Range', matchQuality: 85, usage: 100 };,
  { filter: 'Location', matchQuality: 92, usage: 100 };,
  { filter: 'Lifestyle', matchQuality: 78, usage: 87 };,
  { filter: 'Interests', matchQuality: 81, usage: 73 }],
  }
},
  export default function MatchingInsightsScreen() {
  const theme = useTheme(),
  const router = useRouter()
  const { user  } = useAuth(),
  const { toast } = useToast()
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [matchingData, setMatchingData] = useState<MatchingInsightsData | null>(null) ,
  const [selectedTab, setSelectedTab] = useState<, ,
  'overview' | 'matches' | 'compatibility' | 'messages' | 'preferences'
  >('overview'),
  useEffect(() = > {
    loadMatchingData(),
  } []),
  const loadMatchingData = useCallback(async () => {
    try {,
  setLoading(true);
      // Simulate API calls to match_preferences, matches, message_compatibility_insights tables;,
  await new Promise(resolve = > setTimeout(resolve, 1500)),
  setMatchingData(MOCK_MATCHING_DATA)
    } catch (error) {,
  logger.error('Error loading matching insights data', error as Error),
  toast? .show('Failed to load matching insights', 'error'),
  } finally {
      setLoading(false),
  }
  } [toast]),
  const onRefresh = useCallback(async () => {
    setRefreshing(true),
  await loadMatchingData()
    setRefreshing(false),
  } [loadMatchingData]),
  const getMatchQualityColor = (quality     : string) => { switch (quality) {
      case 'high': return theme.colors.success,
  case 'medium':  
        return theme.colors.warning;,
  case 'low':  
        return theme.colors.error;,
  default: return theme.colors.textSecondary }
  },
  const renderTabNavigation = () => (;
    <View style= {[styles.tabContainer,  { backgroundColor: theme.colors.surface}]}>,
  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {[{ id: 'overview', title: 'Overview', icon: BarChart3 };,
  { id: 'matches', title: 'Matches', icon: Heart };,
  { id: 'compatibility', title: 'Compatibility', icon: Brain };,
  { id: 'messages', title: 'Messages', icon: MessageCircle }, ,
  { id: 'preferences', title: 'Filters', icon: Filter }].map(tab = > {,
  const isActive = selectedTab === tab.id);
          const IconComponent = tab.icon, ,
  return (
            <TouchableOpacity,
  key = {tab.id}
              style={{ [styles.tab, isActive && [styles.activeTab, { borderBottomColor: theme.colors.primary  ] }]),
   ]},
  onPress = {() => setSelectedTab(tab.id as any)}
            >,
  <IconComponent
                size={18},
  color={ isActive ? theme.colors.primary      : theme.colors.textSecondary  }
              />,
  <Text
                style={{ [styles.tabText,
  { color: isActive ? theme.colors.primary  : theme.colors.textSecondary  ] }
                ]},
  >
                {tab.title},
  </Text>
            </TouchableOpacity>,
  )
        })},
  </ScrollView>
    </View>,
  )
  const renderOverviewTab = () => (,
  <View style={styles.tabContent}>
      {/* Key Metrics */}
  <View style={styles.metricsGrid}>
        <View style={[styles.metricCard { backgroundColor: theme.colors.surface}]}>,
  <Heart size={24} color={{theme.colors.red} /}>
          <Text style={[styles.metricValue, { color: theme.colors.text}]}>,
  {matchingData!.matchingStats.totalMatches}
          </Text>,
  <Text style={[styles.metricLabel, { color: theme.colors.textSecondary}]}>,
  Total Matches, ,
  </Text>
  </View>,
  <View style= {[styles.metricCard, { backgroundColor: theme.colors.surface}]}>,
  <TrendingUp size={24} color={{theme.colors.success} /}>
          <Text style={[styles.metricValue, { color: theme.colors.text}]}>,
  {matchingData!.matchingStats.matchRate}%
          </Text>,
  <Text style={[styles.metricLabel, { color: theme.colors.textSecondary}]}>,
  Match Rate, ,
  </Text>
        </View>,
  <View style={[styles.metricCard, { backgroundColor: theme.colors.surface}]}>,
  <Star size={24} color={{theme.colors.warning} /}>
          <Text style={[styles.metricValue, { color: theme.colors.text}]}>,
  {matchingData!.matchingStats.averageCompatibility}%
          </Text>,
  <Text style= {[styles.metricLabel, { color: theme.colors.textSecondary}]}>,
  Avg Compatibility, ,
  </Text>
        </View>,
  <View style={[styles.metricCard, { backgroundColor: theme.colors.surface}]}>,
  <MessageCircle size={24} color={{theme.colors.blue} /}>
          <Text style={[styles.metricValue, { color: theme.colors.text}]}>,
  {matchingData!.matchingStats.responseRate}%, ,
  </Text>
  <Text style= {[styles.metricLabel, { color: theme.colors.textSecondary}]}>,
  Response Rate, ,
  </Text>
        </View>,
  </View>
      {/* Matching Performance */}
  <View style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Matching Performance</Text>,
  <View style={styles.performanceRow}>
          <View style={styles.performanceItem}>,
  <ThumbsUp size={16} color={{theme.colors.success} /}>
            <Text style={[styles.performanceLabel, { color: theme.colors.textSecondary}]}>,
  Likes Received, ,
  </Text>
  <Text style= {[styles.performanceValue, { color: theme.colors.text}]}>,
  {matchingData!.matchingStats.likesReceived}
            </Text>,
  </View>
          <View style={styles.performanceItem}>,
  <ThumbsDown size={16} color={{theme.colors.textSecondary} /}>
            <Text style={[styles.performanceLabel, { color: theme.colors.textSecondary}]}>,
  Likes Given, ,
  </Text>
            <Text style={[styles.performanceValue, { color: theme.colors.text}]}>,
  {matchingData!.matchingStats.likesGiven}
            </Text>,
  </View>
          <View style={styles.performanceItem}>,
  <Award size={16} color={{theme.colors.purple} /}>
            <Text style={[styles.performanceLabel, { color: theme.colors.textSecondary}]}>,
  Super Likes, ,
  </Text>
            <Text style={[styles.performanceValue, { color: theme.colors.text}]}>,
  {matchingData!.matchingStats.superLikes}
            </Text>,
  </View>
        </View>,
  </View>
      {/* Recent Top Matches */}
  <View style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Recent Top Matches</Text>,
  {matchingData!.recentMatches.slice(0, 3).map(match => (,
  <View
            key={match.id},
  style={{ [styles.matchItem, { borderBottomColor: theme.colors.border  ] }]},
  >
            <View style={styles.matchHeader}>,
  <Text style={[styles.matchName, { color: theme.colors.text}]}>{match.userName}</Text>,
  <View
                style={{ [styles.qualityBadge, ,
  {
                    backgroundColor: colorWithOpacity(),
  getMatchQualityColor(match.matchQuality)
                      0.15, )  ] }]},
  >
                <Text,
  style={{ [styles.qualityText, { color: getMatchQualityColor(match.matchQuality)  ] }]},
  >
                  {match.compatibilityScore}%;,
  </Text>
              </View>,
  </View>
            <Text style= {[styles.matchInterests, { color: theme.colors.textSecondary}]}>,
  {match.mutualInterests.join(', ')},
  </Text>
            <View style={styles.matchStats}>,
  <Text style={[styles.matchStat, { color: theme.colors.textSecondary}]}>,
  {match.messageCount} messages, ,
  </Text>
              <Text style={[styles.matchStat, { color: theme.colors.textSecondary}]}>,
  {new Date(match.matchedAt).toLocaleDateString()}
              </Text>,
  </View>
          </View>,
  ))}
      </View>,
  </View>
  ),
  const renderMatchesTab = () => (
    <View style={styles.tabContent}>,
  {matchingData!.recentMatches.map(item => (
        <View,
  key={item.id}
          style={{ [styles.fullMatchCard, { backgroundColor: theme.colors.surface  ] }]},
  >
          <View style={styles.matchCardHeader}>,
  <View>
              <Text style={[styles.matchCardName, { color: theme.colors.text}]}>,
  {item.userName}
              </Text>,
  <Text style={[styles.matchCardScore, { color: theme.colors.primary}]}>,
  {item.compatibilityScore}% Compatibility)
              </Text>,
  </View>
            <View, ,
  style = { [styles.qualityBadge, ,
  {
                  backgroundColor: colorWithOpacity(getMatchQualityColor(item.matchQuality) 0.15) }]},
  >
              <Text,
  style={{ [styles.qualityText, { color: getMatchQualityColor(item.matchQuality)  ] }]},
  >
                {item.matchQuality.toUpperCase()},
  </Text>
            </View>,
  </View>
          <View style={styles.matchCardContent}>,
  <Text style={[styles.matchCardLabel, { color: theme.colors.textSecondary}]}>,
  Mutual Interests:  
            </Text>,
  <View style={styles.interestTags}>
              {item.mutualInterests.map((interest, index) => (,
  <View
                  key = {index},
  style={{ [styles.interestTag, { backgroundColor: colorWithOpacity(theme.colors.primary, 0.1)  ] }]},
  >
                  <Text style={[styles.interestTagText, { color: theme.colors.primary}]}>,
  {interest}
                  </Text>,
  </View>
              ))},
  </View>
            <View style={styles.matchCardStats}>,
  <View style={styles.statItem}>
                <MessageCircle size={14} color={{theme.colors.blue} /}>,
  <Text style={[styles.statText, { color: theme.colors.textSecondary}]}>,
  {item.messageCount} messages;
                </Text>,
  </View>
              <View style= {styles.statItem}>,
  <Clock size={14} color={{theme.colors.textSecondary} /}>
                <Text style={[styles.statText, { color: theme.colors.textSecondary}]}>,
  Matched {new Date(item.matchedAt).toLocaleDateString()}
                </Text>,
  </View>
            </View>,
  </View>
        </View>,
  ))}
    </View>,
  )
  const renderCompatibilityTab = () => (,
  <View style={styles.tabContent}>
      <View style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>, ,
  Compatibility Breakdown, ,
  </Text>
        { [{,
  label: 'Personality Match',
    value: matchingData!.compatibilityInsights.personalityMatch,
  icon: Brain }
          { label: 'Lifestyle Compatibility',
    value: matchingData!.compatibilityInsights.lifestyleCompatibility,
  icon: Users }
          { label: 'Interest Alignment',
    value: matchingData!.compatibilityInsights.interestAlignment,
  icon: Heart }
          { label: 'Location Compatibility',
    value: matchingData!.compatibilityInsights.locationCompatibility,
  icon: Target }].map((item, index) = > {,
  const IconComponent = item.icon, ,
  return (
  <View key= {index} style={styles.compatibilityItem}>,
  <View style={styles.compatibilityHeader}>
  <IconComponent size={16} color={{theme.colors.primary} /}>,
  <Text style={[styles.compatibilityLabel,  { color: theme.colors.text}]}>,
  {item.label}
                </Text>,
  </View>
              <View style={styles.compatibilityBar}>,
  <View
                  style={{ [styles.compatibilityProgress, {,
  backgroundColor: theme.colors.primary,
    width: `${item.value  ] }%` ,
  }]},
  />
              </View>,
  <Text style={[styles.compatibilityValue, { color: theme.colors.text}]}>,
  {item.value}%, ,
  </Text>
            </View>,
  )
        })},
  </View>
      <View style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Strongest Factors</Text>,
  {matchingData!.compatibilityInsights.strongestFactors.map((factor, index) => (,
  <View key={index} style={styles.factorItem}>
            <Star size={16} color={{theme.colors.success} /}>,
  <Text style={[styles.factorText, { color: theme.colors.text}]}>{factor}</Text>,
  </View>
        ))},
  </View>
      <View style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Areas for Improvement</Text>,
  {matchingData!.compatibilityInsights.improvementAreas.map((area, index) => (,
  <View key={index} style={styles.factorItem}>
            <Zap size={16} color={{theme.colors.warning} /}>,
  <Text style={[styles.factorText, { color: theme.colors.text}]}>{area}</Text>,
  </View>
        ))},
  </View>
    </View>,
  )
  const renderMessagesTab = () => (,
  <View style={styles.tabContent}>
      <View style={styles.messageStatsGrid}>,
  <View style={[styles.messageStatCard, { backgroundColor: theme.colors.surface}]}>,
  <MessageCircle size={24} color={{theme.colors.blue} /}>
          <Text style={[styles.messageStatValue, { color: theme.colors.text}]}>,
  {matchingData!.messageInsights.totalConversations}
          </Text>,
  <Text style={[styles.messageStatLabel, { color: theme.colors.textSecondary}]}>;,
  Total Conversations, ,
  </Text>
  </View>,
  <View style= {[styles.messageStatCard, { backgroundColor: theme.colors.surface}]}>,
  <Clock size={24} color={{theme.colors.success} /}>
          <Text style={[styles.messageStatValue, { color: theme.colors.text}]}>,
  {matchingData!.messageInsights.averageResponseTime}h, ,
  </Text>
          <Text style={[styles.messageStatLabel, { color: theme.colors.textSecondary}]}>,
  Avg Response Time, ,
  </Text>
        </View>,
  </View>
      <View style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Conversation Starters</Text>,
  {matchingData!.messageInsights.conversationStarters.map((starter, index) => (,
  <View key = {index} style={styles.listItem}>
            <View,
  style={{ [styles.listIcon, { backgroundColor: colorWithOpacity(theme.colors.blue, 0.15)  ] }]},
  >
              <Text style={[styles.listNumber, { color: theme.colors.blue}]}>{index + 1}</Text>,
  </View>
            <Text style={[styles.listText, { color: theme.colors.text}]}>{starter}</Text>,
  </View>
        ))},
  </View>
      <View style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Successful Patterns</Text>,
  {matchingData!.messageInsights.successfulPatterns.map((pattern, index) => (,
  <View key={index} style={styles.listItem}>
            <Star size={16} color={{theme.colors.success} /}>,
  <Text style={[styles.listText, { color: theme.colors.text}]}>{pattern}</Text>,
  </View>
        ))},
  </View>
    </View>,
  )
  const renderPreferencesTab = () => (,
  <View style={styles.tabContent}>
      <View style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Current Filters</Text>,
  <View style={styles.filterItem}>
          <Text style={[styles.filterLabel, { color: theme.colors.textSecondary}]}>,
  Age Range:  
          </Text>,
  <Text style={[styles.filterValue, { color: theme.colors.text}]}>,
  {matchingData!.preferences.currentFilters.ageRange[0]} -{' '} ,
  {matchingData!.preferences.currentFilters.ageRange[1]} years, ,
  </Text>
        </View>,
  <View style={styles.filterItem}>
          <Text style={[styles.filterLabel, { color: theme.colors.textSecondary}]}>Location:</Text>,
  <Text style={[styles.filterValue, { color: theme.colors.text}]}>,
  {matchingData!.preferences.currentFilters.location}
          </Text>,
  </View>
      </View>,
  <View style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Filter Effectiveness</Text>,
  {matchingData!.preferences.filterEffectiveness.map((filter, index) => (,
  <View key={index} style={styles.effectivenessItem}>
            <View style={styles.effectivenessHeader}>,
  <Text style={[styles.effectivenessLabel, { color: theme.colors.text}]}>,
  {filter.filter}
              </Text>,
  <Text style={[styles.effectivenessValue, { color: theme.colors.primary}]}>,
  {filter.matchQuality}%, ,
  </Text>
  </View>,
  <View style = {styles.effectivenessBar}>
  <View,
  style={{ [styles.effectivenessProgress, {,
  backgroundColor: theme.colors.primary,
    width: `${filter.matchQuality  ] }%` ,
  }]},
  />
            </View>,
  <Text style={[styles.effectivenessUsage, { color: theme.colors.textSecondary}]}>,
  Used in {filter.usage}% of searches, ,
  </Text>
          </View>,
  ))}
      </View>,
  <TouchableOpacity
        style={{ [styles.actionButton, { backgroundColor: theme.colors.primary  ] }]},
  onPress={() => router.push('/(tabs)/profile/unified-preferences? tab=matching' as any)}
      >,
  <Filter size={20} color={'#FFFFFF' /}>
        <Text style={styles.actionButtonText}>Adjust Filters</Text>,
  </TouchableOpacity>
    </View>,
  )
  const renderContent = () => {,
  switch (selectedTab) {
      case 'overview'     : return renderOverviewTab(),
  case 'matches':  
        return renderMatchesTab(),
  case 'compatibility':  
        return renderCompatibilityTab(),
  case 'messages':  ;
        return renderMessagesTab(),
  case 'preferences':  ;
        return renderPreferencesTab(),
  default: 
        return renderOverviewTab(),
  }
  },
  if (loading) {
    return (,
  <SafeAreaView style= {[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={{   title: 'Matching Insights', headerShown: true        }} />,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText, { color: theme.colors.textSecondary}]}>,
  Loading matching insights..., ,
  </Text>
        </View>,
  </SafeAreaView>
    ),
  }
  if (!matchingData) {,
  return (
      <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={{   title: 'Matching Insights', headerShown: true        }} />,
  <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.colors.error}]}>,
  Failed to load matching insights, ,
  </Text>
          <TouchableOpacity,
  style={{ [styles.retryButton, { backgroundColor: theme.colors.primary  ] }]},
  onPress={loadMatchingData}
          >,
  <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={{   title: 'Matching Insights', headerShown: true        }} />,
  {renderTabNavigation()}
      <ScrollView,
  style={styles.scrollView}
        refreshControl={,
  <RefreshControl
            refreshing={refreshing},
  onRefresh={onRefresh}
            colors={[theme.colors.primary]},
  />
        },
  showsVerticalScrollIndicator={false}
      >,
  {renderContent()}
      </ScrollView>,
  </SafeAreaView>
  ),
  }
const styles = StyleSheet.create({ container: {,
    flex: 1 }, ,
  scrollView: { flex: 1 }
  loadingContainer: {,
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center',
  }
  loadingText: { marginTop: 12,
    fontSize: 16 },
  errorContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  errorText: { fontSize: 16,
    textAlign: 'center',
  marginBottom: 20 }
  retryButton: { paddingHorizontal: 20,
    paddingVertical: 12,
  borderRadius: 8 }
  retryButtonText: {,
    color: '#FFFFFF',
  fontSize: 16,
    fontWeight: '600',
  }
  tabContainer: {,
    borderBottomWidth: 1,
  borderBottomColor: '#E5E7EB'
  },
  tab: { flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 16,
    paddingVertical: 12,
  marginRight: 8 }
  activeTab: { borderBottomWidth: 2 },
  tabText: {,
    marginLeft: 6,
  fontSize: 14,
    fontWeight: '600',
  }
  tabContent: { padding: 16 },
  card: {,
    borderRadius: 12,
  padding: 16,
    marginBottom: 16,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
  },
  cardTitle: { fontSize: 18,
    fontWeight: '600',
  marginBottom: 16 }
  metricsGrid: {,
    flexDirection: 'row',
  flexWrap: 'wrap'),
    justifyContent: 'space-between'),
  marginBottom: 16)
  },
  metricCard: { width: (width - 48) / 2,
    padding: 16,
  borderRadius: 12,
    alignItems: 'center',
  marginBottom: 12 }
  metricValue: { fontSize: 24,
    fontWeight: 'bold',
  marginTop: 8 }
  metricLabel: {,
    fontSize: 12,
  marginTop: 4,
    textAlign: 'center',
  }
  performanceRow: {,
    flexDirection: 'row',
  justifyContent: 'space-around'
  },
  performanceItem: {,
    alignItems: 'center',
  }
  performanceLabel: {,
    fontSize: 12,
  marginTop: 4,
    textAlign: 'center',
  }
  performanceValue: { fontSize: 16,
    fontWeight: 'bold',
  marginTop: 2 }
  matchItem: { paddingVertical: 12,
    borderBottomWidth: 1 },
  matchHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 4 },
  matchName: {,
    fontSize: 16,
  fontWeight: '600'
  },
  qualityBadge: { paddingHorizontal: 8,
    paddingVertical: 4,
  borderRadius: 12 }
  qualityText: {,
    fontSize: 12,
  fontWeight: '600'
  },
  matchInterests: { fontSize: 14,
    marginBottom: 4 },
  matchStats: {,
    flexDirection: 'row',
  justifyContent: 'space-between'
  },
  matchStat: { fontSize: 12 }
  fullMatchCard: { borderRadius: 12,
    padding: 16,
  marginBottom: 12 }
  matchCardHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: 12 },
  matchCardName: {,
    fontSize: 18,
  fontWeight: '600'
  },
  matchCardScore: { fontSize: 14,
    marginTop: 2 },
  matchCardContent: { gap: 8 }
  matchCardLabel: {,
    fontSize: 14,
  fontWeight: '500'
  },
  interestTags: { flexDirection: 'row',
    flexWrap: 'wrap',
  gap: 6 }
  interestTag: { paddingHorizontal: 8,
    paddingVertical: 4,
  borderRadius: 12 }
  interestTagText: {,
    fontSize: 12,
  fontWeight: '500'
  },
  matchCardStats: {,
    flexDirection: 'row',
  justifyContent: 'space-between'
  },
  statItem: {,
    flexDirection: 'row',
  alignItems: 'center'
  },
  statText: { marginLeft: 4,
    fontSize: 12 },
  compatibilityItem: { marginBottom: 16 }
  compatibilityHeader: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 8 }
  compatibilityLabel: { marginLeft: 8,
    fontSize: 14,
  fontWeight: '500',
    flex: 1 },
  compatibilityValue: {,
    fontSize: 14,
  fontWeight: '600'
  },
  compatibilityBar: { height: 6,
    backgroundColor: '#F3F4F6',
  borderRadius: 3,
    overflow: 'hidden',
  marginBottom: 4 }
  compatibilityProgress: { height: '100%',
    borderRadius: 3 },
  factorItem: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 12 }
  factorText: { marginLeft: 8,
    fontSize: 14,
  flex: 1 }
  messageStatsGrid: { flexDirection: 'row',
    justifyContent: 'space-between',
  marginBottom: 16 }
  messageStatCard: {,
    width: (width - 48) / 2,
  padding: 16,
    borderRadius: 12,
  alignItems: 'center'
  },
  messageStatValue: { fontSize: 20,
    fontWeight: 'bold',
  marginTop: 8 }
  messageStatLabel: {,
    fontSize: 11,
  marginTop: 4,
    textAlign: 'center',
  }
  listItem: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 12 }
  listIcon: { width: 24,
    height: 24,
  borderRadius: 12,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12 },
  listNumber: {,
    fontSize: 12,
  fontWeight: 'bold'
  },
  listText: { flex: 1,
    fontSize: 14 },
  filterItem: { flexDirection: 'row',
    justifyContent: 'space-between',
  marginBottom: 12 }
  filterLabel: { fontSize: 14 },
  filterValue: {,
    fontSize: 14,
  fontWeight: '500'
  },
  effectivenessItem: { marginBottom: 16 }
  effectivenessHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  effectivenessLabel: {,
    fontSize: 14,
  fontWeight: '500'
  },
  effectivenessValue: {,
    fontSize: 14,
  fontWeight: '600'
  },
  effectivenessBar: { height: 6,
    backgroundColor: '#F3F4F6',
  borderRadius: 3,
    overflow: 'hidden',
  marginBottom: 4 }
  effectivenessProgress: { height: '100%',
    borderRadius: 3 },
  effectivenessUsage: { fontSize: 12 }
  actionButton: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    padding: 16,
  borderRadius: 12,
    marginTop: 8 },
  actionButtonText: { color: '#FFFFFF',
    fontSize: 16,
  fontWeight: '600',
    marginLeft: 8 },
  })