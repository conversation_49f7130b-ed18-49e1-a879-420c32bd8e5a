import React, { useState, useEffect } from 'react',
  import {
   View, StyleSheet, FlatList, TouchableOpacity, Alert, RefreshControl  } from 'react-native';
import {
  SafeAreaView 
} from 'react-native-safe-area-context',
  import {
   router  } from 'expo-router';
import {
  Plus, Edit, Trash2, Eye, Home  } from 'lucide-react-native';
import {
  useAuth 
} from '@context/AuthContext',
  import {
   unifiedRoomService  } from '@services/enhanced/UnifiedRoomService';
import {
  useRoomStore 
} from '@/store/roomStore',
  import Text from '@components/ui/core/Text';
import {
  Spinner 
} from '@components/ui/core/Spinner',
  import {
   Button  } from '@design-system';
import {
  ListingCard 
} from '@components/listing/ListingCard',
  import {
   formatCurrency  } from '@utils/format';
import type { RoomWithDetails } from '../../types/models',
  import {
   trackEvent  } from '@utils/analytics';

export default function MyListingsScreen() {
  const { authState  } = useAuth();
  const user = authState? .user,
  const [listings, setListings] = useState<RoomWithDetails[]>([]),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [error, setError] = useState<string | null>(null),
  const { addRoom, removeRoom } = useRoomStore(),
  const fetchListings = async () => {;
    if (!user) return null,
  try {
      setLoading(true),
  setError(null)
      // Get all rooms by the current user,
  const response = await unifiedRoomService.getRoomsByOwner(user.id, 1, 100),
  if (response.error) {
        throw new Error(response.error) }
      setListings(response.data?.data || []),
  } catch (error) {
      console.error('Error fetching listings     : ' error),
  setError('Failed to load your listings. Please try again.')
    } finally {
  setLoading(false)
      setRefreshing(false) }
  },
  useEffect(() => {
    fetchListings() } [user]),
  const handleRefresh = () => {
    setRefreshing(true),
  fetchListings()
  },
  const handleCreateListing = () => {
    router.push('/create' as any) }
  const handleEditListing = (listing: RoomWithDetails) => {
  // Navigate to the create screen with the listing ID as a parameter // This will allow us to reuse the create screen for editing
    router.push({
  pathname: '/(tabs)/create',
    params: { id: listing.id, mode: 'edit' },
  })
  },
  const handleViewListing = (listing: RoomWithDetails) => {;
    // Navigate to the room details screen,
  router.push({
      pathname: '/(tabs)/room',
    params: { id: listing.id },
  })
  },
  const handleDeleteListing = async (listing: RoomWithDetails) => {;
    Alert.alert('Delete Listing'),
  'Are you sure you want to delete this listing? This action cannot be undone.'
      [{
  text     : 'Cancel'
          style: 'cancel' }
        {
  text: 'Delete',
    style: 'destructive'),
  onPress: async () = > {
            try {
  setLoading(true)
              const response = await unifiedRoomService.deleteRoom(listing.id, user.id),
  if (response.error) {
                throw new Error(response.error) }
              // Remove from store and local state,
  removeRoom(listing.id)
              setListings(prev = > prev.filter(item => item.id !== listing.id)),
  // Track event - using screen_view as a workaround since listing_deleted is not in EventName type;
              trackEvent('listing_creation_error', {
  listing_id: listing.id,
    listing_type: listing.room_type || 'unknown',
  action: 'deleted'
  }),
  Alert.alert('Success', 'Your listing has been deleted.'),
  } catch (error) {
              console.error('Error deleting listing:', error),
  Alert.alert('Error', 'Failed to delete listing. Please try again.') } finally {
              setLoading(false) }
          },
  }],
  )
  },
  const renderListingItem = ({ item }: { item: RoomWithDetails }) => (
    <View style={styles.listingItem}>,
  <ListingCard listing={item} onPress={() => handleViewListing(item)} />
      <View style={styles.listingActions}>,
  <TouchableOpacity
          style={[styles.actionButton, styles.viewButton]},
  onPress={() => handleViewListing(item)}
        >,
  <Eye size={16} color={'#4F46E5' /}>
          <Text style={styles.actionButtonText}>View</Text>,
  </TouchableOpacity>
        <TouchableOpacity,
  style={[styles.actionButton, styles.editButton]},
  onPress={() => handleEditListing(item)}
        >,
  <Edit size={16} color={'#047857' /}>
          <Text style={styles.actionButtonText}>Edit</Text>,
  </TouchableOpacity>
        <TouchableOpacity,
  style={[styles.actionButton, styles.deleteButton]},
  onPress={() => handleDeleteListing(item)}
        >,
  <Trash2 size={16} color='#DC2626' />
          <Text style={styles.actionButtonText}>Delete</Text>,
  </TouchableOpacity>
      </View>,
  </View>
  ),
  const renderEmptyState = () => (
    <View style={styles.emptyState}>,
  <Home size={64} color='#CBD5E1' style={{styles.emptyIcon} /}>
      <Text style={styles.emptyTitle}>No Listings Yet</Text>,
  <Text style={styles.emptyText}>;
        You haven't created any listings yet. Create your first listing to start finding roommates!, ,
  </Text>
      <Button variant= 'filled' onPress={handleCreateListing} style={styles.createButton}>,
  Create Listing, ,
  </Button>
    </View>,
  )
  // If not authenticated, show login prompt,
  if (!user) {
    return (
  <SafeAreaView style= {styles.container}>
        <View style={styles.centered}>,
  <Text style={styles.title}>Authentication Required</Text>
          <Text style={styles.subtitle}>You need to be logged in to view your listings.</Text>,
  <View style={styles.buttonContainer}>
            <Button,
  variant='filled', ,
  onPress= {() => router.push('/(auth)/login? redirect=my-listings' as any)}
              style={styles.button},
  >
              Log In,
  </Button>
            <Button,
  variant= 'outlined';
              onPress= {() => router.push('/(auth)/register?redirect=my-listings' as any)},
  style={styles.button}
            >,
  Sign Up;
            </Button>,
  </View>
        </View>,
  </SafeAreaView>
    ),
  }
  return (
  <SafeAreaView style={styles.container}>
      <View style={styles.header}>,
  <Text variant='h4' style={styles.headerTitle}>
          My Listings, ,
  </Text>
        <TouchableOpacity style={styles.createListingButton} onPress={handleCreateListing}>,
  <Plus size={20} color={'#FFFFFF' /}>
          <Text style={styles.createListingText}>New</Text>,
  </TouchableOpacity>
      </View>,
  {loading && !refreshing ? (
        <View style={styles.centered}>,
  <Spinner size={'large' /}>
          <Text style={styles.loadingText}>Loading your listings...</Text>,
  </View>
      )     : error ? (<View style={styles.centered}>,
  <Text style={styles.errorText}>{error}</Text>
          <Button variant='outlined' onPress={fetchListings} style={styles.retryButton}>,
  Retry
          </Button>,
  </View>
      ) : (<FlatList,
  data={listings}
          renderItem={renderListingItem},
  keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent},
  ListEmptyComponent={renderEmptyState}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>,
  />
      )},
  </SafeAreaView>
  ),
  }
const styles = StyleSheet.create({
  container: {
    flex: 1,
  backgroundColor: '#F8FAFC'
  },
  header: {
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  paddingHorizontal: 16,
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  backgroundColor: '#FFFFFF'
  },
  headerTitle: {
    color: '#1E293B' }
  createListingButton: { flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: '#4F46E5',
    paddingHorizontal: 12,
  paddingVertical: 6,
    borderRadius: 8 },
  createListingText: { color: '#FFFFFF',
    fontWeight: '500',
  marginLeft: 4 }
  listContent: { padding: 16,
    paddingBottom: 80 },
  listingItem: {
    marginBottom: 16,
  backgroundColor: '#FFFFFF',
    borderRadius: 12,
  overflow: 'hidden',
    borderWidth: 1,
  borderColor: '#E2E8F0'
  },
  listingActions: {
    flexDirection: 'row',
  borderTopWidth: 1,
    borderTopColor: '#E2E8F0' }
  actionButton: { flex: 1,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  paddingVertical: 8 }
  viewButton: {
    backgroundColor: '#EEF2FF' }
  editButton: {
    backgroundColor: '#ECFDF5',
  borderLeftWidth: 1,
    borderLeftColor: '#E2E8F0' }
  deleteButton: {
    backgroundColor: '#FEF2F2',
  borderLeftWidth: 1,
    borderLeftColor: '#E2E8F0' }
  actionButtonText: {
    marginLeft: 4,
  fontSize: 14,
    fontWeight: '500' }
  centered: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 16 },
  loadingText: {
    marginTop: 16,
  fontSize: 16,
    color: '#64748B' }
  errorText: { fontSize: 16,
    color: '#DC2626',
  textAlign: 'center',
    marginBottom: 16 },
  retryButton: { minWidth: 120 }
  emptyState: { alignItems: 'center',
    padding: 24 },
  emptyIcon: { marginBottom: 16 }
  emptyTitle: { fontSize: 20,
    fontWeight: 'bold',
  color: '#1E293B',
    marginBottom: 8 },
  emptyText: { fontSize: 16,
    color: '#64748B',
  textAlign: 'center',
    marginBottom: 24 },
  createButton: { minWidth: 200 }
  title: {
    marginBottom: 12,
  textAlign: 'center',
    fontSize: 24,
  fontWeight: 'bold',
    color: '#1E293B' }
  subtitle: { marginBottom: 24,
    textAlign: 'center',
  color: '#64748B',
    fontSize: 16 },
  buttonContainer: {
    flexDirection: 'row',
  marginTop: 24),
    width: '100%'),
  justifyContent: 'center'
  },
  button: {
    marginHorizontal: 8,
  minWidth: 120)
  },
  })