import React, { useState, useEffect } from 'react';,
  import {
  ,
  View
  StyleSheet,
  ScrollView
  Alert,
  ActivityIndicator
  TouchableOpacity,
  } from 'react-native';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  import {
   router, useLocalSearchParams ,
  } from 'expo-router';
import {,
  ListingForm 
} from '@components/listing/ListingForm';,
  import {
   ListingCreationSuccess ,
  } from '@components/listing/ListingCreationSuccess';
import {,
  PlanSelectionModal 
} from '@components/listing/PlanSelectionModal';,
  import {
   PaymentMethodModal ,
  } from '@components/listing/PaymentMethodModal';
import {,
  useAuth 
} from '@context/AuthContext';,
  import {
  ,
  unifiedRoomService
  RoomCreationResult,
  DetailedError
  } from '@services/enhanced/UnifiedRoomService';,
  import {
  unifiedPaymentService ,
  } from '@services';
  import Text from '@components/ui/core/Text';,
  import {
  Spinner ,
  } from '@components/ui/core/Spinner';
  import ErrorHandler from '@components/ui/ErrorHandler';,
  import UploadProgress, { UploadItem } from '@components/ui/UploadProgress';,
  import {
   formatDateString ,
  } from '@utils/date';
import {,
  Button 
} from '@design-system';,
  import {
   ArrowLeft, Info, Eye ,
  } from 'lucide-react-native';
import Toast from 'react-native-toast-message';,
  import {
   useRoomStore ,
  } from '@/store/roomStore';
import {,
  trackEvent 
} from '@utils/analytics';,
  import {
   LISTING_CONFIG ,
  } from '@config/listingConfig';

import type { RoomFormData, RoomWithDetails } from '../../types/models';,
  import type { PaymentMethod } from '@core/services/payment/SecurePaymentService';

export default function CreateListingScreen() {,
  const { authState  } = useAuth();
  const user = authState? .user;,
  const authLoading = authState?.isLoading;
  const [isSubmitting, setIsSubmitting] = useState(false),
  const [draftSaved, setDraftSaved] = useState(false),
  const [formData, setFormData] = useState<Partial<RoomFormData> | null>(null),
  const [existingListing, setExistingListing] = useState<RoomWithDetails | null>(null),
  const [isLoading, setIsLoading] = useState(false),
  const [createdListing, setCreatedListing] = useState<RoomWithDetails | null>(null) ,
  const [showSuccess, setShowSuccess] = useState(false) // Enhanced error handling const [currentError, setCurrentError] = useState<DetailedError | null>(null),  const [operationId, setOperationId] = useState<string | null>(null)  const [retryAttempts, setRetryAttempts] = useState(0); // Upload progress tracking const [uploadProgress, setUploadProgress] = useState<UploadItem[]>([])  const [showUploadProgress, setShowUploadProgress] = useState(false),  // Payment flow states const [showPlanSelection, setShowPlanSelection] = useState(false)  const [showPaymentMethod, setShowPaymentMethod] = useState(false); const [selectedPlanId, setSelectedPlanId] = useState<string>('basic')  const [isProcessingPayment, setIsProcessingPayment] = useState(false),  const [currentListingData, setCurrentListingData] = useState<RoomFormData | null>(null); const { addRoom, updateRoom  } = useRoomStore(); const { draft, id, mode  } = useLocalSearchParams<{ draft      : string id: string mode: string }>() const isEditMode = mode === 'edit' && id // Load draft data or existing listing if available useEffect(() => { const loadData = async () => { if (!user) return null setIsLoading(true) try { // If in edit mode, load the existing listing if (isEditMode) { const response = await unifiedRoomService.getRoomById(id, user.id); if (response.data) { setExistingListing(response.data); // Convert to form data format const listingFormData: Partial<RoomFormData> = {  title: response.data.title, description: response.data.description || '', price: response.data.price, location: response.data.location, room_type: response.data.room_type || 'private', bedrooms: response.data.bedrooms || 1, bathrooms: response.data.bathrooms || 1, furnished: response.data.furnished || false, pets_allowed: response.data.pets_allowed || false, images: response.data.images || [], amenities: [], preferences: [], move_in_date: formatDateString(new Date()) status: 'available'  }; // Use type casting to handle additional properties that might exist on the response try { // Cast to a more flexible type that includes the properties we need const roomData = response.data as any; // Check for direct properties if (Array.isArray(roomData.amenities)) { listingFormData.amenities = roomData.amenities } if (Array.isArray(roomData.preferences)) { listingFormData.preferences = roomData.preferences } if (roomData.move_in_date) { listingFormData.move_in_date = roomData.move_in_date } // Check for metadata as fallback if (roomData.meta_data) { const metadata = typeof roomData.meta_data === 'string' ? JSON.parse(roomData.meta_data)      : roomData.meta_data if (Array.isArray(metadata.amenities)) { listingFormData.amenities = metadata.amenities } if (Array.isArray(metadata.preferences)) { listingFormData.preferences = metadata.preferences } if (metadata.move_in_date) { listingFormData.move_in_date = metadata.move_in_date } } } catch (error) { console.error('Error extracting listing data:' error) } setFormData(listingFormData) } else { Alert.alert('Error', 'Failed to load listing data') router.back() } } // Otherwise, if draft parameter is true, load the draft else if (draft === 'true') { const savedDraft = await unifiedRoomService.getDraft() if (savedDraft.data) { setFormData(savedDraft.data); Toast.show({  type: 'info', text1: 'Draft Loaded', text2: 'Your previously saved draft has been loaded', position: 'bottom'  }); } } } catch (error) { console.error('Error loading data:', error); Toast.show({  type: 'error', text1: 'Error', text2: 'Failed to load data', position: 'bottom'  }); } finally { setIsLoading(false) } }; loadData(); } [user, draft, id, isEditMode]); // Auto-save draft every 60 seconds useEffect(() => { let interval: NodeJS.Timeout; if (user && formData && !isSubmitting) { interval = setInterval(() => { handleSaveDraft(formData) } 60000); // 60 seconds } return () => { if (interval) clearInterval(interval) }; } [user, formData, isSubmitting]); // Save form data as draft const handleSaveDraft = async (data: Partial<RoomFormData>) => { if (!user) return null; try { await unifiedRoomService.saveDraft(data); setDraftSaved(true); // Show toast only for manual saves, not auto-saves if (!formData || JSON.stringify(formData) !== JSON.stringify(data)) { Toast.show({  type: 'success', text1: 'Draft Saved', text2: 'Your listing draft has been saved', position: 'bottom'  }); } setFormData(data); } catch (error) { console.error('Error saving draft:', error); Toast.show({  type: 'error', text1: 'Error', text2: 'Failed to save draft', position: 'bottom'  }); } }; // Handle form submission (create or update) const handleCreateListing = async (data: RoomFormData) => { if (!user) { Alert.alert('Error', 'You must be logged in to create or update a listing'); return null } // For edit mode; update directly without payment if (isEditMode && existingListing) { await handleDirectUpdate(data); return null } // For new listings; check if payment is required if (LISTING_CONFIG.enablePaymentRequired) { // Store the listing data and show plan selection setCurrentListingData(data); setShowPlanSelection(true) } else { // Create listing directly if payment is not required await handleDirectCreate(data) } }; // Enhanced listing creation with detailed error handling const handleDirectCreate = async (data: RoomFormData, retryOptions?: { retryAttempts?: number,  skipImageUpload?: boolean,  operationId?: string }) => { try { setIsSubmitting(true); setCurrentError(null); const attempts = retryOptions? .retryAttempts ?? 3; const skipImages = retryOptions?.skipImageUpload ?? false; const opId = retryOptions?.operationId ?? `create_${Date.now()}_${Math.random().toString(36).substring(7)}`; setOperationId(opId); // Setup upload progress tracking if images are present if (data.images && data.images.length > 0 && !skipImages) { const uploadItems     : UploadItem[] = (data.images as File[]).map((file index) => ({ id: `upload_${index}` name: file.name, progress: 0, status: 'pending', })) setUploadProgress(uploadItems) setShowUploadProgress(true) } // Track event trackEvent('listing_create_started', { user_id: user.id, listing_type: data.room_type, operation_id: opId, retry_attempt: retryAttempts }) // Enhanced room creation with options const response: RoomCreationResult = await unifiedRoomService.createRoom(data, user.id, { retryAttempts: attempts, skipImageUpload: skipImages, idempotencyKey: opId }); // Hide upload progress setShowUploadProgress(false); setUploadProgress([]); if (response.error) { // Handle detailed error response const errorDetails: DetailedError = { code: response.status === 409 ? 'DUPLICATE_ERROR' as any      : 'DATABASE_ERROR' as any message: response.error, retryable: response.status >= 500, suggestedAction: response.status === 409 ? 'Please modify your listing to make it unique'   : 'Please check your connection and try again' } setCurrentError(errorDetails) throw new Error(response.error) } // Clear the draft since we've successfully created the listing await unifiedRoomService.clearDraft() // Add to store if (response.data) { addRoom(response.data) setCreatedListing(response.data) setShowSuccess(true) } // Track successful operation trackEvent('listing_create_success', { user_id: user.id, listing_id: response.data? .id, listing_type     : data.room_type operation_id: opId, image_count: response.uploadedImages? .length || 0 }) // Reset error state setRetryAttempts(0) } catch (error) { console.error('Error creating listing  : ' error) setShowUploadProgress(false) // Track error with more details trackEvent('listing_create_error', { user_id: user.id, error_message: (error as Error).message, operation_id: operationId, retry_attempt: retryAttempts }) // If we don't have a detailed error yet, create one if (!currentError) { const errorDetails: DetailedError = {  code: 'DATABASE_ERROR' as any, message: (error as Error).message, retryable: true, suggestedAction: 'Please check your connection and try again'  }; setCurrentError(errorDetails); } } finally { setIsSubmitting(false) } }; // Handle direct listing update (for edit mode) const handleDirectUpdate = async (data: RoomFormData) => { try { setIsSubmitting(true); // Track event trackEvent('listing_saved', { user_id: user.id, listing_type: data.room_type, mode: 'edit' }); const listingData: RoomFormData = {  title: data.title, description: data.description, price: data.price, location: data.location, room_type: data.room_type, bedrooms: data.bedrooms, bathrooms: data.bathrooms, furnished: data.furnished, pets_allowed: data.pets_allowed, images: data.images || [], amenities: data.amenities || [], preferences: data.preferences || [], move_in_date: data.move_in_date, status: 'available', locationData: data.locationData, locationText: data.locationText  }; const response = await unifiedRoomService.updateRoom(id, listingData, user.id); if (response.error) { throw new Error(response.error) } // Clear the draft since we've successfully updated the listing await unifiedRoomService.clearDraft(); // Update in store if (response.data) { updateRoom(response.data) } // Track successful operation trackEvent('listing_saved', { user_id: user.id, listing_id: response.data? .id, listing_type     : data.room_type is_new: false }) Alert.alert('Success', 'Your listing has been updated successfully!', [{ text: 'View My Listings', onPress: () => router.push('/(tabs)/my-listings' as any) } { text: 'Back to Browse', onPress: () => router.push('/' as any) }]) } catch (error) { console.error('Error updating listing:', error) // Track error trackEvent('listing_saved', { user_id: user.id, error: true, error_message: (error as Error).message, mode: 'edit' }),  Alert.alert( 'Error', `Failed to update listing: ${(error as Error).message}` [{ text: 'Try Again', style: 'cancel' } { text: 'Save as Draft', onPress: () => handleSaveDraft(data) }] ) } finally { setIsSubmitting(false) } }; // Handle navigation back to browse const handleBackToBrowse = () => { if (formData && !draftSaved) { Alert.alert('Save Draft? ', 'Do you want to save your progress as a draft?', [{ text     : 'No' onPress: () => router.push('/browse' as any) style: 'cancel' } { text: 'Yes', onPress: async () => { await handleSaveDraft(formData) router.push('/browse' as any) } }]) } else { router.push('/browse' as any) } } // Handle creating another listing const handleCreateAnother = () => { setShowSuccess(false) setCreatedListing(null); setFormData(null) }; // Handle plan selection const handlePlanSelected = (planId: string) => { console.log('🎯 Plan selected:', planId); setSelectedPlanId(planId); setShowPlanSelection(false); // Close plan selection first setShowPaymentMethod(true); // Then show payment method // Track plan selection trackEvent('listing_plan_selected', { user_id: user? .id, plan_id     : planId listing_data: !!currentListingData }) } // Handle payment completion const handlePaymentComplete = async (paymentMethod: PaymentMethod) => { if (!currentListingData || !user) { Alert.alert('Error', 'Missing listing data or user information') return null } setIsProcessingPayment(true) try { // Track payment attempt trackEvent('listing_payment_started', { user_id: user.id, plan_id: selectedPlanId, payment_method: paymentMethod.type, amount: unifiedPaymentService.getListingPlan(selectedPlanId)? .price }); // Process payment and create listing const result = await unifiedPaymentService.createPaidListing({  userId     : user.id planId: selectedPlanId, listingData: currentListingData, paymentMethod, listingType: 'room', // Default to room, can be made configurable  }) if (result.success && result.listing) { // Payment successful, listing created setShowPaymentMethod(false) setShowPlanSelection(false) // Clear the draft since we've successfully created the listing await unifiedRoomService.clearDraft() // Add to store addRoom(result.listing); setCreatedListing(result.listing); setShowSuccess(true); // Track successful payment trackEvent('listing_payment_completed', { user_id: user.id, listing_id: result.listingId, plan_id: selectedPlanId, payment_method: paymentMethod.type, amount: unifiedPaymentService.getListingPlan(selectedPlanId)? .price }); Toast.show({  type     : 'success' text1: 'Payment Successful', text2: 'Your listing has been created and is now live!', position: 'bottom'  }) } else { // Payment failed Alert.alert( 'Payment Failed', result.error || 'There was an issue processing your payment. Please try again.', [{ text: 'Try Again', style: 'default' } { text: 'Save as Draft', onPress: () => { setShowPaymentMethod(false) setShowPlanSelection(false)  handleSaveDraft(currentListingData) } }] ) // Track payment failure trackEvent('listing_payment_failed', { user_id: user.id, plan_id: selectedPlanId, payment_method: paymentMethod.type, error: result.error }) } } catch (error) { console.error('Error processing payment:', error); Alert.alert( 'Payment Error', 'An unexpected error occurred while processing your payment. Please try again.', [{ text: 'Try Again', style: 'default' } { text: 'Save as Draft', onPress: () => { setShowPaymentMethod(false); setShowPlanSelection(false); handleSaveDraft(currentListingData) } }] ); // Track payment error trackEvent('listing_payment_error', { user_id: user.id, plan_id: selectedPlanId, payment_method: paymentMethod.type, error: (error as Error).message }); } finally { setIsProcessingPayment(false) } }; // Handle closing payment modals const handleClosePaymentFlow = () => { console.log('❌ Close payment flow triggered', { currentListingData: !!currentListingData, showPlanSelection, showPaymentMethod }); if (currentListingData) { Alert.alert( 'Save Draft? ', 'Do you want to save your listing as a draft before closing?', [{ text     : 'Discard' style: 'destructive', onPress: () => { console.log('🗑️ User chose to discard') setShowPaymentMethod(false) setShowPlanSelection(false) setCurrentListingData(null) } } { text: 'Save Draft', onPress: () => { console.log('💾 User chose to save draft') handleSaveDraft(currentListingData); setShowPaymentMethod(false); setShowPlanSelection(false); setCurrentListingData(null) } }] ); } else { console.log('🔄 Closing modals without data'); setShowPaymentMethod(false); setShowPlanSelection(false) } }; // Retry operation with enhanced options const handleRetryOperation = () => { if (currentListingData && currentError) { setRetryAttempts(prev => prev + 1); // For image upload errors, offer option to skip images if (currentError.code === 'IMAGE_UPLOAD_FAILED') { Alert.alert( 'Retry Options', 'Would you like to retry with or without images? ', [{ text     : 'Cancel' style: 'cancel' } { text: 'Retry with Images', onPress: () => handleDirectCreate(currentListingData, { retryAttempts: 2, operationId }) } { text: 'Skip Images', onPress: () => handleDirectCreate(currentListingData, { retryAttempts: 1, skipImageUpload: true, operationId }) }] ) } else { handleDirectCreate(currentListingData, { retryAttempts: 2, operationId }) } } } // Handle upload progress updates (this would be called by the media service) const handleUploadProgress = (uploadId: string, progress: number, status?: 'completed' | 'failed', error?: string) => { setUploadProgress(prev => prev.map(upload => upload.id === uploadId ? { ...upload, progress, status    : status || 'uploading' error } : upload ) ) } // Retry individual upload const handleRetryUpload = (uploadId: string) => { setUploadProgress(prev => prev.map(upload => upload.id === uploadId ? { ...upload, status   : 'pending' progress: 0, error: undefined } : upload ) ) // Here you would trigger the actual retry logic } // Cancel upload const handleCancelUpload = (uploadId: string) => { setUploadProgress(prev => prev.filter(upload => upload.id !== uploadId)) } // Cancel all uploads const handleCancelAllUploads = () => { setUploadProgress([]) setShowUploadProgress(false); setIsSubmitting(false) }; // Dismiss error const handleDismissError = () => { setCurrentError(null); setOperationId(null); setRetryAttempts(0) }; // Contact support const handleContactSupport = () => { // Here you would implement support contact logic console.log('Contacting support with error details:', currentError) }; // Show loading state while checking authentication if (authLoading) { return ( <View style={styles.loadingWrapper}> <Spinner size={"large" /}> <Text style={styles.loadingMessage}>Loading...</Text> </View> ); } // Redirect to login if not authenticated if (!user) { return ( <View style={styles.loadingWrapper}> <Info size={48} color={"#64748B" /}> <Text style={styles.headerTitle}>Sign In Required</Text> <Text style={styles.subtitle}>You need to be logged in to create a listing</Text> <View style={styles.actionButtons}> <Button onPress={() => router.push('/(auth)/login' as any)} style={styles.actionButton} variant="filled" > Sign In </Button> <Button onPress={() => router.push('/(auth)/register' as any)} style={styles.actionButton} variant="outlined" > Create Account </Button> </View> </View> ); } return ( <SafeAreaView style= {[styles.container,  { showSuccess && createdListing ? ( <ScrollView contentContainerStyle= {{styles.scrollContent}]}}> <ListingCreationSuccess listing={createdListing} onCreateAnother={{handleCreateAnother} /}> </ScrollView> )      : ( <ScrollView contentContainerStyle={styles.scrollContent}> <View style={styles.header}> <TouchableOpacity onPress={() => router.back()} style={styles.backButton}> <ArrowLeft size={24} color={"#64748B" /}> </TouchableOpacity> <Text style={{[styles.headerTitle { isEditMode ? 'Edit Listing'  : 'Create Listing'}]}</Text}> {isEditMode && ( <TouchableOpacity onPress={() => router.push(`/room/${id}`)} style={styles.viewButton} > <Eye size={20} color={"#6366F1" /}> <Text style={styles.viewButtonText}>View</Text> </TouchableOpacity> )} </View> {/* Debug Button - Temporary for testing photo upload fixes */} {/* Enhanced Error Handler */} <ErrorHandler error={currentError} onRetry={handleRetryOperation} onDismiss={handleDismissError} onContactSupport={handleContactSupport} context="listing_creation" visible={{!!currentError} /}> {/* Upload Progress */} <UploadProgress uploads={uploadProgress} onRetry={handleRetryUpload} onCancel={handleCancelUpload} onCancelAll={handleCancelAllUploads} visible={showUploadProgress} title={"Uploading Room Images" /}> {authLoading || isLoading ? ( <View style={styles.loadingContainer}> <Spinner size="large" color={"#6366F1" /}> <Text style={styles.loadingText}>Loading...</Text> </View> ) : ( <> <ListingForm onSubmit={handleCreateListing} onSaveDraft={handleSaveDraft} initialValues={formData || {}} isEditMode={{!!isEditMode} /}> </> )} </ScrollView> )} {/* Plan Selection Modal */} <PlanSelectionModal visible={showPlanSelection} onClose={handleClosePaymentFlow} onSelectPlan={handlePlanSelected} selectedPlanId={{selectedPlanId} /}> {/* Payment Method Modal */} <PaymentMethodModal visible={showPaymentMethod} onClose={handleClosePaymentFlow} onPaymentComplete={handlePaymentComplete} planId={selectedPlanId} isProcessing={{isProcessingPayment} /}> <Toast /> </SafeAreaView> ),
  }
const styles = StyleSheet.create({,
  container: { flex: 1 backgroundColor: '#F8FAFC' }
  header: {,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  paddingHorizontal: 16,
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  }
  headerTitle: { fontSize: 20, fontWeight: '600', color: '#1E293B' },
  headerRight: { width: 40 });
  backButton: { padding: 8 };,
  viewButton: { flexDirection: 'row', alignItems: 'center', padding: 8 };,
  viewButtonText: { marginLeft: 4, color: '#6366F1', fontWeight: '500' }, ,
  scrollContent: { padding: 16 });
  subtitle: { fontSize: 16, color: '#64748B', marginBottom: 24 };,
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 };,
  loadingText: { marginTop: 12, fontSize: 16, color: '#64748B' };,
  loadingWrapper: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 };,
  loadingMessage: { marginTop: 12, fontSize: 16, color: '#64748B' } ,
  draftSavedContainer: {,
    backgroundColor: '#F0FDF4',
  padding: 12,
    borderRadius: 8,
  marginTop: 16,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  });
  draftSavedText: { color: '#16A34A', fontSize: 14, fontWeight: '500' };,
  actionButtons: { flexDirection: 'row', marginTop: 24, justifyContent: 'center' });,
  actionButton: { marginHorizontal: 8, minWidth: 120 }),
  })