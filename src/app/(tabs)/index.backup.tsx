import React from 'react',
  import {
   View, StyleSheet  } from 'react-native';
import {
  Stack 
} from 'expo-router',
  import HomeScreen from '@features/home';
import HomeErrorBoundary from '@features/home/<USER>/ErrorBoundary',
  /**;
 * Index Tab Screen;
  * Main entry point for the app, shows the Home screen with improved performance,
  * and optimized data fetching for browsing room and housemate listings;
 */,
  export default function TabIndexScreen() {
  return (
  <View style= {styles.container}>
      <Stack.Screen options={   headerShown: false        } />,
  <HomeErrorBoundary>
        <HomeScreen />,
  </HomeErrorBoundary>
    </View>,
  )
},
  const styles = StyleSheet.create({
  container: {
    flex: 1),
  backgroundColor: '#F9FAFB')
  },
  });