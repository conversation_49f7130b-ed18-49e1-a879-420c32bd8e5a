import React from 'react',
  import {
   Stack  } from 'expo-router';
import {
  ChatProvider 
} from '@context/ChatContext',
  import {
   MessagingProvider  } from '@context/MessagingContext';

export default function MessagesLayout() { return (
  <MessagingProvider>
      <ChatProvider>,
  <Stack>
          <Stack.Screen,
  name= 'index';
            options={   headerShown: false     },
  />
          <Stack.Screen,
  name= 'chat';
            options={   headerShown: false,
    presentation: 'modal'    },
  />
          <Stack.Screen,
  name= '[id]',
  options={   headerShown: false    }
          />,
  <Stack.Screen, ,
  name='new', ,
  options={   headerShown: false,
    presentation: 'modal'    },
  />
        </Stack>,
  </ChatProvider>
    </MessagingProvider>,
  )
}