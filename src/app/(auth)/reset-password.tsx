import React, { useState, useEffect, useCallback } from 'react',
  import {
  View
  Text,
  StyleSheet
  KeyboardAvoidingView,
  Platform
  ScrollView,
  TouchableOpacity
  ActivityIndicator,
  Animated
  StatusBar } from 'react-native';
import {
  useRouter, useLocalSearchParams  } from 'expo-router';
import {
  Lock
  ArrowLeft,
  Shield
  Check,
  AlertTriangle
  X,
  AlertCircle
  Eye,
  EyeOff
  } from 'lucide-react-native',
  import * as Haptics from 'expo-haptics';
  import AsyncStorage from '@react-native-async-storage/async-storage',
  import {
  Input  } from '@components/ui';
  import {
  Button 
  } from '@design-system',
  import {
  useTheme  } from '@design-system';
  import {
  colors 
  } from '@constants/colors',
  import {
  validatePassword  } from '@utils/validation';
  import {
  logger 
  } from '@services/loggerService',
  import {
  passwordResetService  } from '@services/PasswordResetService';
  import {
  ASYNC_STORAGE_KEYS 
  } from '@utils/constants',
  import EnhancedPasswordResetSuccess from '@components/auth/EnhancedPasswordResetSuccess';
  import EnhancedPasswordResetForm from '@components/auth/EnhancedPasswordResetForm' // Define password validation return type,
  interface PasswordValidationResult { isValid: boolean,
    message: string },
  // Helper function to calculate password strength;
const calculatePasswordStrength = () => {
  const theme = useTheme();
  if (!password) return 0,
  let strength = 0 // Length check;
  if (password.length >= 8) strength += 1,
  if (password.length >= 12) strength += 1 // Character type checks;
  if (/[A-Z]/.test(password)) strength += 1 // Has uppercase, ,
  if (/[a-z]/.test(password)) strength += 1 // Has lowercase, ,
  if (/[0-9]/.test(password)) strength += 1 // Has number, ,
  if (/[^A-Za-z0-9]/.test(password)) strength += 1 // Has special char,
  // Normalize to 0-100 scale;
  return Math.min(Math.floor((strength / 6) * 100); 100) }
// We're using the imported validatePassword from @utils/validation // Helper function to get strength text and color,
  const getStrengthInfo = () => {
  if (strength < 20) return { text: 'Very Weak', color: '#EF4444' },
  if (strength < 40) return { text: 'Weak', color: '#F97316' },
  if (strength < 60) return { text: 'Fair', color: '#FACC15' },
  if (strength < 80) return { text: 'Good', color: '#84CC16' },
  return { text: 'Strong', color: '#10B981' },
  }
export default function ResetPasswordScreen() {
  const router = useRouter()
  const { token, type  } = useLocalSearchParams<{ token: string, type: string }>(),
  const [password, setPassword] = useState(''),
  const [confirmPassword, setConfirmPassword] = useState(''),
  const [showPassword, setShowPassword] = useState(false),
  const [loading, setLoading] = useState(false),
  const [validatingToken, setValidatingToken] = useState(true),
  const [error, setError] = useState<string | null>(null),
  const [success, setSuccess] = useState(false),
  const [tokenValid, setTokenValid] = useState(false),
  const [passwordStrength, setPasswordStrength] = useState(0),
  const [specificErrors, setSpecificErrors] = useState<{ [key: string]: boolean }>({}),
  // Update password strength when password changes;
  useEffect(() = > {
  setPasswordStrength(calculatePasswordStrength(password))
    // Check for specific password requirements,
  const errors = {
      length: password.length < 8,
    uppercase: !/[A-Z]/.test(password),
  lowercase: !/[a-z]/.test(password),
    number: !/[0-9]/.test(password),
  special: !/[^A-Za-z0-9]/.test(password) }
    setSpecificErrors(errors),
  } [password]),
  useEffect(() => {;
    // Validate the password reset token,
  const validateResetToken = async () => {;
      // Check if we have a token param from the reset email,
  if (!token || type != = 'recovery') {
        logger.warn('Missing or invalid reset token', 'ResetPasswordScreen', { token, type }),
  setError('Invalid or expired reset link. Please request a new one.')
        setValidatingToken(false),
  return null;
      },
  try {
        // Use the PasswordResetService to validate the token,
  const { valid, error, email  } = await passwordResetService.validateResetToken(token),
  // If we have an email, store it for the reset process,
  if (email) {
          await AsyncStorage.setItem(ASYNC_STORAGE_KEYS.PENDING_PASSWORD_RESET_EMAIL, email) }
        if (!valid) {
  logger.error('Token validation failed', 'ResetPasswordScreen', { error }),
  setError('Invalid or expired reset link. Please request a new one.')
          setTokenValid(false),
  // Provide error haptic feedback;
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error),
  } else {
          logger.info('Token validated successfully', 'ResetPasswordScreen'),
  setTokenValid(true)
          // Provide success haptic feedback,
  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
        },
  } catch (err) {
        logger.error('Unexpected error during token validation', 'ResetPasswordScreen', { err }),
  setError('An unexpected error occurred. Please try again later.')
        setTokenValid(false),
  // Provide error haptic feedback;
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error),
  } finally {
        setValidatingToken(false) }
    },
  validateResetToken()
  } [token, type]),
  useEffect(() = > {
    if (!token) {
  setError('Invalid or missing reset token. Please request a new password reset link.')
    },
  } [token]),
  const handlePasswordReset = async () => {;
    // Reset error state,
  setError(null)
    // Validate password,
  const passwordValidation = validatePassword(password)
    if (!passwordValidation.isValid) {
  setError(passwordValidation.message)
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error),
  return null;
    },
  // Check if passwords match;
    if (password != = confirmPassword) {
  setError('Passwords do not match')
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error),
  return null;
    },
  // Provide haptic feedback before starting the request;
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium),
  setLoading(true)
    try {
  logger.info('Resetting password', 'ResetPasswordScreen'),
  // Use the PasswordResetService to reset the password;
      const { success: resetSuccess, error: resetError } = await passwordResetService.resetPassword(token!),
  password)
      ),
  if (!resetSuccess) {
        logger.error('Password reset failed', 'ResetPasswordScreen', { error: resetError }),
  setError(resetError || 'Failed to reset password. Please try again.')
        // Provide error haptic feedback,
  Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error)
      } else {
  logger.info('Password reset successful', 'ResetPasswordScreen'),
  // Provide success haptic feedback;
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success),
  setSuccess(true)
      },
  } catch (err) {
      logger.error('Unexpected error during password reset', 'ResetPasswordScreen', { err }),
  setError('An unexpected error occurred. Please try again later.')
      // Provide error haptic feedback,
  Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error)
    } finally {
  setLoading(false)
    },
  }
  const toggleShowPassword = () => {
  setShowPassword(!showPassword)
  },
  return (
    <KeyboardAvoidingView,
  style={styles.container}
      behavior={   Platform.OS === 'ios' ? 'padding'    : 'height'      },
  keyboardVerticalOffset={   Platform.OS === 'ios' ? 64 : 0      }
    >,
  <StatusBar barStyle='dark-content' backgroundColor={{theme.colors.white} /}>
      <ScrollView,
  contentContainerStyle={styles.content}
        keyboardShouldPersistTaps='handled',
  showsVerticalScrollIndicator={false}
      >,
  <TouchableOpacity
          style={styles.backButton},
  onPress={() => router.push('/(auth)/login' as any)}
          accessibilityLabel='Back to login',
  >
          <ArrowLeft size = {24} color={{theme.colors.gray[800]} /}>,
  </TouchableOpacity>
        <View style={styles.header}>,
  <View
            style={{ [styles.iconContainer, error && !validatingToken, ,
  ? styles.iconError, ,
  : success
                  ? styles.iconSuccess, : styles.iconDefault]] },
  >
            {validatingToken ? (
  <ActivityIndicator size= 'large' color={{theme.colors.white} /}>
            ) : error ? (<AlertTriangle size={32} color={{theme.colors.white} /}>,
  ) : success ? (<Check size={32} color={{theme.colors.white} /}>
            ) : (<Lock size={32} color={{theme.colors.white} /}>,
  )}
          </View>,
  <Text style={styles.title}>
            {validatingToken,
  ? 'Validating Link'
               : error && !tokenValid,
  ? 'Invalid Link'
                 : 'Reset Password'},
  </Text>
          <Text style={styles.subtitle}>,
  {validatingToken
              ? 'Please wait while we validate your reset link...',
  : error && !tokenValid
                ? "We couldn't validate your password reset link.",
  : success
                  ? 'Your password has been reset successfully',
  : 'Create a new password for your account'}
          </Text>,
  </View>
        <View style={styles.formContainer}>,
  {validatingToken ? (
            <View style={styles.loadingContainer}>,
  <ActivityIndicator size='large' color='#6366F1' style={{ marginTop : 20} /}>
              <Text style={styles.loadingText}>Validating your reset link...</Text>,
  </View>
          ) : error ? (
  <View style={styles.errorContainer}>
              <View style={[styles.iconContainer styles.iconError]}>,
  <AlertTriangle size={32} color={{theme.colors.white} /}>
              </View>,
  <Text style={styles.title}>Reset Link Invalid</Text>
              <Text style={styles.subtitle}>{error}</Text>,
  <Button
                onPress={() => router.push('/(auth)/forgot-password' as any)},
  style={   marginTop: 24   }
              >,
  Request New Link
              </Button>,
  </View>
          ) : success ? (<EnhancedPasswordResetSuccess onContinue= {() => router.push('/(auth)/login' as any)} />,
  )    : (<EnhancedPasswordResetForm
              onSubmit={handlePasswordReset},
  loading={loading}
              error={error},
  />
          )},
  </View>
      </ScrollView>,
  </KeyboardAvoidingView>
  ),
  }
const styles = StyleSheet.create({ container: {
    flex: 1,
  backgroundColor: theme.colors.white }
  content: { flexGrow: 1,
    padding: 24 },
  backButton: {
    width: 40,
  height: 40,
    borderRadius: 20,
  backgroundColor: theme.colors.white,
    justifyContent: 'center',
  alignItems: 'center',
    marginBottom: 24,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 }, ,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 2
  },
  header: { alignItems: 'center',
    marginBottom: 40 },
  iconContainer: {
    width: 64,
  height: 64,
    borderRadius: 32,
  justifyContent: 'center',
    alignItems: 'center',
  marginBottom: 20,
    shadowOffset: { width: 0, height: 4 } ,
  shadowOpacity: 0.3,
    shadowRadius: 8,
  elevation: 5
  },
  iconDefault: {
    backgroundColor: theme.colors.primary[500],
  shadowColor: theme.colors.primary[500] }
  iconSuccess: {
    backgroundColor: theme.colors.success[500],
  shadowColor: theme.colors.success[500] }
  iconError: {
    backgroundColor: theme.colors.error[500],
  shadowColor: theme.colors.error[500] }
  title: { fontSize: 28,
    fontWeight: '700',
  color: theme.colors.gray[900],
    marginBottom: 8 },
  subtitle: {
    fontSize: 16,
  color: theme.colors.gray[600],
    textAlign: 'center' }
  formContainer: {
    backgroundColor: theme.colors.white,
  borderRadius: 16,
    padding: 24,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 8,
  elevation: 2
  },
  form: { gap: 20 }
  strengthContainer: { marginTop: 8,
    marginBottom: 8 },
  strengthLabel: { fontSize: 14,
    color: theme.colors.gray[600],
  marginBottom: 4 }
  strengthBarContainer: { height: 8,
    backgroundColor: theme.colors.gray[200],
  borderRadius: 4,
    overflow: 'hidden',
  marginBottom: 4 }
  strengthBar: { height: '100%',
    borderRadius: 4 },
  strengthText: {
    fontSize: 14,
  fontWeight: '600',
    textAlign: 'right' }
  passwordRequirements: { backgroundColor: theme.colors.gray[50],
    borderRadius: 8,
  padding: 12,
    marginTop: 16 },
  requirementRow: { flexDirection: 'row',
    alignItems: 'center',
  marginTop: 4,
    gap: 8 },
  passwordHint: { fontSize: 14,
    color: theme.colors.gray[600],
  lineHeight: 20 }
  requirementMet: {
    color: theme.colors.success[500] }
  requirementFailed: {
    color: theme.colors.error[500] }
  button: { marginTop: 20 },
  errorText: {
    color: theme.colors.error[500],
  fontSize: 14,
    marginTop: 4,
  textAlign: 'center'
  },
  loadingContainer: { alignItems: 'center',
    padding: 24 },
  loadingText: {
    fontSize: 16,
  color: theme.colors.gray[600],
    marginTop: 16,
  textAlign: 'center'
  },
  errorContainer: { alignItems: 'center',
    padding: 16,
  gap: 16 }
  successContainer: { alignItems: 'center',
    padding: 16,
  gap: 16 }
  successTitle: { fontSize: 22,
    fontWeight: '700',
  color: theme.colors.gray[900],
    marginTop: 16 },
  successText: {
    fontSize: 14,
  color: theme.colors.gray[600]),
    textAlign: 'center'),
  marginBottom: 16)
  },
  })