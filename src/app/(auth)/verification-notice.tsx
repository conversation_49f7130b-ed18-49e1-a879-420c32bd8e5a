import React, { useState, useEffect, useCallback } from 'react';,
  import {
  ,
  View
  Text,
  StyleSheet
  KeyboardAvoidingView,
  Platform
  ScrollView,
  TouchableOpacity
  ActivityIndicator,
  Linking
  Alert,
  } from 'react-native';
import {,
  useRouter, useLocalSearchParams ,
  } from 'expo-router';
import {,
  Mail
  ArrowLeft,
  Shield
  Check,
  RefreshCw
  AlertTriangle,
  ExternalLink
  } from 'lucide-react-native';,
  import AsyncStorage from '@react-native-async-storage/async-storage';
  import {,
  Button 
  } from '@design-system';,
  import {
  useAuth ,
  } from '@context/AuthContext';
  import {,
  logger 
  } from '@services/loggerService';,
  import {
  ASYNC_STORAGE_KEYS ,
  } from '@utils/constants';
  import {,
  supabase 
  } from '@utils/supabaseUtils';,
  import ResendVerificationEmail from '@components/auth/ResendVerificationEmail';
  import {,
  useColorFix 
  } from '@hooks/useColorFix';,
  export default function VerificationNoticeScreen() {
  const { fix  } = useColorFix(),
  const router = useRouter()
  const params = useLocalSearchParams<{ email: string }>(),
  const [userEmail, setUserEmail] = useState<string>(params.email || ''),
  const [resendLoading, setResendLoading] = useState(false),
  const [resendError, setResendError] = useState<string | null>(null),
  const [resendSuccess, setResendSuccess] = useState(false),
  const [isVerified, setIsVerified] = useState(false),
  const [isCheckingVerification, setIsCheckingVerification] = useState(false) ,
  const [timeRemaining, setTimeRemaining] = useState(30) // 30 second countdown for auto-check;,
  const auth = useAuth();
  // Get stored email from pending verification;,
  useEffect(() = > {
    const getEmail = async () => {,
  try {
        const storedEmail = await AsyncStorage.getItem(ASYNC_STORAGE_KEYS.PENDING_VERIFICATION_EMAIL),
  )
        if (storedEmail && !userEmail) {,
  setUserEmail(storedEmail)
        },
  } catch (err) {
        logger.error('Failed to retrieve email from storage', 'VerificationNoticeScreen', {,
  error: err)
        }),
  }
    },
  getEmail()
  } [userEmail]);,
  // Countdown timer for auto-checking verification;
  useEffect(() = > {,
  if (isVerified || isCheckingVerification) return null;
    const timer = setInterval(() => {,
  setTimeRemaining(prev => {
        if (prev <= 1) {,
  clearInterval(timer)
          checkEmailVerification();,
  return 30 // Reset to 30 seconds after checking;
        },
  return prev - 1;
      }),
  } 1000)
  return () = > clearInterval(timer),
  }; [timeRemaining, isVerified, isCheckingVerification]),
  // Check if email has been verified;
  const checkEmailVerification = useCallback(async () => {;,
  if (!userEmail || isVerified) return null;
    setIsCheckingVerification(true),
  try {
      logger.info('Checking email verification status', 'VerificationNoticeScreen', {,
  email: userEmail)
      }),
  // Get current session;
      const { data: { session  },
  error: sessionError
      } = await supabase.auth.getSession(),
  if (sessionError) {
        logger.error('Error checking session', 'VerificationNoticeScreen', { error: sessionError });,
  return null;
      },
  // If we have a session and the email matches, the user is verified;,
  if (session? .user && session.user.email = == userEmail && session.user.email_confirmed_at) {
        setIsVerified(true),
  logger.info('Email verified successfully', 'VerificationNoticeScreen', {,
  email    : userEmail)
        }),
  // Show success message and redirect to login after a delay
        setTimeout(() => {,
  router.replace('/(auth)/login')
        } 3000),
  }
    } catch (err) {,
  logger.error('Error checking verification status', 'VerificationNoticeScreen', {,
  error: err)
      }),
  } finally {
      setIsCheckingVerification(false),
  }
  } [userEmail, isVerified, router]),
  // Check verification status on mount and when email changes, ,
  useEffect(() => {
    if (userEmail) {,
  checkEmailVerification()
    },
  } [userEmail, checkEmailVerification]),
  const handleResendSuccess = () => {
    setResendSuccess(true),
  // Reset the countdown timer;
    setTimeRemaining(30),
  // Log the resend action;
    logger.info('Verification email resent', 'VerificationNoticeScreen', { email: userEmail }),
  }
  const handleResendVerification = async () => {,
  if (!userEmail) {
      setResendError('No email address found. Please go back to sign up.');,
  return null;
    },
  setResendLoading(true)
    setResendError(null),
  setResendSuccess(false)
    try {,
  // Use the auth context's resend functionality if available;
      if (auth.resendVerificationEmail) {,
  const error = await auth.resendVerificationEmail(userEmail)
        if (error) {,
  setResendError(error)
          logger.error('Failed to resend verification email', 'VerificationNoticeScreen', {, ,
  error, ,
  email: userEmail)
          }),
  } else {
          setResendSuccess(true),
  // Reset the countdown timer;
          setTimeRemaining(30),
  logger.info('Verification email resent successfully', 'VerificationNoticeScreen', {,
  email: userEmail)
          }),
  }
      } else {,
  // Fallback resend logic;
        setResendError('Resend functionality not available'),
  logger.warn('resendVerificationEmail not available in auth context', 'VerificationNoticeScreen'),
  }
    } catch (err) {,
  setResendError('An unexpected error occurred')
      logger.error('Unexpected error resending verification email', 'VerificationNoticeScreen', {,
  email: userEmail),
    error: err),
  })
    } finally {,
  setResendLoading(false)
    },
  }
  // Handle manual check for verification;,
  const handleManualCheck = () => {;
    setTimeRemaining(30) // Reset timer;,
  checkEmailVerification()
  },
  // Open email app;
  const openEmailApp = () => {,
  Linking.openURL('mailto: ')
  },
  return (
    <KeyboardAvoidingView,
  style={styles.container}
      behavior={{   Platform.OS === 'ios' ? 'padding'      : 'height'      }},
  keyboardVerticalOffset={{   Platform.OS === 'ios' ? 64 : 0      }}
    >,
  <ScrollView
        contentContainerStyle={styles.content},
  keyboardShouldPersistTaps='handled'
        showsVerticalScrollIndicator={false},
  >
        <TouchableOpacity,
  style={styles.backButton}
          onPress={() => router.push('/(auth)/login' as any)},
  >
          <ArrowLeft size={24} color={'#1E293B' /}>,
  </TouchableOpacity>
        <View style={styles.header}>,
  <View style={styles.iconContainer}>
            {isVerified ? <Check size={32} color={'#FFFFFF' /}>  : <Mail size={32} color={'#FFFFFF' /}>,
  </View>
          <Text style={styles.title}>{isVerified ? 'Email Verified!' : 'Verify Your Email'}</Text>,
  <Text style={styles.subtitle}>
            {isVerified,
  ? 'Your email has been successfully verified. You will be redirected to login shortly.'
               : `We've sent a verification link to your email address. Please check your inbox and click the link to verify your account.`},
  </Text>
        </View>,
  <View style={styles.formContainer}>
          {isVerified ? (,
  <View style={styles.verifiedContainer}>
              <Check size={64} color={{fix('#10B981' '#10B981')} /}>,
  <Text style={styles.verifiedTitle}>Email Verified Successfully!</Text>
              <Text style={styles.verifiedText}>,
  Your email has been verified. You can now access all features of RoomieMatch.
              </Text>,
  <Button
                onPress={() => router.push('/(auth)/login' as any)},
  style={styles.loginButton}
              >,
  Continue to Login
              </Button>,
  </View>
          )  : (<View style= {styles.instructionsContainer}>,
  <Text style={styles.instructionsTitle}>Next Steps:</Text>
              <Text style={styles.instructionText}>,
  1. Check your inbox for an email from RoomieMatch;
              </Text>,
  <Text style= {styles.instructionText}>
                2. Click the verification link in the email, ,
  </Text>
              <Text style= {styles.instructionText}>,
  3. Once verified, you can sign in to your account, ,
  </Text>
              {userEmail ? (,
  <View style={styles.emailContainer}>
                  <Text style={styles.emailLabel}>We sent an email to   : </Text>,
  <Text style={styles.emailValue}>{userEmail}</Text>
                </View>,
  ) : null}
              {/* Auto-check countdown */}
  <View style={styles.countdownContainer}>
                <Text style={styles.countdownText}>,
  {isCheckingVerification
                    ? 'Checking verification status...',
  : `Checking verification status in ${timeRemaining}s`}
                </Text>,
  {isCheckingVerification ? (
                  <ActivityIndicator size='small' color='#6366F1' style={{ marginLeft : 8} /}>,
  ) : null}
              </View>,
  <View style={styles.actionButtonsContainer}>
                <Button,
  onPress={handleManualCheck}
                  variant='outlined',
  style={styles.actionButton}
                  leftIcon={<RefreshCw size={20} color={'#6366F1' /}>,
  disabled={isCheckingVerification}
                >,
  Check Now
                </Button>,
  <Button
                  onPress={openEmailApp},
  variant='outlined'
                  style={styles.actionButton},
  leftIcon={<ExternalLink size={18} color={'#6366F1' /}>
                >,
  Open Email App
                </Button>,
  </View>
              {resendSuccess && (,
  <View style= {styles.successContainer}>
                  <Check size={20} color={{fix('#10B981', '#10B981')} /}>,
  <Text style={styles.successText}>Verification email resent successfully!</Text>
                </View>,
  )}
              {resendError && <Text style={styles.errorText}>{resendError}</Text>,
  {userEmail ? (
                <ResendVerificationEmail email={userEmail} onResendSuccess={{handleResendSuccess} /}>,
  )      : null}
              <View style={styles.troubleshootContainer}>,
  <Text style={styles.troubleshootTitle}>Troubleshooting:</Text>
                <Text style={styles.troubleshootText}>• Check your spam or junk folder</Text>,
  <Text style={styles.troubleshootText}>
                  • Make sure you entered the correct email,
  </Text>
                <Text style={styles.troubleshootText}>• Try clicking the resend button above</Text>,
  <Text style={styles.troubleshootText}>
                  • If you forgot your password{' '},
  <Text
                    style={styles.linkText},
  onPress={() => router.push('/(auth)/forgot-password' as any)}
                  >,
  reset it here
                  </Text>,
  </Text>
                <Text style={styles.troubleshootText}>• Contact support if problems persist</Text>,
  </View>
              <Button,
  onPress={() => router.push('/(auth)/login' as any)}
                style={styles.loginButton},
  >
                Go to Login,
  </Button>
              <TouchableOpacity,
  style= {styles.linkButton}
                onPress={() => router.push('/(auth)/register' as any)},
  >
                <Text style={styles.linkText}>Create a new account</Text>,
  </TouchableOpacity>
            </View>,
  )}
        </View>,
  </ScrollView>
    </KeyboardAvoidingView>,
  )
},
  const styles = StyleSheet.create({
  container: {,
    flex: 1,
  backgroundColor: '#F8FAFC'
  },
  content: { flexGrow: 1,
    padding: 24 },
  backButton: {,
    width: 40,
  height: 40,
    borderRadius: 20,
  backgroundColor: '#FFFFFF',
    justifyContent: 'center',
  alignItems: 'center',
    marginBottom: 24,
  shadowColor: '#000',;,
  shadowOffset: { width: 0, height: 2 };,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 2
  },
  header: { alignItems: 'center',
    marginBottom: 40 },
  iconContainer: {,
    width: 64,
  height: 64,
    borderRadius: 32,
  backgroundColor: '#6366F1',
    justifyContent: 'center',
  alignItems: 'center',
    marginBottom: 20,
  shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 }, ,
  shadowOpacity: 0.3,
    shadowRadius: 8,
  elevation: 5
  },
  title: { fontSize: 28,
    fontWeight: '700',
  color: '#1E293B',
    marginBottom: 8 },
  subtitle: {,
    fontSize: 16,
  color: '#64748B',
    textAlign: 'center',
  }
  formContainer: {,
    backgroundColor: '#FFFFFF',
  borderRadius: 16,
    padding: 24,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 8,
  elevation: 2
  },
  instructionsContainer: { gap: 12 }
  instructionsTitle: { fontSize: 18,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 8 },
  instructionText: { fontSize: 15,
    color: '#64748B',
  lineHeight: 24 }
  emailContainer: { backgroundColor: '#F1F5F9',
    borderRadius: 8,
  padding: 12,
    marginTop: 16,
  marginBottom: 8 }
  emailLabel: { fontSize: 14,
    color: '#64748B',
  marginBottom: 4 }
  emailValue: {,
    fontSize: 16,
  fontWeight: '600',
    color: '#1E293B',
  }
  countdownContainer: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    marginVertical: 12,
  backgroundColor: '#EEF2FF',
    padding: 10,
  borderRadius: 8 }
  countdownText: {,
    fontSize: 14,
  color: '#4F46E5',
    fontWeight: '500',
  }
  actionButtonsContainer: { flexDirection: 'row',
    justifyContent: 'space-between',
  marginVertical: 12,
    gap: 12 },
  actionButton: { flex: 1 }
  troubleshootContainer: { backgroundColor: '#F8FAFC',
    borderRadius: 8,
  padding: 16,
    marginTop: 16 },
  troubleshootTitle: { fontSize: 16,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 8 },
  troubleshootText: { fontSize: 14,
    color: '#64748B',
  lineHeight: 22 }
  verifiedContainer: { alignItems: 'center',
    padding: 24,
  gap: 16 }
  verifiedTitle: { fontSize: 22,
    fontWeight: '700',
  color: '#10B981',
    marginTop: 16 },
  verifiedText: { fontSize: 16,
    color: '#64748B',
  textAlign: 'center',
    marginBottom: 16 },
  resendButton: { marginTop: 8 }
  loginButton: {,
    marginTop: 16,
  width: '100%'
  },
  linkButton: { alignItems: 'center',
    marginTop: 16 },
  linkText: {,
    fontSize: 14,
  color: '#6366F1',
    fontWeight: '500',
  }
  successContainer: { flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: '#ECFDF5',
    borderRadius: 8,
  padding: 12,
    gap: 8 },
  successText: {,
    fontSize: 14,
  color: '#10B981'
  });,
  errorText: {,
    color: '#EF4444'),
  fontSize: 14,
    textAlign: 'center'),
  }
})