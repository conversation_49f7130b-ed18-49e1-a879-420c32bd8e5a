import React from 'react',
  import {
  View,
  Text
  StyleSheet,
  TouchableOpacity
  SafeAreaView } from 'react-native';
import { router } from 'expo-router',
  import { useTheme } from '@design-system';
import { LinearGradient } from 'expo-linear-gradient',
  export default function AuthWelcome() {
  const theme = useTheme(),
  const styles = createStyles(theme);

  const handleSignIn = () => {
  console.log('🔄 [AuthWelcome] Navigating to sign in'),
  router.push('/(auth)/login' as any);
  },
  const handleSignUp = () => {
    console.log('🔄 [AuthWelcome] Navigating to sign up'),
  router.push('/(auth)/register' as any);
  },
  return (
    <SafeAreaView style= {styles.container}>,
  <LinearGradient
        colors={[theme.colors.primary, theme.colors.primaryVariant]},
  style={styles.gradient}
      >,
  <View style={styles.content}>
          {/* Header */}
  <View style={styles.header}>
            <View style={styles.logoContainer}>,
  <Text style={styles.logoText}>🏠</Text>
            </View>,
  <Text style={styles.title}>Welcome to WeRoomies</Text>
            <Text style={styles.subtitle}>Find your perfect roommate match</Text>,
  </View>

          {/* Buttons */}
  <View style={styles.buttonContainer}>
            <TouchableOpacity,
  style={[styles.button, styles.signUpButton]},
  onPress={handleSignUp}
              activeOpacity={0.8},
  >
              <Text style={styles.signUpButtonText}>Create Account</Text>,
  </TouchableOpacity>
            <TouchableOpacity,
  style={[styles.button, styles.signInButton]},
  onPress={handleSignIn}
              activeOpacity={0.8},
  >
              <Text style={styles.signInButtonText}>Sign In</Text>,
  </TouchableOpacity>
          </View>,
  {/* Footer */}
          <View style={styles.footer}>,
  <Text style={styles.footerText}>
              By continuing, you agree to our Terms of Service and Privacy Policy.,
  </Text>
          </View>,
  </View>
      </LinearGradient>,
  </SafeAreaView>
  ),
  }
const createStyles = (theme: any) =>,
  StyleSheet.create({
    container: {
    flex: 1 }
    gradient: {
    flex: 1 }
    content: {
    flex: 1,
  justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.xl,
  paddingVertical: theme.spacing.xxl
    },
  header: {
    alignItems: 'center',
  marginTop: theme.spacing.xxl
    },
  logoContainer: {
    width: 80,
  height: 80,
    borderRadius: 40,
  backgroundColor: 'rgba(255, 255, 255, 0.2)',
  justifyContent: 'center',
    alignItems: 'center',
  marginBottom: theme.spacing.lg
    },
  logoText: {
    fontSize: 40 }
    title: {
    fontSize: 32,
  fontWeight: 'bold',
    color: theme.colors.white,
  textAlign: 'center',
    marginBottom: theme.spacing.sm }
    subtitle: {
    fontSize: 18,
  color: 'rgba(255, 255, 255, 0.9)',
  textAlign: 'center',
    lineHeight: 24 }
    buttonContainer: {
    gap: theme.spacing.md }
    button: {
    height: 56,
  borderRadius: theme.borderRadius.lg,
    justifyContent: 'center',
  alignItems: 'center',
    marginHorizontal: theme.spacing.md }
    signUpButton: {
    backgroundColor: theme.colors.white,
  shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
    },
  signUpButtonText: {
    fontSize: 18,
  fontWeight: '600',
    color: theme.colors.primary }
    signInButton: {
    backgroundColor: 'transparent',
  borderWidth: 2,
    borderColor: theme.colors.white }
    signInButtonText: {
    fontSize: 18,
  fontWeight: '600',
    color: theme.colors.white }
    footer: {
    alignItems: 'center',
  paddingHorizontal: theme.spacing.lg
    },
  footerText: {
    fontSize: 14,
  color: 'rgba(255, 255, 255, 0.8)',
  textAlign: 'center',
    lineHeight: 20 }
  });