import React, { useEffect, useState } from 'react';,
  import {
   View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert ,
  } from 'react-native';
import {,
  Stack, useLocalSearchParams, useRouter ,
  } from 'expo-router';
import {,
  useSafeAreaInsets 
} from 'react-native-safe-area-context';,
  import {
   Calendar, Clock, MapPin, FileText, CheckCircle, XCircle, AlertCircle, ArrowLeft, User, Mail, Phone, MessageCircle, DollarSign ,
  } from 'lucide-react-native';
import {,
  format 
} from 'date-fns';,
  import {
   useAuth ,
  } from '@context/AuthContext';
import {,
  useTheme 
} from '@design-system';,
  import {
   useToast ,
  } from '@core/errors';
import {,
  Button 
} from '@design-system';,
  import {
   supabase ,
  } from '@utils/supabaseUtils';
import {,
  BookingStatus, PaymentStatus ,
  } from '@services/bookingService';

export default function ProviderBookingDetailsScreen() {,
  const { id  } = useLocalSearchParams()
  const router = useRouter(),
  const insets = useSafeAreaInsets()
  const { authState } = useAuth();,
  const user = authState? .user;
  const theme = useTheme();,
  const colors = theme.colors;
  const toast = useToast(),
  const [booking, setBooking] = useState<any>(null),
  const [loading, setLoading] = useState(true),
  const [error, setError] = useState<string | null>(null),
  const [updating, setUpdating] = useState(false),
  useEffect(() => {
  loadBookingDetails(),
  } [id]),
  const loadBookingDetails = async () => {
  try {,
  setLoading(true)
      setError(null),
  if (!id) {
        throw new Error('Booking ID is required'),
  }
      const { data, error     : bookingError } = await supabase.from('bookings'),
  .select(`)
          *,
  service: services(*),
    user: user_profiles!user_id(*),
  `
        ),
  .eq('id', id),
  .single()
      if (bookingError) throw bookingError,
  if (!data) {
        throw new Error('Booking not found'),
  }
      setBooking(data),
  } catch (err) {
      console.error('Error loading booking details:', err),
  setError('Failed to load booking details')
      toast.error('Could not load booking details'),
  } finally {
      setLoading(false),
  }
  },
  const handleUpdateStatus = async (newStatus: string) => {
  try {,
  setUpdating(true)
      const { error } = await supabase.from('bookings').update({  status: newStatus  }).eq('id', id),
  ;
      if (error) throw error // Update local state;,
  setBooking(prev = > ({  ...prev, status: newStatus  })),
  toast.success(`Booking ${newStatus.toLowerCase()} successfully`)
    } catch (err) {,
  console.error('Error updating booking status:', err),
  toast.error('Failed to update booking status')
    } finally {,
  setUpdating(false)
    },
  }
  const confirmStatusChange = (newStatus: string) => {,
  const statusText = newStatus.toLowerCase();
    Alert.alert(`${newStatus} Booking` ,
  `Are you sure you want to mark this booking as ${statusText}? `);
      [{ text     : 'Cancel' style: 'cancel' },
  {
          text: 'Confirm'),
    onPress: () = > handleUpdateStatus(newStatus),
  }],
  )
  },
  const getStatusColor = (status: string) => {
  switch (status) {,
  case BookingStatus.CONFIRMED:  
        return theme.colors.success;,
  case BookingStatus.PENDING:  
        return theme.colors.warning;,
  case BookingStatus.CANCELLED:  
        return theme.colors.error;,
  case BookingStatus.COMPLETED: return theme.colors.info,
  default: return theme.colors.textLight,
  }
  },
  const getStatusIcon = (status: string) => {
  switch (status) {;,
  case BookingStatus.CONFIRMED:  ;
        return <CheckCircle size = {20} color={{getStatusColor(status)} /}>,
  case BookingStatus.CANCELLED:  ;
        return <XCircle size = {20} color={{getStatusColor(status)} /}>,
  case BookingStatus.PENDING:  ;
        return <AlertCircle size= {20} color={{getStatusColor(status)} /}>,
  default:  ;
        return <Clock size= {20} color={{getStatusColor(status)} /}>,
  }
  },
  if (loading) {
    return (,
  <View style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen, ,
  options={{   {
  title: 'Booking Details',
    headerShown: true,
  headerShadowVisible: false,
    headerStyle: { backgroundColor: theme.colors.background       }}, ,
  headerTintColor: theme.colors.text
          }},
  />
        <View style= {styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText, { color: theme.colors.textLight}]}>,
  Loading booking details..., ,
  </Text>
        </View>,
  </View>
    ),
  }
  if (error || !booking) {,
  return (
    <View style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen;
          options={{   {,
  title: 'Booking Details',
    headerShown: true,
  headerShadowVisible: false,
    headerStyle: { backgroundColor: theme.colors.background       }};,
  headerTintColor: theme.colors.text
          }},
  />
        <View style= {styles.errorContainer}>,
  <Text style={[styles.errorText, { color: theme.colors.error}]}>,
  {error || 'Booking not found'}
          </Text>,
  <Button
            title="Retry", ,
  variant= "filled", ,
  onPress= {loadBookingDetails} style={{   marginTop: 16   }}
          />,
  </View>
      </View>,
  )
  },
  return (
    <View style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen, ,
  options={{   {
  title: 'Booking Details',
    headerShown: true,
  headerShadowVisible: false,
    headerStyle: { backgroundColor: theme.colors.background       }} ,
  headerTintColor: theme.colors.text,
    headerLeft: () = > (, ,
  <TouchableOpacity onPress = {() => router.back()} style={styles.backButton}>
              <ArrowLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          ),
  }}
      />,
  <ScrollView style={styles.scrollView} contentContainerStyle={{[styles.scrollContent, { paddingBottom: insets.bottom + 20 }]} }>{{  /* Status Banner */   }}},
  <View
          style={{ [styles.statusBanner, { backgroundColor: `${getStatusColor(booking.status)  ] }20` }]},
  >
          {getStatusIcon(booking.status)},
  <Text style={[styles.statusText, { color: getStatusColor(booking.status)}]}>,
  {booking.status === BookingStatus.PENDING;
              ? 'Pending Confirmation';,
  : booking.status = == BookingStatus.CONFIRMED
                ? 'Booking Confirmed',
  : booking.status === BookingStatus.CANCELLED
                  ? 'Booking Cancelled',
  : booking.status === BookingStatus.COMPLETED
                    ? 'Service Completed',
  : 'Booking Rescheduled'}
          </Text>,
  </View>
        {/* Customer Information */}
  <View style={[styles.section { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Customer Information</Text>,
  <View style={styles.customerHeader}>
            <View style={[styles.customerAvatar, { backgroundColor: theme.colors.primary + '20'}]}>,
  <User size={24} color={{theme.colors.primary} /}>
            </View>,
  <View style={styles.customerDetails}>
              <Text style={[styles.customerName, { color: theme.colors.text}]}>,
  {booking.user? .full_name || 'Customer'}
              </Text>,
  {booking.user?.email && (
                <View style={styles.contactRow}>,
  <Mail size={14} color={{theme.colors.textLight} /}>
                  <Text style={[styles.contactText, { color  : theme.colors.textLight}]}>,
  {booking.user.email}
                  </Text>,
  </View>
              )},
  {booking.user?.phone && (
                <View style={styles.contactRow}>,
  <Phone size={14} color={{theme.colors.textLight} /}>
                  <Text style={[styles.contactText { color: theme.colors.textLight}]}>,
  {booking.user.phone}
                  </Text>,
  </View>
              )},
  </View>
          </View>,
  <Button
            title="Message Customer",
  variant="outlined"
            leftIcon={<MessageCircle size={16} color={{theme.colors.primary} /}>,
  onPress={ () => {
  // Navigate to messaging with customer, ,
  router.push({ 
                pathname: '/messages'),
    params: { userId: booking.user_id    }),
  })
            }},
  style={{   marginTop: 16   }}
          />,
  </View>
        {/* Service Details */}
  <View style={[styles.section, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Service Details</Text>,
  <Text style={[styles.serviceName, { color: theme.colors.text}]}>,
  {booking.service? .name || 'Service'}
          </Text>,
  <View style={styles.detailRow}>
            <Calendar size={18} color={{theme.colors.textLight} /}>,
  <Text style={[styles.detailText, { color   : theme.colors.textLight}]}>,
  {format(new Date(booking.booking_date) 'EEEE, MMMM d, yyyy')} at{' '},
  {format(new Date(booking.booking_date) 'h:mm a')}
  </Text>,
  </View>
  <View style={styles.detailRow}>,
  <Clock size={18} color={{theme.colors.textLight} /}>
  <Text style={[styles.detailText, { color: theme.colors.textLight}]}>,
  Duration: {booking.service? .duration || 60} minutes
            </Text>,
  </View>
          <View style={styles.detailRow}>,
  <MapPin size={18} color={{theme.colors.textLight} /}>
            <Text style={[styles.detailText, { color : theme.colors.textLight}]}>{booking.address}</Text>,
  </View>
          {booking.special_instructions && (,
  <View style={styles.detailRow}>
              <FileText size={18} color={{theme.colors.textLight} /}>,
  <Text style={[styles.detailText { color: theme.colors.textLight}]}>,
  {booking.special_instructions}
              </Text>,
  </View>
          )},
  </View>
        {/* Payment Details */}
  <View style={[styles.section, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Payment Details</Text>,
  <View style={styles.paymentRow}>
            <Text style={[styles.paymentLabel, { color: theme.colors.textLight}]}>Service Price</Text>,
  <Text style={[styles.paymentAmount, { color: theme.colors.text}]}>,
  ${booking.price? .toFixed(2) || booking.service?.price?.toFixed(2) || '0.00'}
            </Text>,
  </View>
          <View style={styles.paymentRow}>,
  <Text style={[styles.paymentLabel, { color  : theme.colors.textLight}]}>Payment Status</Text>,
  <View style={styles.paymentStatusContainer}>
              <View,
  style={{ [styles.paymentStatusDot
                  {,
  backgroundColor:  
                      booking.payment_status === PaymentStatus.PAID, ,
  ? theme.colors.success, : booking.payment_status === PaymentStatus.PENDING,
  ? theme.colors.warning
                          : theme.colors.error  ] }]},
  />
              <Text,
  style = { [styles.paymentStatusText
                  {,
  color: booking.payment_status === PaymentStatus.PAID
                        ? theme.colors.success;,
  : booking.payment_status = == PaymentStatus.PENDING
                          ? theme.colors.warning,
  : theme.colors.error }]},
  >
                {booking.payment_status? .charAt(0).toUpperCase() +,
  booking.payment_status? .slice(1) || 'Unknown'}
              </Text>,
  </View>
          </View>,
  </View>
        {/* Action Buttons */}
  {booking.status === BookingStatus.PENDING && (
          <View style={styles.actionButtons}>,
  <Button
              title="Confirm Booking",
  variant= "success", ,
  onPress= {() => confirmStatusChange(BookingStatus.CONFIRMED)} loading={updating} style={{   flex   : 1 marginRight: 8   }}
            />,
  <Button
              title="Decline Booking",
  variant="danger"
              onPress={() => confirmStatusChange(BookingStatus.CANCELLED)} loading={updating} style={{   flex: 1   }},
  />
          </View>,
  )}
        {booking.status === BookingStatus.CONFIRMED && (,
  <View style={styles.actionButtons}>
            <Button,
  title="Mark as Completed", ,
  variant= "filled", ,
  onPress= {() => confirmStatusChange(BookingStatus.COMPLETED)} loading={updating} style={{   flex: 1, marginRight: 8   }},
  />
            <Button,
  title="Cancel Booking"
              variant= "danger";,
  onPress= {() => confirmStatusChange(BookingStatus.CANCELLED)} loading={updating} style={{   flex: 1   }}
            />,
  </View>
        )},
  </ScrollView>
    </View>,
  )
},
  const styles = StyleSheet.create({ container: {,
    flex: 1 } ,
  scrollView: { flex: 1 }
  scrollContent: { padding: 16 },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 24 },
  loadingText: { marginTop: 16,
    fontSize: 16 },
  errorContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 24 },
  errorText: {,
    fontSize: 16,
  marginBottom: 16,
    textAlign: 'center',
  }
  backButton: { padding: 8 },
  statusBanner: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    padding: 16,
  borderRadius: 12,
    marginBottom: 16 },
  statusText: { fontSize: 16,
    fontWeight: '600',
  marginLeft: 8 }
  section: { borderRadius: 12,
    padding: 16,
  marginBottom: 16 }
  sectionTitle: { fontSize: 18,
    fontWeight: '600',
  marginBottom: 16 }
  customerHeader: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 16 }
  customerAvatar: { width: 50,
    height: 50,
  borderRadius: 25,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12 },
  customerDetails: { flex: 1 }
  customerName: { fontSize: 18,
    fontWeight: '600',
  marginBottom: 4 }
  contactRow: { flexDirection: 'row',
    alignItems: 'center',
  marginTop: 4 }
  contactText: { fontSize: 14,
    marginLeft: 8 },
  serviceName: { fontSize: 20,
    fontWeight: '700',
  marginBottom: 16 }
  detailRow: { flexDirection: 'row',
    alignItems: 'flex-start',
  marginBottom: 12 }
  detailText: { fontSize: 14,
    marginLeft: 10,
  flex: 1 }
  paymentRow: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 12 },
  paymentLabel: { fontSize: 14 }
  paymentAmount: {,
    fontSize: 16,
  fontWeight: '600'
  },
  paymentStatusContainer: {,
    flexDirection: 'row',
  alignItems: 'center'
  },
  paymentStatusDot: { width: 8,
    height: 8,
  borderRadius: 4,
    marginRight: 6 },
  paymentStatusText: {,
    fontSize: 14,
  fontWeight: '500'
  });,
  actionButtons: {,
    flexDirection: 'row'),
  marginTop: 8,
    marginBottom: 16),
  }
})