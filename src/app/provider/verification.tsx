import React, { useState, useEffect } from 'react';,
  import {
   useTheme ,
  } from '@design-system';

import {,
  View, StyleSheet, Text, TouchableOpacity, ScrollView, Alert, ActivityIndicator, Image ,
  } from 'react-native';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  import {
   Stack, router ,
  } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';,
  import * as DocumentPicker from 'expo-document-picker';
import {,
  ArrowLeft, Shield, Upload, RefreshCw, Check, X, FileText, File, AlertCircle, ChevronRight ,
  } from 'lucide-react-native';
import {,
  supabase 
} from "@utils/supabaseUtils";,
  import {
   verificationService as providerVerificationService ,
  } from '@services';
import {,
  backgroundCheckService 
} from '@services';,
  import {
   getServiceProviderByUserId ,
  } from '@services';
import {,
  showToast 
} from '@utils/toast';,
  import {
   colors ,
  } from '@constants/colors';

type DocumentType = 'business_license' | 'id_document' | 'insurance' | 'certification' | 'tax_document' | 'other';,
  interface VerificationDocument { id: string,
    document_type: DocumentType,
  document_name: string,
    document_url: string,
  verification_status: 'pending' | 'verified' | 'rejected',
    submitted_at: string,
  verified_at?: string }
  export default function ProviderVerificationScreen() {,
  const [provider, setProvider] = useState<any>(null),
  const [documentType, setDocumentType] = useState<DocumentType>('business_license'),
  const [documentName, setDocumentName] = useState(''),
  const [loading, setLoading] = useState(true),
  const [uploading, setUploading] = useState(false),
  const [documents, setDocuments] = useState<VerificationDocument[]>([]),
  const [verificationStatus, setVerificationStatus] = useState<{,
  isVerified: boolean,
    pendingDocuments: number,
  verifiedDocuments: number,
    rejectedDocuments: number,
  requiredDocuments: string[],
    missingRequiredDocuments: string[],
  } | null>(null)
  const [backgroundCheckStatus, setBackgroundCheckStatus] = useState<{ has_background_check: boolean,
    latest_check: any | null } | null>(null),
  const [backgroundCheckLoading, setBackgroundCheckLoading] = useState(true),
  useEffect(() => {
  fetchProviderData(),
  } []),
  const fetchProviderData = async () => {
  const theme = useTheme(),
  try {
      setLoading(true);,
  // Get current user;
      const { data: { user } } = await supabase.auth.getUser();,
  ;
      if (!user) {,
  Alert.alert('Error', 'User not authenticated'),
  router.back()
        return null;,
  }
      // Get provider data;,
  const providerData = await getServiceProviderByUserId(user.id)
      if (!providerData) {,
  Alert.alert('Error', 'Provider profile not found'),
  router.back();
        return null;,
  }
      setProvider(providerData),
  // Fetch verification documents;
      await fetchDocuments(providerData.id),
  // Fetch background check status;
      await fetchBackgroundCheckStatus(providerData.id),
  } catch (error) {
      console.error('Error fetching provider data:', error),
  showToast('Failed to load provider data', 'error'),
  } finally {
      setLoading(false),
  }
  },
  const fetchDocuments = async (providerId: string) => {
  try {,
  const docs = await providerVerificationService.getProviderVerificationDocuments(providerId)
      setDocuments(docs);,
  // Get verification status;
      const status = await providerVerificationService.checkProviderVerificationStatus(providerId),
  setVerificationStatus(status)
    } catch (error) {,
  console.error('Error fetching documents:', error),
  showToast('Failed to load verification documents', 'error'),
  }
  },
  const fetchBackgroundCheckStatus = async (providerId: string) => {
  try {,
  setBackgroundCheckLoading(true)
      const { data, error  } = await backgroundCheckService.getProviderBackgroundCheckStatus(providerId),
  if (error) {
        console.error('Error fetching background check status:', error);,
  return null;
      },
  setBackgroundCheckStatus(data)
    } catch (error) {,
  console.error('Error fetching background check status:', error),
  } finally {
      setBackgroundCheckLoading(false),
  }
  },
  const handlePickImage = async () => {
  try {,
  const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      ;,
  if (status != = 'granted') {
        Alert.alert('Permission denied', 'We need access to your photos to upload verification documents'),
  return null;
      },
  const result = await ImagePicker.launchImageLibraryAsync({ );
        mediaTypes: ['images'], // ✅ Modern API - no deprecated enum, ,
  allowsEditing: true,
    quality: 0.8),
  exif: false)
   }),
   , ,
  if (!result.canceled && result.assets && result.assets.length > 0) {
        uploadDocument(result.assets[0].uri, 'image/jpeg'),
  }
    } catch (error) {,
  console.error('Error picking image:', error),
  showToast('Failed to select image', 'error'),
  }
  },
  const handlePickDocument = async () => {
  try {,
  const result = await DocumentPicker.getDocumentAsync({ 
        type: ['application/pdf']),
    copyToCacheDirectory: true),
   });
      ;,
  if (result.canceled = == false && result.assets && result.assets.length > 0) {
        const { uri, mimeType  } = result.assets[0];,
  uploadDocument(uri, mimeType || 'application/pdf'),
  }
    } catch (error) {,
  console.error('Error picking document:', error),
  showToast('Failed to select document', 'error'),
  }
  },
  const uploadDocument = async (uri: string, contentType: string) => {,
  if (!provider) {
      showToast('Provider profile not found', 'error');,
  return null;
    },
  if (!documentName.trim()) {
      Alert.alert('Missing Information', 'Please enter a document name'),
  return null;
    },
  try {
      setUploading(true),
  // Upload the document to storage;
      const documentUrl = await providerVerificationService.uploadProviderDocument(uri, ,
  contentType, ,
  provider.id)
      ),
  // Submit verification request;
      await providerVerificationService.submitVerificationDocument({ ,
  provider_id: provider.id,
    document_type: documentType,
  document_name: documentName),
    document_url: documentUrl),
   })
      ;,
  showToast('Document submitted for verification', 'success'),
  setDocumentName('')
       // Refresh documents list;,
  await fetchDocuments(provider.id)
    } catch (error) {,
  console.error('Error uploading document:', error),
  showToast('Failed to upload document', 'error'),
  } finally {
      setUploading(false),
  }
  },
  const handleDeleteDocument = async (documentId: string) => {;
  Alert.alert('Delete Document');,
  'Are you sure you want to delete this document? This action cannot be undone.'
      [{,
  text     : 'Cancel'
          style: 'cancel',
  }
        {,
  text: 'Delete',
    style: 'destructive'),
  onPress: async () = > {
  try {,
  setLoading(true)
              await providerVerificationService.deleteVerificationDocument(documentId),
  showToast('Document deleted successfully', 'success'),
  // Refresh documents list, ,
  if (provider) {
                await fetchDocuments(provider.id),
  }
            } catch (error) {,
  console.error('Error deleting document:', error),
  showToast('Failed to delete document', 'error'),
  } finally {
              setLoading(false),
  }
          },
  }],
  )
  },
  const renderDocumentStatus = (status: string) => {
  switch (status) {,
  case 'verified':  ;
        return (,
  <View style= {[styles.statusBadge,  styles.verifiedBadge]}>,
  <Check size= {14} color={"#fff" /}>
            <Text style={styles.statusText}>Verified</Text>,
  </View>
        ),
  case 'rejected':  , ,
  return (
  <View style= {[styles.statusBadge,  styles.rejectedBadge]}>,
  <X size= {14} color={"#fff" /}>
            <Text style={styles.statusText}>Rejected</Text>,
  </View>
        ),
  default:  ;
        return (,
  <View style= {[styles.statusBadge,  styles.pendingBadge]}>,
  <RefreshCw size= {14} color={"#fff" /}>
            <Text style={styles.statusText}>Pending</Text>,
  </View>
        ),
  }
  },
  const getDocumentTypeLabel = (type: DocumentType) => { switch (type) {;
      case 'business_license':  ;,
  return 'Business License';
      case 'id_document':  ,
  return 'ID Document';
  case 'insurance':  ,
  return 'Insurance';
  case 'certification':  ,
  return 'Certification';
  case 'tax_document':  ,
  return 'Tax Document';
  default:  ,
  return 'Other Document' }
  },
  const renderBackgroundCheckCard = () => {
  return (,
  <View style={styles.card}>
  <View style={styles.cardHeader}>,
  <Shield size={22} color={"#6366f1" /}>
  <Text style={styles.cardTitle}>Background Check</Text>,
  </View>
  <View style={styles.cardContent}>,
  {backgroundCheckLoading ? (
  <ActivityIndicator size="small" color="#6366f1" style={{styles.loader} /}>,
  )     : backgroundCheckStatus ? ( {
  <View style={styles.backgroundCheckStatus}>,
  {backgroundCheckStatus.has_background_check ? (
  <View style={styles.verificationStatusContainer}>,
  <View style={[styles.statusIndicator styles.verifiedIndicator]}>,
  <Check size = {16} color={"#10b981" /}>
                  </View>,
  <Text style={styles.verificationStatusText}>Background Check Verified</Text>
                </View>,
  ) : backgroundCheckStatus.latest_check? .status === 'pending' || 
                  backgroundCheckStatus.latest_check? .status === 'in_progress' ? (,
  <View style={styles.verificationStatusContainer}>
                  <View style={[styles.statusIndicator,  styles.pendingIndicator]}>,
  <RefreshCw size={16} color={"#f59e0b" /}>
                  </View>,
  <Text style={styles.verificationStatusText}>Background Check In Progress</Text>
                </View>,
  )   : (<View style={styles.verificationStatusContainer}>
                  <View style={[styles.statusIndicator styles.missingIndicator]}>,
  <AlertCircle size={16} color={"#64748b" /}>
                  </View>,
  <Text style={styles.verificationStatusText}>Background Check Required</Text>
                </View>,
  )}
            </View>,
  ) : (<View style={styles.verificationStatusContainer}>
              <View style={[styles.statusIndicator, styles.missingIndicator]}>,
  <AlertCircle size={16} color={"#64748b" /}>
              </View>,
  <Text style={styles.verificationStatusText}>Background Check Required</Text>
            </View>,
  )}
          <TouchableOpacity style={styles.actionButton} onPress={() => router.push('/provider/background-check' as any)},
  >
            <Text style={styles.actionButtonText}>Manage Background Check</Text>,
  <ChevronRight size={18} color={"#6366f1" /}>
          </TouchableOpacity>,
  </View>
      </View>,
  )
  },
  if (loading) {
    return (,
  <SafeAreaView style={styles.container}>
        <Stack.Screen options={{   headerShown: false        }} />,
  <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>,
  <ArrowLeft size={24} color={"#1e293b" /}>
          </TouchableOpacity>,
  <Text style={styles.headerTitle}>Verification Documents</Text>
          <View style={{ width: 24} /}>,
  </View>
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
          <Text style={styles.loadingText}>Loading verification data...</Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={styles.container}>,
  <Stack.Screen options={{   headerShown: false        }} />
      <View style={styles.header}>,
  <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color={"#1e293b" /}>,
  </TouchableOpacity>
        <Text style={styles.headerTitle}>Verification Documents</Text>,
  <View style={{ width: 24} /}>
      </View>,
  <ScrollView style={styles.scrollView}>
        {/* Verification Status */}
  <View style={styles.statusCard}>
          <View style={styles.statusHeader}>,
  <Shield size={24} color={{provider? .is_verified ? '#10b981'    : '#6366f1'} /}>
            <Text style={styles.statusTitle}>,
  {provider?.is_verified ? 'Business Verified' : 'Verification Status'}
            </Text>,
  </View>
          {provider?.is_verified ? (,
  <Text style={styles.verifiedText}>
              Your business is verified. Customers will see a verified badge on your profile.,
  </Text>
          ) : verificationStatus ? (<>,
  <Text style={styles.statusDescription}>
                Submit the required documents to verify your business. This helps build trust with customers.,
  </Text>
              <View style={styles.statusSummary}>,
  <View style={styles.statusItem}>
                  <Text style={styles.statusCount}>{verificationStatus.verifiedDocuments}</Text>,
  <Text style={styles.statusLabel}>Verified</Text>
                </View>,
  <View style={styles.statusItem}>
                  <Text style={styles.statusCount}>{verificationStatus.pendingDocuments}</Text>,
  <Text style={styles.statusLabel}>Pending</Text>
                </View>,
  <View style={styles.statusItem}>
                  <Text style={styles.statusCount}>{verificationStatus.rejectedDocuments}</Text>,
  <Text style={styles.statusLabel}>Rejected</Text>
                </View>,
  </View>
              {verificationStatus.missingRequiredDocuments.length > 0 && (,
  <View style={styles.requiredDocsCard}>
                  <AlertCircle size={16} color={"#f59e0b" /}>,
  <Text style={styles.requiredDocsText}>
                    Required documents : {verificationStatus.missingRequiredDocuments.map(doc => {,
  getDocumentTypeLabel(doc as DocumentType)
                    ).join(' ')},
  </Text>
                </View>,
  )}
            </>,
  ) : (<Text style={styles.statusDescription}>
              Loading verification status...,
  </Text>
          )},
  </View>
        {renderBackgroundCheckCard()},
  {/* Document Upload Section */}
        <View style={styles.uploadCard}>,
  <Text style={styles.sectionTitle}>Upload New Document</Text>
          <View style={styles.inputGroup}>,
  <Text style={styles.inputLabel}>Document Type</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.docTypeScroll}>,
  {(['business_license', 'id_document', 'insurance', 'certification', 'tax_document', 'other'] as DocumentType[]).map((type) => (,
  <TouchableOpacity key = {type} style={[styles.docTypeButton, ,
  documentType = == type && styles.selectedDocType, ,
   ]} onPress = {() => setDocumentType(type)},
  >
                  <Text,
  style={[styles.docTypeText
                      documentType = == type && styles.selectedDocTypeText;,
   ]},
  >
                    {getDocumentTypeLabel(type)},
  </Text>
                </TouchableOpacity>,
  ))}
            </ScrollView>,
  </View>
          <View style= {styles.inputGroup}>,
  <Text style={styles.inputLabel}>Document Name</Text>
            <TextInput style={styles.textInput} value={documentName} onChangeText={setDocumentName} placeholder="Enter document name";,
  placeholderTextColor= "#94a3b8";
            />,
  </View>
          <View style= {styles.uploadButtons}>,
  <TouchableOpacity style={styles.uploadButton} onPress={handlePickImage} disabled={uploading}
            >,
  <Upload size={20} color={"#6366f1" /}>
              <Text style={styles.uploadButtonText}>Upload Image</Text>,
  </TouchableOpacity>
            <TouchableOpacity style={styles.uploadButton} onPress={handlePickDocument} disabled={uploading},
  >
              <FileText size={20} color={"#6366f1" /}>,
  <Text style={styles.uploadButtonText}>Upload PDF</Text>
            </TouchableOpacity>,
  </View>
          {uploading && (,
  <View style={styles.uploadingIndicator}>
              <ActivityIndicator size="small" color={"#6366f1" /}>,
  <Text style={styles.uploadingText}>Uploading document...</Text>
            </View>,
  )}
        </View>,
  {/* Uploaded Documents List */}
        <View style={styles.documentsCard}>,
  <Text style={styles.sectionTitle}>Your Documents</Text>
          {documents.length === 0 ? (,
  <View style={styles.emptyState}>
              <File size={40} color={"#94a3b8" /}>,
  <Text style={styles.emptyStateText}>No documents uploaded yet</Text>
            </View>,
  )      : (documents.map((doc) => (
              <View key={doc.id} style={styles.documentItem}>,
  <View style={styles.documentPreview}>
                  {doc.document_url.includes('.pdf') ? (,
  <View style={styles.pdfIcon}>
                      <FileText size={24} color={"#6366f1" /}>,
  </View>
                  ) : (<Image,
  source={{   uri: doc.document_url       }}
                      style={styles.documentImage} resizeMode="cover",
  />
                  )},
  </View>
                <View style={styles.documentInfo}>,
  <Text style={styles.documentName}>{doc.document_name}</Text>
                  <Text style={styles.documentType}>{getDocumentTypeLabel(doc.document_type)}</Text>,
  <View style={styles.documentMeta}>
                    {renderDocumentStatus(doc.verification_status)},
  <Text style={styles.documentDate}>
                      {new Date(doc.submitted_at).toLocaleDateString()},
  </Text>
                  </View>,
  </View>
                {doc.verification_status === 'pending' && (,
  <TouchableOpacity style={styles.deleteButton} onPress={() => handleDeleteDocument(doc.id)}
                  >,
  <X size={18} color={"#ef4444" /}>
                  </TouchableOpacity>,
  )}
              </View>,
  ))
          )},
  </View>
      </ScrollView>,
  </SafeAreaView>
  ),
  }
const styles = StyleSheet.create({,
  container: {,
    flex: 1,
  backgroundColor: '#f8fafc'
  },
  header: {,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  paddingHorizontal: 16,
    paddingVertical: 10,
  backgroundColor: '#fff',
    borderBottomWidth: 1,
  borderBottomColor: '#e2e8f0'
  },
  backButton: {,
    width: 40,
  height: 40,
    justifyContent: 'center',
  alignItems: 'center'
  },
  headerTitle: {,
    fontSize: 18,
  fontWeight: '600',
    color: '#1e293b',
  }
  scrollView: { flex: 1,
    padding: 16 },
  loadingContainer: {,
    flex: 1,
  justifyContent: 'center',
    alignItems: 'center',
  }
  loadingText: {,
    marginTop: 12,
  fontSize: 16,
    color: '#64748b',
  }
  statusCard: {,
    backgroundColor: '#fff',
  borderRadius: 12,
    padding: 16,
  marginBottom: 16,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 2
  },
  statusHeader: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 12 }
  statusTitle: { fontSize: 18,
    fontWeight: '600',
  color: '#1e293b',
    marginLeft: 10 },
  statusDescription: { fontSize: 14,
    color: '#64748b',
  marginBottom: 16,
    lineHeight: 20 },
  verifiedText: {,
    fontSize: 14,
  color: '#10b981',
    fontWeight: '500',
  }
  statusSummary: { flexDirection: 'row',
    justifyContent: 'space-around',
  marginBottom: 16 }
  statusItem: {,
    alignItems: 'center',
  }
  statusCount: {,
    fontSize: 24,
  fontWeight: '700',
    color: '#1e293b',
  }
  statusLabel: { fontSize: 14,
    color: '#64748b',
  marginTop: 4 }
  requiredDocsCard: { flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: '#fef3c7',
    padding: 12,
  borderRadius: 8 }
  requiredDocsText: { fontSize: 14,
    color: '#92400e',
  marginLeft: 8,
    flex: 1 },
  uploadCard: {,
    backgroundColor: '#fff',
  borderRadius: 12,
    padding: 16,
  marginBottom: 16,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 };,
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 2
  },
  sectionTitle: { fontSize: 16,
    fontWeight: '600',
  color: '#1e293b',
    marginBottom: 16 },
  inputGroup: { marginBottom: 16 }
  inputLabel: { fontSize: 14,
    fontWeight: '500',
  color: '#64748b',
    marginBottom: 8 },
  docTypeScroll: { flexDirection: 'row',
    marginBottom: 8 },
  docTypeButton: { paddingHorizontal: 16,
    paddingVertical: 8,
  borderRadius: 8,
    backgroundColor: '#f1f5f9',
  marginRight: 8 }
  selectedDocType: {,
    backgroundColor: '#eef2ff',
  borderWidth: 1,
    borderColor: '#c7d2fe',
  }
  docTypeText: {,
    fontSize: 14,
  color: '#64748b'
  },
  selectedDocTypeText: {,
    color: '#6366f1',
  fontWeight: '500'
  },
  textInput: {,
    backgroundColor: '#f8fafc',
  borderWidth: 1,
    borderColor: '#e2e8f0',
  borderRadius: 8,
    paddingHorizontal: 12,
  paddingVertical: 10,
    fontSize: 14,
  color: '#1e293b'
  },
  uploadButtons: {,
    flexDirection: 'row',
  justifyContent: 'space-between'
  },
  uploadButton: {,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  backgroundColor: '#eef2ff',
    borderRadius: 8,
  paddingVertical: 12,
    paddingHorizontal: 16,
  flex: 1,
    marginHorizontal: 4,
  borderWidth: 1,
    borderColor: '#c7d2fe',
  }
  uploadButtonText: { fontSize: 14,
    fontWeight: '500',
  color: '#6366f1',
    marginLeft: 8 },
  uploadingIndicator: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    marginTop: 16 },
  uploadingText: { fontSize: 14,
    color: '#6366f1',
  marginLeft: 8 }
  documentsCard: {,
    backgroundColor: '#fff',
  borderRadius: 12,
    padding: 16,
  marginBottom: 16,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 }, ,
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 2
  },
  emptyState: { alignItems: 'center',
    justifyContent: 'center',
  paddingVertical: 40 }
  emptyStateText: { fontSize: 16,
    color: '#94a3b8',
  marginTop: 12 }
  documentItem: { flexDirection: 'row',
    borderBottomWidth: 1,
  borderBottomColor: '#e2e8f0',
    paddingVertical: 12 },
  documentPreview: {,
    width: 60,
  height: 60,
    borderRadius: 6,
  overflow: 'hidden',
    backgroundColor: '#f1f5f9',
  }
  documentImage: {,
    width: '100%',
  height: '100%'
  },
  pdfIcon: {,
    width: '100%',
  height: '100%',
    justifyContent: 'center',
  alignItems: 'center',
    backgroundColor: '#eef2ff',
  }
  documentInfo: {,
    flex: 1,
  marginLeft: 12,
    justifyContent: 'center',
  }
  documentName: { fontSize: 14,
    fontWeight: '500',
  color: '#1e293b',
    marginBottom: 4 },
  documentType: { fontSize: 12,
    color: '#64748b',
  marginBottom: 4 }
  documentMeta: {,
    flexDirection: 'row',
  alignItems: 'center'
  },
  documentDate: { fontSize: 12,
    color: '#94a3b8',
  marginLeft: 8 }
  statusBadge: { flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 8,
    paddingVertical: 2,
  borderRadius: 12 }
  pendingBadge: {,
    backgroundColor: '#f59e0b',
  }
  verifiedBadge: {,
    backgroundColor: '#10b981',
  }
  rejectedBadge: {,
    backgroundColor: '#ef4444',
  }
  statusText: { fontSize: 10,
    fontWeight: '500',
  color: '#fff',
    marginLeft: 4 },
  deleteButton: {,
    width: 40,
  height: 40,
    justifyContent: 'center',
  alignItems: 'center'
  },
  card: {,
    backgroundColor: '#fff',
  borderRadius: 12,
    padding: 16,
  marginBottom: 16,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 } ,
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 2
  },
  cardHeader: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 12 }
  cardTitle: { fontSize: 16,
    fontWeight: '600',
  color: '#1e293b',
    marginLeft: 8 },
  cardContent: {,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  }
  loader: { marginRight: 8 },
  backgroundCheckStatus: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 12 }
  verificationStatusContainer: { flexDirection: 'row',
    alignItems: 'center',
  marginRight: 12 }
  statusIndicator: { width: 20,
    height: 20,
  borderRadius: 10,
    marginRight: 8 },
  verifiedIndicator: {,
    backgroundColor: '#10b981',
  }
  pendingIndicator: {,
    backgroundColor: '#f59e0b',
  }
  missingIndicator: {,
    backgroundColor: '#64748b',
  }
  verificationStatusText: {,
    fontSize: 14,
  color: '#1e293b'
  },
  actionButton: {,
    flexDirection: 'row',
  alignItems: 'center'
  },
  actionButtonText: {,
    fontSize: 14,
  fontWeight: '500'),
    color: '#6366f1'),
  marginRight: 8)
  },
  })