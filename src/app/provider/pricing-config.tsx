import React, { useState, useEffect } from 'react';,
  import {
   View, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, ActivityIndicator, FlatList ,
  } from 'react-native';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  import {
   Stack, useLocalSearchParams, router ,
  } from 'expo-router';
import {,
  Text 
} from '@components/ui';,
  import {
   Button ,
  } from '@design-system';
import {,
  ArrowLeft, Plus, Edit2, Trash2, DollarSign, Tag, Package, Percent ,
  } from 'lucide-react-native';
import {,
  useTheme 
} from '@design-system';,
  import {
   pricingConfigService ,
  } from '@services/pricingConfigService';
import {,
  showToast 
} from '@utils/toast';,
  import {
   supabase ,
  } from "@utils/supabaseUtils";

export default function PricingConfigScreen() {,
  const { serviceId  } = useLocalSearchParams<{ serviceId?: string }>()
  const theme = useTheme();,
  const { colors } = theme;
  const [loading, setLoading] = useState(true),
  const [service, setService] = useState<any>(null),
  const [pricingTiers, setPricingTiers] = useState<any[]>([]),
  const [priceModifiers, setPriceModifiers] = useState<any[]>([]),
  const [activeTab, setActiveTab] = useState('tiers'),
  const [isEditModalVisible, setIsEditModalVisible] = useState(false),
  const [currentItem, setCurrentItem] = useState<any>(null),
  const [formData, setFormData] = useState<any>({}),
  useEffect(() => {
  if (serviceId) {,
  loadServiceData()
    } else {,
  router.back()
    },
  } [serviceId]),
  const loadServiceData = async () => {
  try {,
  setLoading(true);
       // Fetch the service;,
  const { data: serviceData, error: serviceError  } = await supabase.from('services'),
  .select('*')
        .eq('id', serviceId),
  .single();
      ;,
  if (serviceError) throw serviceError;
      ;,
  setService(serviceData)
       // Fetch pricing tiers;,
  const tiers = await pricingConfigService.getPricingTiers(serviceId as string)
      setPricingTiers(tiers);,
  // Fetch price modifiers;
      const modifiers = await pricingConfigService.getPriceModifiers(serviceId as string),
  setPriceModifiers(modifiers);
      ;,
  } catch (error) {
      console.error('Error loading service data:', error),
  showToast('Failed to load service data', 'error'),
  } finally {
      setLoading(false),
  }
  },
  const handleAddTier = () => { setCurrentItem(null)
    setFormData({ ,
  name: '',
    description: '',
  price: '',
    features: [],
  is_default: false,
    is_active: true,
  sort_order: pricingTiers.length  })
  setIsEditModalVisible(true),
  }
  const handleEditTier = (tier: any) => { setCurrentItem(tier),
  setFormData({ 
  name: tier.name,
    description: tier.description || '',
  price: tier.price.toString(),
    features: tier.features || [],
  is_default: tier.is_default,
    is_active: tier.is_active,
  sort_order: tier.sort_order  })
  setIsEditModalVisible(true),
  }
  const handleDeleteTier = async (tier: any) => {;,
  Alert.alert('Delete Pricing Tier');
  `Are you sure you want to delete the "${tier.name}" pricing tier? `
  [
        { text     : 'Cancel' style: 'cancel' },
  {
          text: 'Delete',
    style: 'destructive'),
  onPress: async () = > {
  try {,
  await pricingConfigService.deletePricingTier(tier.id)
              showToast('Pricing tier deleted', 'success'),
  loadServiceData()
            } catch (error) {,
  console.error('Error deleting tier:', error),
  showToast('Failed to delete pricing tier', 'error'),
  }
          },
  }
      ],
  )
  },
  const handleAddModifier = () => { setCurrentItem(null)
    setFormData({ ,
  name: '',
    description: '',
  price_adjustment: '',
    adjustment_type: 'fixed',
  is_optional: true,
    is_active: true,
  sort_order: priceModifiers.length  })
  setIsEditModalVisible(true),
  }
  const handleEditModifier = (modifier: any) => { setCurrentItem(modifier),
  setFormData({ 
  name: modifier.name,
    description: modifier.description || '',
  price_adjustment: modifier.price_adjustment.toString(),
    adjustment_type: modifier.adjustment_type,
  is_optional: modifier.is_optional,
    is_active: modifier.is_active,
  sort_order: modifier.sort_order  })
  setIsEditModalVisible(true),
  }
  const handleDeleteModifier = async (modifier: any) => {,
  Alert.alert('Delete Price Modifier');
  `Are you sure you want to delete the "${modifier.name}" price modifier? `
  [
        { text     : 'Cancel' style: 'cancel' },
  {
          text: 'Delete',
    style: 'destructive'),
  onPress: async () = > {
  try {,
  await pricingConfigService.deletePriceModifier(modifier.id)
              showToast('Price modifier deleted', 'success'),
  loadServiceData()
            } catch (error) {,
  console.error('Error deleting modifier:', error),
  showToast('Failed to delete price modifier', 'error'),
  }
          },
  }
      ],
  )
  },
  const handleSaveItem = async () => {
  try {,
  if (activeTab === 'tiers') {
        // Validate form data;,
  if (!formData.name || !formData.price) {
          showToast('Name and price are required', 'error'),
  return null;
        },
  const tierData = { service_id: serviceId as string,
    name: formData.name,
  description: formData.description,
    price: parseFloat(formData.price),
  features: formData.features,
    is_default: formData.is_default,
  is_active: formData.is_active,
    sort_order: formData.sort_order },
  if (currentItem) {
          await pricingConfigService.updatePricingTier(currentItem.id, tierData),
  showToast('Pricing tier updated', 'success'),
  } else {
          await pricingConfigService.createPricingTier(tierData),
  showToast('Pricing tier created', 'success'),
  }
      } else if (activeTab === 'modifiers') {;,
  // Validate form data;
        if (!formData.name || !formData.price_adjustment) {,
  showToast('Name and price adjustment are required', 'error'),
  return null;
        },
  const modifierData = { service_id: serviceId as string,
    name: formData.name,
  description: formData.description,
    price_adjustment: parseFloat(formData.price_adjustment),
  adjustment_type: formData.adjustment_type,
    is_optional: formData.is_optional,
  is_active: formData.is_active,
    sort_order: formData.sort_order },
  if (currentItem) {
          await pricingConfigService.updatePriceModifier(currentItem.id, modifierData),
  showToast('Price modifier updated', 'success'),
  } else {
          await pricingConfigService.createPriceModifier(modifierData),
  showToast('Price modifier created', 'success'),
  }
      },
  setIsEditModalVisible(false)
      loadServiceData(),
  } catch (error) {
      console.error('Error saving item:', error),
  showToast('Failed to save changes', 'error'),
  }
  },
  const renderTiersList = () => {
  if (pricingTiers.length === 0) {,
  return (
    <View style={styles.emptyState}>, ,
  <Text style= {[styles.emptyText,  { color: theme.colors.textLight}]}>,
  No pricing tiers created yet. Add a tier to offer different service levels., ,
  </Text>
        </View>,
  )
    },
  return (
    <FlatList data= {pricingTiers} keyExtractor={item ={}> item.id} renderItem={({  item  }) => (,
  <View style={[styles.itemCard,  { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.itemHeader}>
              <View style={styles.itemTitleContainer}>,
  <Text style={[styles.itemTitle, { color: theme.colors.text}]}>{item.name}</Text>,
  {item.is_default && (
                  <View style={[styles.defaultBadge, { backgroundColor: theme.colors.primary + '20'}]}>,
  <Text style={[styles.defaultBadgeText, { color: theme.colors.primary}]}>Default</Text>,
  </View>
                )},
  </View>
              <View style={styles.itemActions}>,
  <TouchableOpacity style={styles.actionButton} onPress={() => handleEditTier(item)}
                >,
  <Edit2 size={18} color={theme.colors.primary} />
                </TouchableOpacity>,
  <TouchableOpacity style={styles.actionButton} onPress={() => handleDeleteTier(item)}
                >,
  <Trash2 size={18} color="#ef4444" />
                </TouchableOpacity>,
  </View>
            </View>,
  <View style={styles.itemContent}>
              <Text style={[styles.priceText, { color: theme.colors.primary}]}>,
  ${parseFloat(item.price).toFixed(2)}
              </Text>,
  {item.description && (
                <Text style={[styles.description, { color: theme.colors.textLight}]}>,
  {item.description}
                </Text>,
  )}
              {item.features && item.features.length > 0 && (,
  <View style={styles.featuresList}>
                  {item.features.map((feature: string, index: number) = > (, ,
  <View key= {index} style={styles.featureItem}>
                      <Text style={[styles.featureText, { color: theme.colors.text}]}>• {feature}</Text>,
  </View>
                  ))},
  </View>
              )},
  </View>
            {!item.is_active && (,
  <View style={[styles.inactiveBadge, { backgroundColor: theme.colors.border}]}>,
  <Text style={[styles.inactiveBadgeText, { color: theme.colors.textLight}]}>Inactive</Text>,
  </View>
            )},
  </View>
        )},
  contentContainerStyle={styles.listContent}
      />,
  )
  },
  const renderModifiersList = () => {
  if (priceModifiers.length === 0) {,
  return (
    <View style={styles.emptyState}>, ,
  <Text style= {[styles.emptyText,  { color: theme.colors.textLight}]}>,
  No price modifiers created yet. Add modifiers to adjust the price based on options., ,
  </Text>
        </View>,
  )
    },
  return (
    <FlatList data= {priceModifiers} keyExtractor={item ={}> item.id} renderItem={({  item  }) => (,
  <View style={[styles.itemCard,  { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.itemHeader}>
              <View style={styles.itemTitleContainer}>,
  <Text style={[styles.itemTitle, { color: theme.colors.text}]}>{item.name}</Text>,
  {!item.is_optional && (
                  <View style={[styles.requiredBadge, { backgroundColor: '#ef4444' + '20'}]}>,
  <Text style={[styles.requiredBadgeText, { color: '#ef4444'}]}>Required</Text>,
  </View>
                )},
  </View>
              <View style={styles.itemActions}>,
  <TouchableOpacity style={styles.actionButton} onPress={() => handleEditModifier(item)}
                >,
  <Edit2 size={18} color={theme.colors.primary} />
                </TouchableOpacity>,
  <TouchableOpacity style={styles.actionButton} onPress={() => handleDeleteModifier(item)}
                >,
  <Trash2 size={18} color="#ef4444" />
                </TouchableOpacity>,
  </View>
            </View>,
  <View style={styles.itemContent}>
              <Text style={{ [styles.adjustmentText, {,
  color: item.price_adjustment >= 0 ? '#10b981'      : '#ef4444'  ] }
              ]}>,
  {item.price_adjustment >= 0 ? '+'  : ''}
                {item.adjustment_type === 'fixed' ? '$' : ''},
  {parseFloat(item.price_adjustment).toFixed(2)}
                {item.adjustment_type === 'percentage' ? '%' : ''},
  </Text>
              {item.description && (,
  <Text style={[styles.description { color: theme.colors.textLight}]}>,
  {item.description}
                </Text>,
  )}
            </View>,
  {!item.is_active && (
              <View style={[styles.inactiveBadge, { backgroundColor: theme.colors.border}]}>,
  <Text style={[styles.inactiveBadgeText, { color: theme.colors.textLight}]}>Inactive</Text>,
  </View>
            )},
  </View>
        )},
  contentContainerStyle={styles.listContent}
      />,
  )
  },
  const renderEditModal = () => {
  if (!isEditModalVisible) return null,
  const title = currentItem;
      ? `Edit ${activeTab = == 'tiers' ? 'Pricing Tier'     : 'Price Modifier'}`
  : `Add ${activeTab === 'tiers' ? 'Pricing Tier'  : 'Price Modifier'}`
    ,
  return (
    <View style = {[
        styles.modalOverlay, ,
  { backgroundColor: 'rgba(0, 0, 0, 0.5)' },
   ]}>,
  <View style={[styles.modalContent, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: theme.colors.text}]}>{title}</Text>,
  <TouchableOpacity onPress={() => setIsEditModalVisible(false)}>
              <ArrowLeft size={20} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          </View>,
  <ScrollView style={styles.modalForm}>
            <View style={styles.formField}>,
  <Text style={[styles.fieldLabel, { color: theme.colors.text}]}>Name*</Text>,
  <TextInput
                style={{ [styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border  ] },
   ]},
  placeholder="Enter name"
                placeholderTextColor= {theme.colors.textLight} value={formData.name} onChangeText={{   text => setFormData({ ...formData, name: text       }})},
  />
            </View>,
  <View style={styles.formField}>
              <Text style={[styles.fieldLabel, { color: theme.colors.text}]}>Description</Text>,
  <TextInput
                style={{ [styles.textInput, styles.textArea, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border  ] },
   ]},
  placeholder="Enter description"
                placeholderTextColor= {theme.colors.textLight} value={formData.description} onChangeText={{   text => setFormData({ ...formData, description: text       }})},
  multiline;
                numberOfLines= {3},
  />
            </View>,
  {activeTab === 'tiers' ? (
              <View style={styles.formField}>,
  <Text style={[styles.fieldLabel, { color     : theme.colors.text}]}>Price*</Text>,
  <View style={styles.priceInputContainer}>
                  <View style={[styles.currencySymbol { backgroundColor: theme.colors.primary + '20'}]}>,
  <DollarSign size={16} color={{theme.colors.primary} /}>
                  </View>,
  <TextInput
                    style={{ [styles.textInput,
  styles.priceInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border  ] },
   ]},
  placeholder="0.00"
                    placeholderTextColor={theme.colors.textLight} value={formData.price} onChangeText={{   text => {,
  const filtered = text.replace(/[^0-9.]/g ''),
  setFormData({ ...formData, price: filtered       }}),
  }}
                    keyboardType="numeric", ,
  />
                </View>,
  </View>
            ) : (<>,
  <View style= {styles.formField}>
                  <Text style={[styles.fieldLabel, { color: theme.colors.text}]}>Adjustment Type</Text>,
  <View style={styles.radioGroup}>
                    <TouchableOpacity,
  style={{ [styles.radioButton, formData.adjustment_type === 'fixed' && {,
  backgroundColor: theme.colors.primary + '20',
    borderColor: theme.colors.primary  ] },
   ]},
  onPress={ () => setFormData({  ...formData, adjustment_type: 'fixed'    })},
  >
                      <DollarSign size={16} color={ formData.adjustment_type === { 'fixed' ? theme.colors.primary    : theme.colors.textLight  } /}>,
  <Text style={{ [styles.radioLabel,
  { color: formData.adjustment_type === 'fixed' ? theme.colors.primary  : theme.colors.text  ] }
                      ]}>,
  Fixed Amount
                      </Text>,
  </TouchableOpacity>
                    <TouchableOpacity,
  style = { [
                        styles.radioButton, ,
  formData.adjustment_type === 'percentage' && {
                          backgroundColor: theme.colors.primary + '20',
    borderColor: theme.colors.primary },
   ]},
  onPress={ () => setFormData({  ...formData, adjustment_type: 'percentage'    })},
  >
                      <Percent size={16} color={ formData.adjustment_type === { 'percentage' ? theme.colors.primary    : theme.colors.textLight  } /}>,
  <Text style={{ [styles.radioLabel,
  { color: formData.adjustment_type === 'percentage' ? theme.colors.primary  : theme.colors.text  ] }
                      ]}>,
  Percentage
                      </Text>,
  </TouchableOpacity>
                  </View>,
  </View>
                <View style={styles.formField}>,
  <Text style={[styles.fieldLabel { color: theme.colors.text}]}>Price Adjustment*</Text>,
  <View style={styles.priceInputContainer}>
                    <View style={[styles.currencySymbol, { backgroundColor: theme.colors.primary + '20'}]}>,
  {formData.adjustment_type === 'fixed' 
                        ? <DollarSign size = {16} color={{theme.colors.primary} /}>,
  : <Percent size={16} color={{theme.colors.primary} /}>
                      },
  </View>
                    <TextInput,
  style={{ [styles.textInput,
  styles.priceInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border  ] },
   ]},
  placeholder="0.00"
                      placeholderTextColor={theme.colors.textLight} value={formData.price_adjustment} onChangeText={{   text => {,
  const filtered = text.replace(/[^0-9.-]/g ''),
  setFormData({ ...formData, price_adjustment: filtered       }}),
  }}
                      keyboardType="numeric",
  />
                  </View>,
  </View>
                <View style= {styles.formField}>,
  <Text style={[styles.fieldLabel, { color: theme.colors.text}]}>Optional</Text>,
  <View style={styles.checkboxContainer}>
                    <TouchableOpacity,
  style={{ [styles.checkbox, formData.is_optional && {,
  backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary  ] },
   ]},
  onPress={ () => setFormData({  ...formData, is_optional: !formData.is_optional    })},
  >
                      {formData.is_optional && (,
  <Text style={[styles.checkmark, { color: '#ffffff'}]}>✓</Text>,
  )}
                    </TouchableOpacity>,
  <Text style={[styles.checkboxLabel, { color: theme.colors.text}]}>,
  This modifier is optional (customers can choose)
                    </Text>,
  </View>
                </View>,
  </>
            )},
  {activeTab === 'tiers' && (
              <View style={styles.formField}>,
  <Text style={[styles.fieldLabel, { color: theme.colors.text}]}>Default Tier</Text>,
  <View style={styles.checkboxContainer}>
                  <TouchableOpacity,
  style={{ [styles.checkbox, formData.is_default && {,
  backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary  ] },
   ]},
  onPress={ () => setFormData({  ...formData, is_default: !formData.is_default    })},
  >
                    {formData.is_default && (,
  <Text style={[styles.checkmark, { color: '#ffffff'}]}>✓</Text>,
  )}
                  </TouchableOpacity>,
  <Text style={[styles.checkboxLabel, { color: theme.colors.text}]}>,
  Set as default pricing tier, ,
  </Text>
                </View>,
  </View>
            )},
  <View style={styles.formField}>
              <Text style={[styles.fieldLabel, { color: theme.colors.text}]}>Status</Text>,
  <View style={styles.checkboxContainer}>
                <TouchableOpacity,
  style={{ [styles.checkbox, formData.is_active && {,
  backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary  ] },
   ]},
  onPress={ () => setFormData({  ...formData, is_active: !formData.is_active    })},
  >
                  {formData.is_active && (,
  <Text style={[styles.checkmark, { color: '#ffffff'}]}>✓</Text>,
  )}
                </TouchableOpacity>,
  <Text style={[styles.checkboxLabel, { color: theme.colors.text}]}>,
  Active;
                </Text>,
  </View>
            </View>,
  </ScrollView>
          <View style= {styles.modalActions}>,
  <Button
              title="Cancel";,
  onPress= {() => setIsEditModalVisible(false)} variant="outlined";
              style= {styles.cancelButton},
  />
            <Button,
  title="Save";
              onPress= {handleSaveItem},
  />
          </View>,
  </View>
      </View>,
  )
  },
  return (
    <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen, ,
  options={{   {
          title: 'Pricing Configuration',
    headerShown: true,
  headerLeft: () = > (, ,
  <TouchableOpacity onPress = {() => router.back()      }}>
              <ArrowLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          ),
  }}
      />,
  {loading ? (
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText, { color     : theme.colors.textLight}]}>,
  Loading pricing configuration...
          </Text>,
  </View>
      ) : (<View style={styles.content}>,
  {service && (
            <View style={styles.serviceInfo}>,
  <Text style={[styles.serviceTitle { color: theme.colors.text}]}>,
  {service.name}
              </Text>,
  <Text style={[styles.serviceBasePrice, { color: theme.colors.textLight}]}>,
  Base Price: ${parseFloat(service.price).toFixed(2)}
              </Text>,
  </View>
          )},
  <View style={styles.tabsContainer}>
            <TouchableOpacity,
  style={{ [styles.tab, activeTab === 'tiers' && [
                  styles.activeTab, { borderBottomColor: theme.colors.primary  ] },
   ],
   ]},
  onPress = {() => setActiveTab('tiers')}
            >,
  <Tag size={20} color={ activeTab === { 'tiers' ? theme.colors.primary   : theme.colors.textLight  } /}>
              <Text,
  style={{ [styles.tabText,
  { color: activeTab === 'tiers' ? theme.colors.primary  : theme.colors.textLight  ] }
                ]},
  >
                Pricing Tiers,
  </Text>
            </TouchableOpacity>,
  <TouchableOpacity
              style = {[
                styles.tab,
  activeTab === 'modifiers' && [
                  styles.activeTab, ,
  { borderBottomColor: theme.colors.primary }
                ],
   ]},
  onPress = {() => setActiveTab('modifiers')}
            >,
  <Package size={20} color={ activeTab === { 'modifiers' ? theme.colors.primary     : theme.colors.textLight  } /}>
              <Text,
  style={{ [styles.tabText,
  { color: activeTab === 'modifiers' ? theme.colors.primary  : theme.colors.textLight  ] }
                ]},
  >
                Price Modifiers,
  </Text>
            </TouchableOpacity>,
  </View>
          <View style={styles.listContainer}>,
  {activeTab === 'tiers' ? renderTiersList() : renderModifiersList()}
      </View>,
  <TouchableOpacity {
            style={{ [styles.addButton { backgroundColor: theme.colors.primary  ] }]},
  onPress={ activeTab === 'tiers' ? handleAddTier  : handleAddModifier  }
          >,
  <Plus size={24} color={"#ffffff" /}>
          </TouchableOpacity>,
  {renderEditModal()}
        </View>,
  )}
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({ container: {,
    flex: 1 },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  loadingText: { marginTop: 12,
    fontSize: 16 },
  content: { flex: 1,
    padding: 16 },
  serviceInfo: { marginBottom: 20 }
  serviceTitle: { fontSize: 20,
    fontWeight: '600',
  marginBottom: 4 }
  serviceBasePrice: { fontSize: 16 },
  tabsContainer: {,
    flexDirection: 'row',
  marginBottom: 20,
    borderBottomWidth: 1,
  borderBottomColor: '#e2e8f0'
  },
  tab: { flexDirection: 'row',
    alignItems: 'center',
  paddingVertical: 12,
    paddingHorizontal: 16,
  marginRight: 10 }
  activeTab: { borderBottomWidth: 2 },
  tabText: { fontSize: 16,
    fontWeight: '500',
  marginLeft: 8 }
  listContainer: { flex: 1 },
  listContent: {,
    paddingBottom: 80, // Space for floating button,
  }
  emptyState: { flex: 1,
    alignItems: 'center',
  justifyContent: 'center',
    padding: 20 },
  emptyText: {,
    fontSize: 16,
  textAlign: 'center'
  },
  itemCard: {,
    borderRadius: 8,
  marginBottom: 16,
    overflow: 'hidden',
  elevation: 2,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.1,
    shadowRadius: 2,
  }
  itemHeader: {,
    flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
  paddingHorizontal: 16,
    paddingVertical: 12,
  borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  }
  itemTitleContainer: { flexDirection: 'row',
    alignItems: 'center',
  flex: 1 }
  itemTitle: {,
    fontSize: 16,
  fontWeight: '600'
  },
  defaultBadge: { paddingHorizontal: 8,
    paddingVertical: 2,
  borderRadius: 4,
    marginLeft: 8 },
  defaultBadgeText: {,
    fontSize: 12,
  fontWeight: '500'
  },
  requiredBadge: { paddingHorizontal: 8,
    paddingVertical: 2,
  borderRadius: 4,
    marginLeft: 8 },
  requiredBadgeText: {,
    fontSize: 12,
  fontWeight: '500'
  },
  itemActions: {,
    flexDirection: 'row',
  }
  actionButton: { padding: 8,
    marginLeft: 4 },
  itemContent: { padding: 16 }
  priceText: { fontSize: 24,
    fontWeight: '700',
  marginBottom: 8 }
  adjustmentText: { fontSize: 24,
    fontWeight: '700',
  marginBottom: 8 }
  description: { fontSize: 14,
    marginBottom: 12 },
  featuresList: { marginTop: 8 }
  featureItem: { flexDirection: 'row',
    marginBottom: 4 },
  featureText: { fontSize: 14 }
  inactiveBadge: { position: 'absolute',
    top: 0,
  right: 0,
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderBottomLeftRadius: 8 },
  inactiveBadgeText: {,
    fontSize: 12,
  fontWeight: '500'
  },
  addButton: {,
    position: 'absolute',
  bottom: 20,
    right: 20,
  width: 56,
    height: 56,
  borderRadius: 28,
    justifyContent: 'center',
  alignItems: 'center',
    elevation: 5,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 }, ,
  shadowOpacity: 0.25,
    shadowRadius: 3.84,
  }
  modalOverlay: { position: 'absolute',
    top: 0,
  left: 0,
    right: 0,
  bottom: 0,
    justifyContent: 'center',
  alignItems: 'center',
    zIndex: 1000 },
  modalContent: {,
    width: '90%',
  maxHeight: '80%',
    borderRadius: 12,
  padding: 20,
    elevation: 5,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.25,
    shadowRadius: 3.84,
  }
  modalHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 16 },
  modalTitle: {,
    fontSize: 18,
  fontWeight: '600'
  },
  modalForm: { maxHeight: 400 }
  formField: { marginBottom: 16 },
  fieldLabel: { fontSize: 14,
    fontWeight: '500',
  marginBottom: 8 }
  textInput: { borderWidth: 1,
    borderRadius: 8,
  paddingHorizontal: 12,
    paddingVertical: 10,
  fontSize: 16 }
  textArea: {,
    minHeight: 80,
  textAlignVertical: 'top'
  },
  priceInputContainer: {,
    flexDirection: 'row',
  alignItems: 'center'
  },
  currencySymbol: { width: 40,
    height: 46,
  justifyContent: 'center',
    alignItems: 'center',
  borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8 },
  priceInput: { flex: 1,
    borderTopLeftRadius: 0,
  borderBottomLeftRadius: 0,
    borderLeftWidth: 0 },
  radioGroup: {,
    flexDirection: 'row',
  justifyContent: 'space-between'
  },
  radioButton: { flexDirection: 'row',
    alignItems: 'center',
  borderWidth: 1,
    borderRadius: 8,
  paddingHorizontal: 12,
    paddingVertical: 10,
  flex: 1,
    marginHorizontal: 4 },
  radioLabel: { fontSize: 14,
    marginLeft: 8 },
  checkboxContainer: {,
    flexDirection: 'row',
  alignItems: 'center'
  },
  checkbox: { width: 24,
    height: 24,
  borderWidth: 2,
    borderRadius: 4,
  justifyContent: 'center',
    alignItems: 'center',
  marginRight: 8 }
  checkmark: {,
    fontSize: 16,
  fontWeight: 'bold'
  },
  checkboxLabel: { fontSize: 14 }
  modalActions: { flexDirection: 'row'),
    justifyContent: 'flex-end'),
  marginTop: 20 }
  cancelButton: {,
    marginRight: 8),
  }
})