import React, { useState, useEffect } from 'react',
  import {
   useTheme  } from '@design-system';

import {
  View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl, Alert, FlatList  } from 'react-native';
import {
  Stack, useRouter  } from 'expo-router';
import {
  SafeAreaView 
} from 'react-native-safe-area-context',
  import {
   useAuth  } from '@context/AuthContext';
import {
  useColorScheme 
} from 'react-native',
  import {
   useToast  } from '@components/ui/Toast';
import {
  logger 
} from '@utils/logger',
  import {
   serviceProviderService  } from '@services/serviceProviderService';
import {
  BookingService, BookingStatus  } from '@services/standardized/BookingService';
import {
  format, isToday, isTomorrow, isThisWeek  } from 'date-fns';
import {
  Calendar, Clock, DollarSign, TrendingUp, Users, CheckCircle, XCircle, AlertCircle, RefreshCw, Phone, MessageSquare, Eye, Star  } from 'lucide-react-native' // ✅ DEBUG: Check imports at module level
console.log('🔍 [Dashboard] Import Check:', {
  useAuth: typeof useAuth,
    useToast: typeof useToast,
  logger: typeof logger,
    serviceProviderService: typeof serviceProviderService),
  BookingStatus: typeof BookingStatus)
  }),
  const COLORS = {
  const theme = useTheme(),
  light: {
    primary: '#3B82F6',
  background: '#FFFFFF',
    card: '#FFFFFF',
  surface: '#F8FAFC',
    text: '#1E293B',
  textSecondary: '#64748B',
    border: '#E2E8F0',
  success: '#10B981',
    warning: '#F59E0B',
  error: '#EF4444',
    info: '#06B6D4' }
  dark: {
    primary: '#60A5FA',
  background: '#0F172A',
    card: '#334155',
  surface: '#1E293B',
    text: '#F8FAFC',
  textSecondary: '#CBD5E1',
    border: '#475569',
  success: '#34D399',
    warning: '#FBBF24',
  error: '#F87171',
    info: '#67E8F9' }
},
  interface DashboardStats { totalBookings: number,
    todayBookings: number,
  weekRevenue: number,
    averageRating: number,
  pendingBookings: number,
    completedBookings: number },
  interface BookingItem { id: string,
    service_name: string,
  customer_name: string
  customer_phone?: string,
  booking_date: string,
    end_date: string,
  status: BookingStatus,
    price: number,
  address: string
  special_instructions?: string },
  export default function ProviderDashboardScreen() {
  // ✅ DEFENSIVE: Check critical imports before using them,
  if (!useAuth) {
  console.error('❌ [Dashboard] useAuth is undefined - AuthContext import failed'),
  return (
    <View style={{ [flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 ]  ] }>,
  <Text style={{ [fontSize: 16, color: 'red', textAlign: 'center' ]  ] }>, ,
  Authentication system unavailable. Please restart the app., ,
  </Text>
      </View>,
  )
  },
  if (!useToast) {
    console.error('❌ [Dashboard] useToast is undefined - Toast import failed'),
  return (
    <View style= {{ [flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 ]  ] }>,
  <Text style={{ [fontSize: 16, color: 'red', textAlign: 'center' ]  ] }>,
  UI components unavailable. Please restart the app., ,
  </Text>
      </View>,
  )
  },
  // ✅ SAFE: Now use the hooks with confidence
  const authResult = useAuth(),
  const toastResult = useToast()
   // ✅ DEFENSIVE: Check hook results,
  if (!authResult) {
  console.error('❌ [Dashboard] useAuth return ed null/undefined'),
  return (
    <View style={{ [flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 ]  ] }>,
  <Text style={{ [fontSize: 16, color: 'red', textAlign: 'center' ]  ] }>, ,
  Authentication context unavailable. Please restart the app., ,
  </Text>
      </View>,
  )
  },
  if (!toastResult) {
    console.error('❌ [Dashboard] useToast return ed null/undefined'),
  return (
    <View style= {{ [flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 ]  ] }>,
  <Text style={{ [fontSize: 16, color: 'red', textAlign: 'center' ]  ] }>,
  Toast system unavailable. Please restart the app., ,
  </Text>
      </View>,
  )
  },
  const { authState  } = authResult;
  const { showSuccess, showError, ToastComponent } = toastResult,
  ;
  console.log('✅ [Dashboard] All imports and hooks working correctly'),
   , ,
  const colorScheme = useColorScheme()
  const colors = COLORS[theme.mode === 'dark' ? 'dark'      : 'light'],
  const router = useRouter()
  ,
  const [loading setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [provider, setProvider] = useState<any>(null),
  const [stats, setStats] = useState<DashboardStats>({  totalBookings: 0,
    todayBookings: 0,
  weekRevenue: 0,
    averageRating: 0,
  pendingBookings: 0,
    completedBookings: 0  }),
  const [bookings, setBookings] = useState<BookingItem[]>([]),
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'today' | 'week'>('all'),
  useEffect(() => {
  console.log('🏠 Provider Dashboard mounted'),
  console.log('👤 Auth state:', {
  isAuthenticated: authState.isAuthenticated,
    userId: authState.user? .id),
  userEmail   : authState.user?.email)
  }),
  loadDashboardData()
  } []),
  const loadDashboardData = async () => {
  if (!authState.user) {
  console.log('❌ No authenticated user found')
      return null }
    try {
  setLoading(true)
      console.log('📊 Loading dashboard data for user:', authState.user.id),
  // Get provider profile;
      const providerResponse = await serviceProviderService.getServiceProviderByUserId(authState.user.id),
  ;
      console.log('🏢 Provider response:', {
  hasData: !!providerResponse.data,
    error: providerResponse.error,
  status: providerResponse.status),
    providerId: providerResponse.data? .id) })
      ,
  if (providerResponse.error || !providerResponse.data) {
        console.log('🚫 Provider profile not found, redirecting to onboarding'),
  showError('Please complete your service provider setup first')
        router.replace('/provider/onboarding'),
  return null;
      },
  setProvider(providerResponse.data)
      console.log('✅ Provider data loaded successfully     : ' providerResponse.data.business_name),
  // Load provider bookings and stats
      await loadProviderBookings(providerResponse.data.id),
  await loadProviderStats(providerResponse.data.id)
      logger.info('Dashboard data loaded successfully', 'ProviderDashboard', {
  providerId: providerResponse.data.id)
      }),
  } catch (error) {
      console.error('💥 Error loading dashboard data:', error),
  logger.error('Error loading dashboard data', 'ProviderDashboard', error as Error),
  showError('Failed to load dashboard data')
    } finally {
  setLoading(false)
      setRefreshing(false) }
  },
  const loadProviderBookings = async (providerId: string) => {
  try {
  // TODO: Implement getProviderBookings in BookingService // For now, we'll use mock data,
  const mockBookings: BookingItem[] = [
        {
  id: '1',
    service_name: 'House Cleaning',
  customer_name: 'John Smith',
    customer_phone: '+****************',
  booking_date: new Date().toISOString(),
    end_date: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
  status: BookingStatus.PENDING,
    price: 150,
  address: '123 Main St, City, State',
  special_instructions: 'Please focus on the kitchen and bathrooms'
  },
  { id: '2',
    service_name: 'Garden Maintenance',
  customer_name: 'Sarah Johnson',
    booking_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  end_date: new Date(Date.now() + 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000).toISOString(),
    status: BookingStatus.CONFIRMED,
  price: 200,
    address: '456 Oak Ave, City, State' } ,
   ],
  ;
      setBookings(mockBookings),
  } catch (error) {
      logger.error('Error loading provider bookings', 'ProviderDashboard', { providerId } error as Error),
  }
  },
  const loadProviderStats = async (providerId: string) => { try {;
      // TODO: Implement getProviderStats in service // For now, we'll use mock data,
  const mockStats: DashboardStats = {
    totalBookings: 45,
  todayBookings: 3,
    weekRevenue: 2150,
  averageRating: 4.8,
    pendingBookings: 5,
  completedBookings: 35 }
  setStats(mockStats),
  } catch (error) {
  logger.error('Error loading provider stats', 'ProviderDashboard', { providerId } error as Error),
  }
  },
  const handleRefresh = () => {
  setRefreshing(true),
  loadDashboardData()
  },
  const handleBookingStatusUpdate = async (bookingId: string, newStatus: BookingStatus, reason?: string) = > {
  try {;
      // TODO: Call booking status API,
  const response = await fetch('/api/bookings/status', {
  method: 'PUT',
    headers: {
  'Content-Type': 'application/json'
        },
  body: JSON.stringify({
    booking_id: bookingId,
  status: newStatus, ,
  user_id: authState.user? .id);
          reason) })
      }),
  const result = await response.json()
      if (!response.ok) {
  throw new Error(result.error || 'Failed to update booking status')
      },
  showSuccess('Booking status updated successfully');
      await loadDashboardData() // Refresh data,
  logger.info('Booking status updated via dashboard', 'ProviderDashboard', {
  bookingId);
        newStatus, ,
  reason)
      }),
  } catch (error) {
      logger.error('Error updating booking status', 'ProviderDashboard', { bookingId, newStatus } error as Error),
  const errorMessage = error instanceof Error ? error.message      : 'Failed to update booking status'
      showError(errorMessage),
  }
  },
  const handleConfirmBooking = (booking: BookingItem) => {
  Alert.alert('Confirm Booking',
  `Confirm booking for ${booking.customer_name}? `
      [{ text  : 'Cancel', style: 'cancel' },
  {
          text: 'Confirm'),
    onPress: () => handleBookingStatusUpdate(booking.id, BookingStatus.CONFIRMED) }],
  )
  },
  const handleCancelBooking = (booking: BookingItem) => {
  Alert.prompt('Cancel Booking', ,
  'Please provide a reason for cancellation: '), ,
  [{ text: 'Cancel', style: 'cancel' } ,
  {
          text: 'Cancel Booking'),
    style: 'destructive'),
  onPress: (reason) = > handleBookingStatusUpdate(booking.id, BookingStatus.CANCELLED, reason) }],
  'plain-text'
      '',
  'default'
  ),
  }
  const handleCompleteBooking = (booking: BookingItem) => {
  Alert.alert('Complete Booking');
  `Mark booking for ${booking.customer_name} as completed? `
  [{ text     : 'Cancel' style: 'cancel' }
  {
  text: 'Complete'),
    onPress: () = > handleBookingStatusUpdate(booking.id, BookingStatus.COMPLETED) }],
  )
  },
  const getFilteredBookings = () => {
  switch (selectedFilter) {
  case 'pending':  
        return bookings.filter(booking = > booking.status === BookingStatus.PENDING),
  case 'today':  ;
        return bookings.filter(booking = > isToday(new Date(booking.booking_date))),
  case 'week':  ;
        return bookings.filter(booking = > isThisWeek(new Date(booking.booking_date))),
  default:  ;
        return bookings }
  },
  const getStatusColor = (status: BookingStatus) => {
  switch (status) {
  case BookingStatus.PENDING:  ;
        return theme.colors.warning,
  case BookingStatus.CONFIRMED:  
        return theme.colors.info,
  case BookingStatus.COMPLETED:  
        return theme.colors.success,
  case BookingStatus.CANCELLED: return theme.colors.error,
  default: return theme.colors.textSecondary }
  },
  const getStatusIcon = (status: BookingStatus) => {
  switch (status) {
  case BookingStatus.PENDING:  ;
        return <AlertCircle size = {16} color={{getStatusColor(status)} /}>,
  case BookingStatus.CONFIRMED:  ;
        return <CheckCircle size = {16} color={{getStatusColor(status)} /}>,
  case BookingStatus.COMPLETED:  ;
        return <CheckCircle size = {16} color={{getStatusColor(status)} /}>,
  case BookingStatus.CANCELLED:  ;
        return <XCircle size= {16} color={{getStatusColor(status)} /}>,
  default:  ;
        return <Clock size= {16} color={{getStatusColor(status)} /}>,
  }
  },
  const renderStatsCard = (title: string, value: string | number, icon: React.ReactNode, color: string) = > ( ,
  <View style={[styles.statsCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border}]}>,
  <View style={[styles.statsIcon, { backgroundColor: color + '15'}]}>,
  {icon}
      </View>,
  <View style={styles.statsContent}>
        <Text style={[styles.statsValue, { color: theme.colors.text}]}>{value}</Text>,
  <Text style={[styles.statsLabel, { color: theme.colors.textSecondary}]}>{title}</Text>,
  </View>
    </View>,
  )
  const renderBookingItem = ({ item }: { item: BookingItem }) => {
  const bookingDate = new Date(item.booking_date)
    const isUpcoming = isToday(bookingDate) || isTomorrow(bookingDate),
  return ( ,
  <View style={[styles.bookingCard,  { backgroundColor: theme.colors.surface, borderColor: theme.colors.border}]}>,
  <View style={styles.bookingHeader}>
          <View style={styles.bookingTitleContainer}>,
  <Text style={[styles.serviceName, { color: theme.colors.text}]}>{item.service_name}</Text>,
  <View style={styles.statusContainer}>
              {getStatusIcon(item.status)},
  <Text style={[styles.statusText, { color: getStatusColor(item.status)}]}>,
  {item.status.toUpperCase()}
              </Text>,
  </View>
          </View>,
  <Text style={[styles.priceText, { color: theme.colors.primary}]}>${item.price}</Text>,
  </View>
        <View style={styles.customerInfo}>,
  <Text style={[styles.customerName, { color: theme.colors.text}]}>{item.customer_name}</Text>,
  {item.customer_phone && (
            <TouchableOpacity style={styles.phoneButton}>,
  <Phone size={14} color={{theme.colors.primary} /}>
              <Text style={[styles.phoneText, { color: theme.colors.primary}]}>{item.customer_phone}</Text>,
  </TouchableOpacity>
          )},
  </View>
        <View style={styles.bookingDetails}>,
  <View style={styles.detailItem}>
            <Calendar size={14} color={{theme.colors.textSecondary} /}>,
  <Text style={[styles.detailText, { color: theme.colors.textSecondary}]}>,
  {format(bookingDate, 'MMM d, yyyy')} at {format(bookingDate, 'h:mm a')},
  </Text>
          </View>,
  <View style={styles.detailItem}>
            <Clock size={14} color={{theme.colors.textSecondary} /}>,
  <Text style={[styles.detailText, { color: theme.colors.textSecondary}]}>,
  Duration: {Math.round((new Date(item.end_date).getTime() - bookingDate.getTime()) / (1000 * 60 * 60))}h, ,
  </Text>
  </View>,
  </View>
  <Text style= {{ [styles.addressText, { color: theme.colors.textSecondary  ] }]} numberOfLines={1}>,
  📍 {item.address}
        </Text>,
  {item.special_instructions && (
          <Text style={{ [styles.instructionsText, { color: theme.colors.textSecondary  ] }]} numberOfLines={2}>,
  💬 {item.special_instructions}
          </Text>,
  )}
        <View style={styles.bookingActions}>,
  <TouchableOpacity
            style={{ [styles.actionButton, { backgroundColor: theme.colors.primary + '15'  ] }]},
  onPress={() => router.push(`/booking-details/${item.id}`)}
          >,
  <Eye size={16} color={{theme.colors.primary} /}>
            <Text style={[styles.actionButtonText, { color: theme.colors.primary}]}>Details</Text>,
  </TouchableOpacity>
          {item.status === BookingStatus.PENDING && (
  <>
              <TouchableOpacity,
  style={{ [styles.actionButton, { backgroundColor: theme.colors.success + '15'  ] }]},
  onPress={() => handleConfirmBooking(item)}
              >,
  <CheckCircle size={16} color={{theme.colors.success} /}>
                <Text style={[styles.actionButtonText, { color: theme.colors.success}]}>Confirm</Text>,
  </TouchableOpacity>
              <TouchableOpacity,
  style={{ [styles.actionButton, { backgroundColor: theme.colors.error + '15'  ] }]},
  onPress={() => handleCancelBooking(item)}
              >,
  <XCircle size={16} color={{theme.colors.error} /}>
                <Text style={[styles.actionButtonText, { color: theme.colors.error}]}>Cancel</Text>,
  </TouchableOpacity>
            </>,
  )}
          {item.status === BookingStatus.CONFIRMED && (
  <TouchableOpacity
              style={{ [styles.actionButton, { backgroundColor: theme.colors.success + '15'  ] }]},
  onPress={() => handleCompleteBooking(item)}
            >,
  <CheckCircle size={16} color={{theme.colors.success} /}>
              <Text style={[styles.actionButtonText, { color: theme.colors.success}]}>Complete</Text>,
  </TouchableOpacity>
          )},
  <TouchableOpacity
            style={{ [styles.actionButton, { backgroundColor: theme.colors.info + '15'  ] }]},
  onPress={ () => {/* TODO: Implement messaging */  }}
      >,
  <MessageSquare size={16} color={{theme.colors.info} /}>
            <Text style={[styles.actionButtonText, { color: theme.colors.info}]}>Message</Text>,
  </TouchableOpacity>
        </View>,
  </View>
    ),
  }
  if (loading) {
  return (
    <SafeAreaView style={{ [styles.container, { backgroundColor: theme.colors.background  ] }]} edges={['top']}>,
  <Stack.Screen options={   title: 'Provider Dashboard'        } />
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText, { color: theme.colors.text}]}>Loading dashboard...</Text>,
  </View>
      </SafeAreaView>,
  )
  },
  const filteredBookings = getFilteredBookings()
  return (
  <SafeAreaView style={{ [styles.container, { backgroundColor: theme.colors.background  ] }]} edges={['top']}>,
  <Stack.Screen, ,
  options={   {
  title: 'Provider Dashboard',
    headerShadowVisible: false,
  headerStyle: { backgroundColor: theme.colors.background       } 
  }},
  />
  <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent} refreshControl={
  <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={{[theme.colors.primary]} /}>,
  }
      >,
  {/* Welcome Section */}
        <View style={styles.welcomeSection}>,
  <Text style={[styles.welcomeText, { color: theme.colors.text}]}>,
  Welcome back, {provider? .business_name || 'Provider'}!, ,
  </Text>
          <Text style={[styles.welcomeSubtext, { color    : theme.colors.textSecondary}]}>,
  {format(new Date() 'EEEE, MMMM d, yyyy')},
  </Text>
        </View>,
  {/* Stats Cards */}
        <View style={styles.statsContainer}>,
  {renderStatsCard('Total Bookings', stats.totalBookings, <Users size={20} color={{theme.colors.primary} /}> theme.colors.primary)},
  {renderStatsCard('Today', stats.todayBookings, <Calendar size={20} color={{theme.colors.info} /}> theme.colors.info)},
  {renderStatsCard('Week Revenue', `$${stats.weekRevenue}` <DollarSign size={20} color={{theme.colors.success} /}> theme.colors.success)},
  {renderStatsCard('Rating', stats.averageRating.toFixed(1) <Star size={20} color={{theme.colors.warning} /}> theme.colors.warning)},
  </View>
        {/* Booking Filters */}
  <View style={styles.filtersContainer}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Bookings</Text>,
  <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersScroll}>
            {[{ key: 'all', label: 'All', count: bookings.length },
  { key: 'pending', label: 'Pending', count: stats.pendingBookings },
  { key: 'today', label: 'Today', count: stats.todayBookings } ,
  { key: 'week', label: 'This Week', count: bookings.filter(b = > isThisWeek(new Date(b.booking_date))).length }].map((filter) => (
  <TouchableOpacity key = {filter.key} style={{ [styles.filterButton, {
  backgroundColor: selectedFilter === filter.key ? theme.colors.primary   : theme.colors.surface,
    borderColor: theme.colors.border  ] }]},
  onPress = {() => setSelectedFilter(filter.key as any)}
              >,
  <Text
                  style={{ [styles.filterButtonText, {
  color: selectedFilter === filter.key ? '#FFFFFF'   : theme.colors.text  ] }]},
  >
                  {filter.label} ({ filter.count }),
  </Text>
              </TouchableOpacity>,
  ))}
          </ScrollView>,
  </View>
        {/* Bookings List */}
  {filteredBookings.length === 0 ? (
          <View style={[styles.emptyState { backgroundColor : theme.colors.surface}]}>,
  <Calendar size={48} color={{theme.colors.textSecondary} /}>
            <Text style={[styles.emptyTitle, { color: theme.colors.text}]}>No Bookings Found</Text>,
  <Text style={[styles.emptyText, { color: theme.colors.textSecondary}]}>,
  {selectedFilter === 'all' 
                ? "You don't have any bookings yet." ,
  : `No ${selectedFilter} bookings to display.`}
            </Text>,
  </View>
        ) : (<FlatList data={filteredBookings} renderItem={renderBookingItem} keyExtractor={(item) ={}> item.id} scrollEnabled={false} contentContainerStyle={styles.bookingsList},
  />
        )},
  </ScrollView>
      <ToastComponent />,
  </SafeAreaView>
  ),
  }
const styles = StyleSheet.create({ container: {
    flex: 1 },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  loadingText: { marginTop: 12,
    fontSize: 16 },
  scrollView: { flex: 1 }
  scrollContent: { padding: 16 },
  welcomeSection: { marginBottom: 24 }
  welcomeText: { fontSize: 24,
    fontWeight: 'bold',
  marginBottom: 4 }
  welcomeSubtext: { fontSize: 16 },
  statsContainer: { flexDirection: 'row',
    flexWrap: 'wrap',
  marginBottom: 24,
    gap: 12 },
  statsCard: { flex: 1,
    minWidth: '47%',
  flexDirection: 'row',
    alignItems: 'center',
  padding: 16,
    borderRadius: 12,
  borderWidth: 1 }
  statsIcon: { width: 40,
    height: 40,
  borderRadius: 20,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 12 },
  statsContent: { flex: 1 }
  statsValue: { fontSize: 20,
    fontWeight: 'bold',
  marginBottom: 2 }
  statsLabel: { fontSize: 12 },
  filtersContainer: { marginBottom: 16 }
  sectionTitle: { fontSize: 20,
    fontWeight: 'bold',
  marginBottom: 12 }
  filtersScroll: { marginHorizontal: -4 },
  filterButton: { paddingHorizontal: 16,
    paddingVertical: 8,
  marginHorizontal: 4,
    borderRadius: 20,
  borderWidth: 1 }
  filterButtonText: {
    fontSize: 14,
  fontWeight: '500'
  },
  bookingsList: { gap: 12 }
  bookingCard: { padding: 16,
    borderRadius: 12,
  borderWidth: 1,
    marginBottom: 12 },
  bookingHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: 12 },
  bookingTitleContainer: { flex: 1 }
  serviceName: { fontSize: 16,
    fontWeight: '600',
  marginBottom: 4 }
  statusContainer: { flexDirection: 'row',
    alignItems: 'center',
  gap: 4 }
  statusText: {
    fontSize: 12,
  fontWeight: '500'
  },
  priceText: {
    fontSize: 18,
  fontWeight: 'bold'
  },
  customerInfo: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 12 },
  customerName: {
    fontSize: 14,
  fontWeight: '500'
  },
  phoneButton: { flexDirection: 'row',
    alignItems: 'center',
  gap: 4 }
  phoneText: { fontSize: 12 },
  bookingDetails: { marginBottom: 8,
    gap: 4 },
  detailItem: { flexDirection: 'row',
    alignItems: 'center',
  gap: 6 }
  detailText: { fontSize: 12 },
  addressText: { fontSize: 12,
    marginBottom: 4 },
  instructionsText: { fontSize: 12,
    fontStyle: 'italic',
  marginBottom: 12 }
  bookingActions: { flexDirection: 'row',
    flexWrap: 'wrap',
  gap: 8 }
  actionButton: { flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 12,
    paddingVertical: 6,
  borderRadius: 16,
    gap: 4 },
  actionButtonText: {
    fontSize: 12,
  fontWeight: '500'
  },
  emptyState: { padding: 32,
    alignItems: 'center',
  borderRadius: 12,
    marginTop: 20 },
  emptyTitle: { fontSize: 18),
    fontWeight: '600'),
  marginTop: 16,
    marginBottom: 8 },
  emptyText: {
    fontSize: 14,
  textAlign: 'center')
  },
  })