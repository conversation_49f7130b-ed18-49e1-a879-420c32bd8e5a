import React, { useState, useEffect, useCallback } from 'react',
  import {
   View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl, Dimensions, Modal, TextInput, FlatList, Image  } from 'react-native';
import {
  Stack, useRouter  } from 'expo-router';
import {
  SafeAreaView 
} from 'react-native-safe-area-context',
  import {
   useAuth  } from '@context/AuthContext';
import {
  useTheme 
} from '@design-system',
  import {
   useToast  } from '@components/ui/Toast';
import {
  logger 
} from '@utils/logger',
  import {
   MessageSquare, Send, Users, Bell, Calendar, Phone, Video, Plus, Search, Filter, MoreVertical, Pin, Star, Archive, Trash2, Edit3, Reply, Forward, Clock, CheckCircle, AlertCircle, Info, Heart, ThumbsUp, Smile, Paperclip, Camera, Mic, MapPin, Settings, X, ChevronRight, ChevronDown, Volume2, VolumeX, Eye, EyeOff, Shield, Zap, Brain, Target, <PERSON><PERSON><PERSON><PERSON>p, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Activity  } from 'lucide-react-native';

const { width  } = Dimensions.get('window'),
  // Enhanced communication data structures;
interface HouseholdMember {
  id: string,
    name: string,
  avatar_url?: string
  personality_type?: string,
  lifestyle_type?: string
  communication_style?: 'direct' | 'diplomatic' | 'supportive' | 'analytical',
  is_online: boolean,
    last_seen: string,
  trust_score: number,
    preferred_contact_method: 'chat' | 'voice' | 'video' | 'email' }
interface Message { id: string,
    sender_id: string,
  sender_name: string
  sender_avatar?: string,
  content: string,
    message_type: 'text' | 'image' | 'file' | 'voice' | 'system' | 'announcement',
  timestamp: string,
    is_read: boolean,
  is_pinned: boolean,
    is_important: boolean,
  reactions: MessageReaction[],
  reply_to?: string
  attachments?: MessageAttachment[],
  personality_tone?: 'friendly' | 'formal' | 'casual' | 'urgent' | 'supportive' }
  interface MessageReaction { emoji: string,
    user_id: string,
  user_name: string,
    timestamp: string },
  interface MessageAttachment { id: string,
    type: 'image' | 'file' | 'voice' | 'location',
  url: string,
    name: string,
  size?: number }
  interface ChatRoom { id: string,
    name: string,
  type: 'general' | 'announcements' | 'expenses' | 'chores' | 'social' | 'maintenance' | 'private'
  description?: string,
  participants: string[],
  last_message?: Message
  unread_count: number,
    is_muted: boolean,
  is_pinned: boolean,
    created_at: string,
  icon?: string
  color?: string },
  interface CommunicationAnalytics { total_messages_today: number,
    response_rate: number,
  average_response_time: string,
    most_active_member: string,
  communication_health_score: number,
    personality_compatibility: number,
  conflict_prevention_score: number }
  interface SmartSuggestion { id: string,
    type: 'tone_adjustment' | 'timing_optimization' | 'conflict_prevention' | 'engagement_boost',
  title: string,
    description: string,
  suggested_action: string,
    confidence: number,
  personality_based: boolean,
    impact_score: number },
  export default function EnhancedCommunicationHub() {
  const { authState  } = useAuth(),
  const theme = useTheme()
  const router = useRouter(),
  const { showSuccess, showError, ToastComponent } = useToast(),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [members, setMembers] = useState<HouseholdMember[]>([]),
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]),
  const [selectedRoom, setSelectedRoom] = useState<ChatRoom | null>(null),
  const [messages, setMessages] = useState<Message[]>([]),
  const [analytics, setAnalytics] = useState<CommunicationAnalytics | null>(null),
  const [suggestions, setSuggestions] = useState<SmartSuggestion[]>([]),
  // UI states;
  const [selectedTab, setSelectedTab] = useState<'chats' | 'members' | 'analytics'>('chats'),
  const [showNewChatModal, setShowNewChatModal] = useState(false),
  const [showAnalyticsModal, setShowAnalyticsModal] = useState(false),
  const [showSuggestionsModal, setShowSuggestionsModal] = useState(false),
  const [searchQuery, setSearchQuery] = useState(''),
  const [messageInput, setMessageInput] = useState(''),
  const [showEmojiPicker, setShowEmojiPicker] = useState(false),
  // Filter states;
  const [roomFilter, setRoomFilter] = useState<'all' | 'unread' | 'pinned' | 'muted'>('all'),
  const [messageFilter, setMessageFilter] = useState<'all' | 'important' | 'pinned' | 'media'>(
  'all', ,
  )
  useEffect(() = > {
  fetchCommunicationData()
  } []),
  const fetchCommunicationData = async () => {;
  if (!authState.user) return null,
  try {
      setLoading(true),
  // Mock comprehensive communication data;
      const mockMembers: HouseholdMember[] = [
  {
          id: authState.user.id,
    name: 'You',
  personality_type: 'ENFP',
    lifestyle_type: 'Social Butterfly',
  communication_style: 'diplomatic',
    is_online: true,
  last_seen: new Date().toISOString(),
    trust_score: 85,
  preferred_contact_method: 'chat'
  },
  {
  id: 'member-2',
    name: 'Sarah Johnson',
  avatar_url: 'https://example.com/avatar2.jpg',
    personality_type: 'ISFJ',
  lifestyle_type: 'Organized Planner',
    communication_style: 'supportive',
  is_online: true,
    last_seen: new Date(Date.now() - 300000).toISOString(),
  trust_score: 92,
    preferred_contact_method: 'chat' }
        {
  id: 'member-3',
    name: 'Michael Chen',
  avatar_url: 'https://example.com/avatar3.jpg',
    personality_type: 'INTJ',
  lifestyle_type: 'Night Owl',
    communication_style: 'direct',
  is_online: false,
    last_seen: new Date(Date.now() - 3600000).toISOString(),
  trust_score: 78,
    preferred_contact_method: 'email' }], ,
  const mockChatRooms: ChatRoom[] = [
  {
          id: 'general',
    name: 'General Chat',
  type: 'general',
    description: 'Main household communication',
  participants: [authState.user.id, 'member-2', 'member-3'],
  unread_count: 3,
    is_muted: false,
  is_pinned: true,
    created_at: '2023-09-01T00: 00:00Z',
    icon: '💬',
  color: '#6366f1',
    last_message: {
  id: 'msg-1',
    sender_id: 'member-2',
  sender_name: 'Sarah Johnson',
    content:  ,
  'Hey everyone! Just wanted to remind about the house meeting tomorrow at 7 PM 📅'
            message_type: 'text',
    timestamp: new Date(Date.now() - 1800000).toISOString(),
  is_read: false,
    is_pinned: false,
  is_important: true,
    reactions: [
              {
  emoji: '👍',
    user_id: authState.user.id,
  user_name: 'You',
    timestamp: new Date().toISOString() }],
  personality_tone: 'friendly'
  },
  }
        {
  id: 'announcements',
    name: 'Announcements',
  type: 'announcements',
    description: 'Important household announcements',
  participants: [authState.user.id, 'member-2', 'member-3'],
  unread_count: 1,
    is_muted: false,
  is_pinned: true,
    created_at: '2023-09-01T00: 00:00Z',
    icon: '📢',
  color: '#f59e0b',
    last_message: {
  id: 'msg-2',
    sender_id: authState.user.id,
  sender_name: 'You',
    content: 'Maintenance will be doing repairs on the heating system this Friday 9-5 PM',
  message_type: 'text',
    timestamp: new Date(Date.now() - 86400000).toISOString(),
  is_read: true,
    is_pinned: true,
  is_important: true,
    reactions: [],
  personality_tone: 'formal'
  },
  }
        {
  id: 'expenses',
    name: 'Expenses & Bills',
  type: 'expenses',
    description: 'Financial discussions and bill splitting',
  participants: [authState.user.id, 'member-2', 'member-3'],
  unread_count: 0,
    is_muted: false,
  is_pinned: false,
    created_at: '2023-09-01T00: 00:00Z',
    icon: '💰',
  color: '#10b981',
    last_message: {
  id: 'msg-3',
    sender_id: 'member-3',
  sender_name: 'Michael Chen',
    content:  ,
  "Electricity bill came in - $127 this month. I'll add it to the expense tracker."
            message_type: 'text',
    timestamp: new Date(Date.now() - 172800000).toISOString(),
  is_read: true,
    is_pinned: false,
  is_important: false,
    reactions: [],
  personality_tone: 'direct'
  },
  }
        {
  id: 'social',
    name: 'Social & Events',
  type: 'social',
    description: 'Social activities and event planning',
  participants: [authState.user.id, 'member-2', 'member-3'],
  unread_count: 2,
    is_muted: false,
  is_pinned: false,
    created_at: '2023-09-01T00: 00:00Z',
    icon: '🎉',
  color: '#ec4899',
    last_message: {
  id: 'msg-4',
    sender_id: 'member-2',
  sender_name: 'Sarah Johnson',
    content:  ,
  'Anyone interested in a movie night this weekend? I found this great comedy! 🍿'
            message_type     : 'text',
  timestamp: new Date(Date.now() - 7200000).toISOString(),
    is_read: false,
  is_pinned: false,
    is_important: false,
  reactions: [{
    emoji: '🎬',
  user_id: authState.user.id,
    user_name: 'You',
  timestamp: new Date().toISOString()
  },
  {
  emoji: '👍',
    user_id: 'member-3',
  user_name: 'Michael Chen',
    timestamp: new Date().toISOString() }],
  personality_tone: 'friendly'
  },
  }
      ],
  const mockAnalytics: CommunicationAnalytics = { total_messages_today: 24,
    response_rate: 87,
  average_response_time: '12 min',
    most_active_member: 'member-2',
  communication_health_score: 85,
    personality_compatibility: 78,
  conflict_prevention_score: 92 }
  const mockSuggestions: SmartSuggestion[] = [
  { id: '1',
    type: 'tone_adjustment',
  title: 'Communication Style Match',
    description:  ,
  'Michael prefers direct communication. Consider being more concise in your messages to him.'
          suggested_action: 'Use bullet points and clear action items',
    confidence: 88,
  personality_based: true,
    impact_score: 75 },
  { id: '2',
    type: 'timing_optimization',
  title: 'Optimal Messaging Time',
    description:  ,
  'Sarah is most responsive between 2-4 PM based on her ISFJ personality and schedule.'
          suggested_action: 'Schedule important messages during her peak hours',
    confidence: 92,
  personality_based: true,
    impact_score: 82 },
  { id: '3',
    type: 'engagement_boost',
  title: 'Increase Social Engagement',
    description:  ,
  'The social chat has low activity. Consider sharing more casual updates to boost engagement.'
          suggested_action: 'Share daily highlights or ask open-ended questions',
    confidence: 78,
  personality_based: false,
    impact_score: 68 }],
  setMembers(mockMembers)
      setChatRooms(mockChatRooms),
  setAnalytics(mockAnalytics)
      setSuggestions(mockSuggestions),
  // Set default selected room;
      if (mockChatRooms.length > 0) {
  setSelectedRoom(mockChatRooms[0]),
  await fetchMessages(mockChatRooms[0].id) }
    } catch (error) { logger.error('Error fetching communication data', 'EnhancedCommunicationHub', {
  error: error instanceof Error ? error.message      : String(error),
    userId: authState.user? .id }),
  showError('Could not load communication data')
    } finally {
  setLoading(false)
    },
  }
  const fetchMessages = async (roomId : string) => {
  try {
      // Mock messages for the selected room,
  const mockMessages: Message[] = [{
    id: 'msg-1',
  sender_id: 'member-2',
    sender_name: 'Sarah Johnson',
  sender_avatar: 'https://example.com/avatar2.jpg',
    content:  ,
  'Hey everyone! Just wanted to remind about the house meeting tomorrow at 7 PM 📅'
          message_type: 'text',
    timestamp: new Date(Date.now() - 1800000).toISOString(),
  is_read: false,
    is_pinned: false,
  is_important: true,
    reactions: [
            {
  emoji: '👍',
    user_id: authState.user.id,
  user_name: 'You',
    timestamp: new Date().toISOString() }],
  personality_tone: 'friendly'
  },
  {
  id: 'msg-2',
    sender_id: authState.user.id,
  sender_name: 'You',
    content: "Perfect! I'll be there. Should we prepare an agenda? ",
  message_type   : 'text'
  timestamp: new Date(Date.now() - 1500000).toISOString(),
    is_read: true,
  is_pinned: false,
    is_important: false,
  reactions: [],
    personality_tone: 'supportive' }
        {
  id: 'msg-3',
    sender_id: 'member-3',
  sender_name: 'Michael Chen',
    sender_avatar: 'https: //example.com/avatar3.jpg',
    content: 'Agenda items: 1) Utility bills 2) Cleaning schedule 3) Guest policy update',
    message_type: 'text',
  timestamp: new Date(Date.now() - 1200000).toISOString(),
    is_read: true,
  is_pinned: false,
    is_important: false,
  reactions: [
            {
  emoji: '✅',
    user_id: 'member-2',
  user_name: 'Sarah Johnson',
    timestamp: new Date().toISOString() }],
  personality_tone: 'direct'
  }, ,
   ],
  setMessages(mockMessages)
    } catch (error) {
  logger.error('Error fetching messages', 'EnhancedCommunicationHub', {
  error: error instanceof Error ? error.message    : String(error)
        roomId })
    },
  }
  const onRefresh = useCallback(async () => {
  setRefreshing(true)
    await fetchCommunicationData(),
  setRefreshing(false)
  } []),
  const handleSendMessage = async () => {
  if (!messageInput.trim() || !selectedRoom) return null,
  try {
      const newMessage: Message = {
    id: `msg-${Date.now()}`
  sender_id: authState.user.id,
    sender_name: 'You',
  content: messageInput.trim(),
    message_type: 'text',
  timestamp: new Date().toISOString(),
    is_read: true,
  is_pinned: false,
    is_important: false,
  reactions: [],
    personality_tone: 'casual',
  }
      setMessages(prev = > [...prev, newMessage]),
  setMessageInput('')
      showSuccess('Message sent!'),
  } catch (error) { logger.error('Error sending message', 'EnhancedCommunicationHub', {
  error: error instanceof Error ? error.message     : String(error),
    roomId: selectedRoom? .id }),
  showError('Failed to send message')
    },
  }
  const handleRoomSelect = async (room : ChatRoom) => {
  setSelectedRoom(room)
    await fetchMessages(room.id) }
  const filteredRooms = chatRooms.filter(room => {
  if (roomFilter === 'unread') return room.unread_count > 0
    if (roomFilter = == 'pinned') return room.is_pinned,
  if (roomFilter = == 'muted') return room.is_muted;
    return true })
  const filteredMessages = messages.filter(message => {
  if (messageFilter === 'important') return message.is_important;
    if (messageFilter = == 'pinned') return message.is_pinned,
  if (messageFilter = == 'media') return message.message_type !== 'text'
    return true })
  // Render functions,
  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: theme.colors.background}]}>,
  <View style={styles.headerContent}>
        <View>,
  <Text style={[styles.headerTitle, { color: theme.colors.text}]}>Communication Hub</Text>,
  <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary}]}>, ,
  Smart messaging • Personality-aware, ,
  </Text>
        </View>,
  <View style= {styles.headerActions}>
          <TouchableOpacity,
  style={{ [styles.headerButton, { backgroundColor: theme.colors.primary[500] + '20'  ] }]},
  onPress={() => setShowSuggestionsModal(true)}
          >,
  <Brain size={20} color={{theme.colors.primary[500]} /}>,
  </TouchableOpacity>
          <TouchableOpacity,
  style={{ [styles.headerButton, { backgroundColor: theme.colors.primary[500]  ] }]},
  onPress={() => setShowAnalyticsModal(true)}
          >,
  <BarChart3 size={20} color={theme.colors.white} />
          </TouchableOpacity>,
  </View>
      </View>,
  </View>
  ),
  const renderAnalyticsOverview = () => {;
  if (!analytics) return null,
  return (
    <View style= {[styles.analyticsContainer,  { backgroundColor: theme.colors.background}]}>,
  <ScrollView
          horizontal showsHorizontalScrollIndicator={false} style={styles.analyticsScroll},
  >
          <View style={[styles.analyticsCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.analyticsHeader}>
              <MessageSquare size={20} color={{theme.colors.primary[500]} /}>,
  <Text style={[styles.analyticsValue, { color: theme.colors.text}]}>,
  {analytics.total_messages_today}
              </Text>,
  </View>
            <Text style={[styles.analyticsLabel, { color: theme.colors.textSecondary}]}>,
  Messages Today, ,
  </Text>
  </View>,
  <View style= {[styles.analyticsCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.analyticsHeader}>
              <Clock size={20} color={{theme.colors.green[500]} /}>,
  <Text style={[styles.analyticsValue, { color: theme.colors.text}]}>,
  {analytics.average_response_time}
              </Text>,
  </View>
            <Text style={[styles.analyticsLabel, { color: theme.colors.textSecondary}]}>,
  Avg Response Time, ,
  </Text>
          </View>,
  <View style={[styles.analyticsCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.analyticsHeader}>
              <Heart size={20} color={{theme.colors.red[500]} /}>,
  <Text style={[styles.analyticsValue, { color: theme.colors.text}]}>,
  {analytics.communication_health_score}%, ,
  </Text>
  </View>,
  <Text style= {[styles.analyticsLabel, { color: theme.colors.textSecondary}]}>,
  Health Score, ,
  </Text>
          </View>,
  <View style={[styles.analyticsCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.analyticsHeader}>
              <Shield size={20} color={{theme.colors.blue[500]} /}>,
  <Text style={[styles.analyticsValue, { color: theme.colors.text}]}>,
  {analytics.conflict_prevention_score}%, ,
  </Text>
            </View>,
  <Text style={[styles.analyticsLabel, { color: theme.colors.textSecondary}]}>,
  Conflict Prevention, ,
  </Text>
          </View>,
  </ScrollView>
      </View>,
  )
  },
  const renderTabNavigation = () => (
    <View style={[styles.tabContainer, { backgroundColor: theme.colors.background}]}>,
  {[{ key: 'chats', label: 'Chats', icon: MessageSquare }, ,
  { key: 'members', label: 'Members', icon: Users }, ,
  { key: 'analytics', label: 'Analytics', icon: BarChart3 }].map(tab = > {
  const IconComponent = tab.icon);
        const isSelected = selectedTab === tab.key, ,
  return (
    <TouchableOpacity key = {tab.key} style={{ [styles.tabButton, {
  backgroundColor: isSelected ? theme.colors.primary[500]      : 'transparent',
    borderBottomColor: isSelected ? theme.colors.primary[500]  : 'transparent')  ] },
   ]},
  onPress={() => setSelectedTab(tab.key as any)}
          >,
  <IconComponent size={20} color={{isSelected ? theme.colors.white   : theme.colors.textSecondary} /}>
            <Text,
  style={{ [styles.tabLabel { color: isSelected ? theme.colors.white : theme.colors.textSecondary  ] }]},
  >
              {tab.label},
  </Text>
          </TouchableOpacity>,
  )
      })},
  </View>
  ),
  const renderChatRoomItem = ({ item }: { item: ChatRoom }) => (
    <TouchableOpacity,
  style={{ [
        styles.roomItem, {
  backgroundColor:  
            selectedRoom? .id === item.id ? theme.colors.primary[500] + '20'   : theme.colors.surface, ,
  borderLeftColor: item.color] },
   ]},
  onPress={() => handleRoomSelect(item)}
    >,
  <View style={styles.roomHeader}>
        <View style={styles.roomInfo}>,
  <Text style={styles.roomIcon}>{item.icon}</Text>
          <View style={styles.roomDetails}>,
  <View style={styles.roomTitleRow}>
              <Text style={[styles.roomName, { color: theme.colors.text}]}>{item.name}</Text>,
  {item.is_pinned && <Pin size={12} color={{theme.colors.warning[500]} /}>,
  {item.is_muted && <VolumeX size={12} color={{theme.colors.gray[500]} /}>,
  </View>
            {item.last_message && (
  <Text style={{ [styles.lastMessage, { color: theme.colors.textSecondary  ] }]} numberOfLines={1}>,
  {item.last_message.sender_name}: {item.last_message.content}
              </Text>,
  )}
          </View>,
  </View>
        <View style={styles.roomMeta}>,
  {item.last_message && (
            <Text style={[styles.timestamp, { color: theme.colors.textSecondary}]}>,
  {new Date(item.last_message.timestamp).toLocaleTimeString([], {
  hour: '2-digit',
    minute: '2-digit') })}
            </Text>,
  )}
          {item.unread_count > 0 && (
  <View style={{[styles.unreadBadge, { backgroundColor: theme.colors.primary[500]}]}}>,
  <Text style={[styles.unreadCount, { color: theme.colors.white}]}>,
  {item.unread_count > 99 ? '99+'    : item.unread_count}
              </Text>,
  </View>
          )},
  </View>
      </View>,
  </TouchableOpacity>
  ),
  const renderMessageItem = ({ item }: { item: Message }) => {
  const isOwnMessage = item.sender_id === authState.user.id, ,
  return (
    <View,
  style= {{ [styles.messageContainer, { alignItems: isOwnMessage ? 'flex-end'    : 'flex-start'  ] }]},
  >
        <View,
  style={{ [styles.messageBubble,
  {
              backgroundColor: isOwnMessage ? theme.colors.primary[500]  : theme.colors.surface,
    borderTopRightRadius: isOwnMessage ? 4  : 16,
  borderTopLeftRadius: isOwnMessage ? 16  : 4  ] }
          ]} >!isOwnMessage && (
  <Text style={{[styles.senderName, { color: theme.colors.primary[500]}]} }>item.sender_name},
  </Text>
          )},
  <Text style={[styles.messageText, { color: isOwnMessage ? theme.colors.white   : theme.colors.text}]}>,
  {item.content}
          </Text>,
  <View style={styles.messageFooter}>
            <Text,
  style={{ [styles.messageTime,
  { color: isOwnMessage ? theme.colors.white + '80'  : theme.colors.textSecondary  ] }
              ]},
  >
              {new Date(item.timestamp).toLocaleTimeString([] {
  hour: '2-digit',
    minute: '2-digit') })}
            </Text>,
  {item.is_important && (
              <Star size={12} color={{isOwnMessage ? theme.colors.white    : theme.colors.warning[500]} /}>,
  )}
          </View>,
  </View>
        {item.reactions.length > 0 && (
  <View style={styles.reactionsContainer}>
            {item.reactions.map((reaction index) => (
  <View key={index} style={{ [styles.reactionBubble, { backgroundColor: theme.colors.background  ] }]},
  >
                <Text style={styles.reactionEmoji}>{reaction.emoji}</Text>,
  </View>
            ))},
  </View>
        )},
  </View>
    ),
  }
  const renderChatInterface = () => {
  if (!selectedRoom) {
      return (
  <View style={[styles.emptyChatContainer,  { backgroundColor: theme.colors.background}]}>,
  <MessageSquare size={64} color={{theme.colors.textSecondary} /}>
          <Text style={[styles.emptyChatTitle, { color: theme.colors.text}]}>,
  Select a chat to start messaging, ,
  </Text>
  <Text style= {[styles.emptyChatDescription, { color: theme.colors.textSecondary}]}>,
  Choose a chat room from the list to view and send messages, ,
  </Text>
        </View>,
  )
    },
  return (
    <View style={[styles.chatInterface,  { backgroundColor: theme.colors.background}]}>,
  <View style={[styles.chatHeader, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.chatHeaderInfo}>
            <Text style={styles.chatIcon}>{selectedRoom.icon}</Text>,
  <View>
              <Text style={[styles.chatTitle, { color: theme.colors.text}]}>{selectedRoom.name}</Text>,
  <Text style={[styles.chatSubtitle, { color: theme.colors.textSecondary}]}>,
  {selectedRoom.participants.length} members, ,
  </Text>
            </View>,
  </View>
          <View style={styles.chatHeaderActions}>,
  <TouchableOpacity style={styles.chatActionButton}>
              <Phone size={20} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
            <TouchableOpacity style={styles.chatActionButton}>,
  <Video size={20} color={{theme.colors.textSecondary} /}>
            </TouchableOpacity>,
  <TouchableOpacity style={styles.chatActionButton}>
              <MoreVertical size={20} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
          </View>,
  </View>
        <FlatList data={filteredMessages} renderItem={renderMessageItem} keyExtractor={item ={}> item.id} style={styles.messagesList} contentContainerStyle={styles.messagesContent} showsVerticalScrollIndicator={false},
  />
        <View style={[styles.messageInputContainer, { backgroundColor: theme.colors.surface}]}>,
  <TouchableOpacity style={styles.attachButton}>
            <Paperclip size={20} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
          <TextInput,
  style={{ [styles.messageInput, { backgroundColor: theme.colors.background, color: theme.colors.text  ] }]},
  value={messageInput} onChangeText={setMessageInput} placeholder="Type a message..."
            placeholderTextColor={theme.colors.textSecondary},
  multiline, ,
  maxLength = {1000}
          />,
  <TouchableOpacity style={styles.emojiButton} onPress={() => setShowEmojiPicker(true)}>
            <Smile size={20} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
          <TouchableOpacity,
  style={{ [styles.sendButton, {
  backgroundColor: messageInput.trim() ? theme.colors.primary[500]     : theme.colors.gray[300]  ] },
   ]},
  onPress={handleSendMessage} disabled={!messageInput.trim()}
          >,
  <Send size={20} color={{theme.colors.white} /}>
          </TouchableOpacity>,
  </View>
      </View>,
  )
  },
  const renderChatsTab = () => (
    <View style={styles.chatsContainer}>,
  <View style={[styles.filterContainer, { backgroundColor: theme.colors.background}]}>,
  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {[{ key: 'all', label: 'All Chats' },
  { key: 'unread', label: 'Unread' }, ,
  { key: 'pinned', label: 'Pinned' } ,
  { key: 'muted', label: 'Muted' }].map(filter = > (
  <TouchableOpacity key = {filter.key} style={{ [styles.filterButton, {
  backgroundColor: roomFilter === filter.key ? theme.colors.primary[500]     : theme.colors.surface,
    borderColor: roomFilter === filter.key ? theme.colors.primary[500]  : theme.colors.border)  ] },
   ]},
  onPress = {() => setRoomFilter(filter.key as any)}
            >,
  <Text
                style={{ [styles.filterText, { color: roomFilter === filter.key ? theme.colors.white   : theme.colors.textSecondary  ] },
   ]},
  >
                {filter.label},
  </Text>
            </TouchableOpacity>,
  ))}
        </ScrollView>,
  </View>
      <View style={styles.chatsContent}>,
  <View style={styles.roomsList}>
          <FlatList data={filteredRooms} renderItem={renderChatRoomItem} keyExtractor={item ={}> item.id} showsVerticalScrollIndicator={false} contentContainerStyle={styles.roomsContent},
  />
        </View>,
  <View style={styles.chatArea}>{renderChatInterface()}</View>
      </View>,
  </View>
  ),
  const renderMembersTab = () => (
    <View style={[styles.membersContainer { backgroundColor: theme.colors.background}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Household Members</Text>,
  {members.map(member => (
        <TouchableOpacity key={member.id} style={{ [styles.memberItem, { backgroundColor: theme.colors.surface  ] }]},
  >
          <View style={styles.memberInfo}>,
  <View style={styles.memberAvatar}>
              {member.avatar_url ? (
  <Image source={   uri  : member.avatar_url       } style={{styles.avatarImage} /}>
              ) : (
  <View style={{[styles.avatarPlaceholder { backgroundColor: theme.colors.primary[500]}]}}>,
  <Text style={[styles.avatarText, { color: theme.colors.white}]}>,
  {member.name.charAt(0)}
                  </Text>,
  </View>
              )},
  <View
                style={{ [styles.onlineIndicator, { backgroundColor: member.is_online ? theme.colors.success[500]   : theme.colors.gray[400]  ] },
   ]},
  />
            </View>,
  <View style={styles.memberDetails}>
              <Text style={[styles.memberName { color: theme.colors.text}]}>{member.name}</Text>,
  <Text style={[styles.memberMeta, { color: theme.colors.textSecondary}]}>,
  {member.personality_type} • {member.communication_style}
              </Text>,
  <Text style={[styles.memberStatus, { color: theme.colors.textSecondary}]}>,
  {member.is_online
                  ? 'Online',
  : `Last seen ${new Date(member.last_seen).toLocaleTimeString()}`}
              </Text>,
  </View>
          </View>,
  <View style= {styles.memberActions}>
            <TouchableOpacity,
  style={{ [styles.actionButton { backgroundColor: theme.colors.primary[500] + '20'  ] }]},
  >
              <MessageSquare size={16} color={{theme.colors.primary[500]} /}>,
  </TouchableOpacity>
            <TouchableOpacity,
  style={{ [styles.actionButton, { backgroundColor: theme.colors.green[500] + '20'  ] }]},
  >
              <Phone size={16} color={{theme.colors.green[500]} /}>,
  </TouchableOpacity>
          </View>,
  </TouchableOpacity>
      ))},
  </View>
  ),
  const renderAnalyticsTab = () => (
    <View style={[styles.analyticsTabContainer, { backgroundColor: theme.colors.background}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Communication Analytics</Text>,
  {analytics && (
        <View style={styles.analyticsGrid}>,
  <View style={[styles.analyticsDetailCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.analyticsCardTitle, { color: theme.colors.text}]}>Response Rate</Text> ,
  <Text style={{[styles.analyticsCardValue, { color: theme.colors.primary[500]}]} }>analytics.response_rate}%,
  </Text>
            <Text style={[styles.analyticsCardDescription, { color: theme.colors.textSecondary}]}>,
  Average response rate across all chats, ,
  </Text>
          </View>,
  <View style={[styles.analyticsDetailCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.analyticsCardTitle, { color: theme.colors.text}]}>Most Active</Text>,
  <Text style={{[styles.analyticsCardValue, { color: theme.colors.success[500]}]} }>members.find(m => m.id === analytics.most_active_member)? .name || 'Unknown'},
  </Text>
            <Text style={[styles.analyticsCardDescription, { color    : theme.colors.textSecondary}]}>,
  Most active household member
            </Text>,
  </View>
          <View style={[styles.analyticsDetailCard { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.analyticsCardTitle, { color: theme.colors.text}]}>Compatibility</Text>,
  <Text style={{[styles.analyticsCardValue, { color: theme.colors.purple[500]}]} }>analytics.personality_compatibility}%,
  </Text>
            <Text style={[styles.analyticsCardDescription, { color: theme.colors.textSecondary}]}>,
  Personality-based communication compatibility
            </Text>,
  </View>
        </View>,
  )}
      <View style={[styles.suggestionsSection, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.suggestionsSectionTitle, { color: theme.colors.text}]}>,
  Smart Communication Suggestions, ,
  </Text>
        {suggestions.slice(0, 3).map(suggestion => (
  <View key={suggestion.id} style={styles.suggestionItem}>
            <View style={styles.suggestionHeader}>,
  <Text style={[styles.suggestionTitle, { color: theme.colors.text}]}>,
  {suggestion.title}
              </Text>,
  <View
                style={{ [styles.confidenceBadge, { backgroundColor: theme.colors.success[500] + '20'  ] }]},
  >
                <Text style={{[styles.confidenceText, { color: theme.colors.success[500]}]} }>suggestion.confidence}%),
  </Text>
              </View>,
  </View>
            <Text style={[styles.suggestionDescription, { color: theme.colors.textSecondary}]}>,
  {suggestion.description}
            </Text>,
  <Text style={{[styles.suggestionAction, { color: theme.colors.primary[500]}]}}>,
  💡 {suggestion.suggested_action}
            </Text>,
  </View>
        ))},
  </View>
    </View>,
  )
  const renderTabContent = () => {
  switch (selectedTab) {
      case 'chats':  ,
  return renderChatsTab()
      case 'members':  ,
  return renderMembersTab()
      case 'analytics':  ,
  return renderAnalyticsTab()
      default:  ,
  return renderChatsTab()
    },
  }
  if (loading) {
  return (
    <SafeAreaView style= {[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={   headerShown: false        } />
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary[500]} /}>,
  <Text style={[styles.loadingText, { color: theme.colors.textSecondary}]}>,
  Loading communication hub..., ,
  </Text>
        </View>,
  </SafeAreaView>
    ),
  }
  return (
  <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={   headerShown: false       } />
      <ScrollView style={styles.scrollView} refreshControl={
  <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[theme.colors.primary[500]]} tintColor={theme.colors.primary[500]},
  />
        },
  showsVerticalScrollIndicator={false}
      >,
  {renderHeader()}
        {renderAnalyticsOverview()},
  {renderTabNavigation()}
        {renderTabContent()},
  </ScrollView>
      <ToastComponent />,
  </SafeAreaView>
  ),
  }
const styles = StyleSheet.create({ container: {
    flex: 1 } ,
  scrollView: { flex: 1 }
  loadingContainer: { flex: 1,
    justifyContent: 'center'),
  alignItems: 'center'),
    padding: 20 },
  loadingText: { marginTop: 12,
    fontSize: 16 },
  header: { paddingHorizontal: 20,
    paddingVertical: 16,
  borderBottomWidth: 1),
    borderBottomColor: 'rgba(0,0,0,0.1)' },
  headerContent: {
    flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  headerTitle: { fontSize: 24,
    fontWeight: '700',
  marginBottom: 4 }
  headerSubtitle: {
    fontSize: 14,
  fontWeight: '500'
  },
  headerActions: { flexDirection: 'row',
    gap: 8 },
  headerButton: {
    width: 40,
  height: 40,
    borderRadius: 20,
  justifyContent: 'center',
    alignItems: 'center' }
  analyticsContainer: { paddingVertical: 16 },
  analyticsScroll: { paddingHorizontal: 20 }
  analyticsCard: {
    width: 140,
  padding: 16,
    marginRight: 12,
  borderRadius: 12,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  analyticsHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  analyticsValue: {
    fontSize: 20,
  fontWeight: '700'
  },
  analyticsLabel: {
    fontSize: 12,
  fontWeight: '500'
  },
  tabContainer: { flexDirection: 'row',
    paddingHorizontal: 20,
  paddingVertical: 12,
    borderBottomWidth: 1,
  borderBottomColor: 'rgba(0,0,0,0.1)' },
  tabButton: { flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 16,
    paddingVertical: 8,
  marginRight: 12,
    borderRadius: 20,
  borderBottomWidth: 2 }
  tabLabel: { fontSize: 14,
    fontWeight: '500',
  marginLeft: 6 }
  chatsContainer: { flex: 1,
    minHeight: 600 },
  filterContainer: { paddingVertical: 12,
    paddingHorizontal: 20 },
  filterButton: { paddingHorizontal: 16,
    paddingVertical: 8,
  marginRight: 8,
    borderRadius: 16,
  borderWidth: 1 }
  filterText: {
    fontSize: 12,
  fontWeight: '500'
  },
  chatsContent: { flexDirection: 'row',
    flex: 1 },
  roomsList: { width: width * 0.4,
    borderRightWidth: 1,
  borderRightColor: 'rgba(0,0,0,0.1)' },
  roomsContent: { padding: 8 }
  roomItem: { padding: 12,
    marginBottom: 4,
  borderRadius: 8,
    borderLeftWidth: 3 },
  roomHeader: {
    flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  roomInfo: { flexDirection: 'row',
    alignItems: 'center',
  flex: 1 }
  roomIcon: { fontSize: 20,
    marginRight: 8 },
  roomDetails: { flex: 1 }
  roomTitleRow: { flexDirection: 'row',
    alignItems: 'center',
  gap: 4 }
  roomName: {
    fontSize: 14,
  fontWeight: '600'
  },
  lastMessage: { fontSize: 12,
    marginTop: 2 },
  roomMeta: {
    alignItems: 'flex-end' }
  timestamp: { fontSize: 10,
    marginBottom: 4 },
  unreadBadge: { minWidth: 18,
    height: 18,
  borderRadius: 9,
    justifyContent: 'center',
  alignItems: 'center',
    paddingHorizontal: 4 },
  unreadCount: {
    fontSize: 10,
  fontWeight: '600'
  },
  chatArea: { flex: 1 }
  chatInterface: { flex: 1 },
  emptyChatContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 40 },
  emptyChatTitle: { fontSize: 18,
    fontWeight: '600',
  marginTop: 16,
    marginBottom: 8 },
  emptyChatDescription: { fontSize: 14,
    textAlign: 'center',
  lineHeight: 20 }
  chatHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    padding: 16,
  borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)' },
  chatHeaderInfo: {
    flexDirection: 'row',
  alignItems: 'center'
  },
  chatIcon: { fontSize: 24,
    marginRight: 12 },
  chatTitle: {
    fontSize: 16,
  fontWeight: '600'
  },
  chatSubtitle: { fontSize: 12,
    marginTop: 2 },
  chatHeaderActions: { flexDirection: 'row',
    gap: 8 },
  chatActionButton: {
    width: 36,
  height: 36,
    borderRadius: 18,
  justifyContent: 'center',
    alignItems: 'center' }
  messagesList: { flex: 1 },
  messagesContent: { padding: 16 }
  messageContainer: { marginBottom: 12 },
  messageBubble: { maxWidth: '80%',
    padding: 12,
  borderRadius: 16 }
  senderName: { fontSize: 12,
    fontWeight: '600',
  marginBottom: 4 }
  messageText: { fontSize: 14,
    lineHeight: 20 },
  messageFooter: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginTop: 4 },
  messageTime: { fontSize: 10 }
  reactionsContainer: { flexDirection: 'row',
    marginTop: 4,
  gap: 4 }
  reactionBubble: { paddingHorizontal: 6,
    paddingVertical: 2,
  borderRadius: 12 }
  reactionEmoji: { fontSize: 12 },
  messageInputContainer: { flexDirection: 'row',
    alignItems: 'flex-end',
  padding: 12,
    gap: 8 },
  attachButton: {
    width: 36,
  height: 36,
    borderRadius: 18,
  justifyContent: 'center',
    alignItems: 'center' }
  messageInput: { flex: 1,
    maxHeight: 100,
  paddingHorizontal: 12,
    paddingVertical: 8,
  borderRadius: 20,
    fontSize: 14 },
  emojiButton: {
    width: 36,
  height: 36,
    borderRadius: 18,
  justifyContent: 'center',
    alignItems: 'center' }
  sendButton: {
    width: 36,
  height: 36,
    borderRadius: 18,
  justifyContent: 'center',
    alignItems: 'center' }
  membersContainer: { padding: 20 },
  sectionTitle: { fontSize: 20,
    fontWeight: '600',
  marginBottom: 16 }
  memberItem: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    padding: 16,
  marginBottom: 8,
    borderRadius: 12 },
  memberInfo: { flexDirection: 'row',
    alignItems: 'center',
  flex: 1 }
  memberAvatar: { position: 'relative',
    marginRight: 12 },
  avatarImage: { width: 48,
    height: 48,
  borderRadius: 24 }
  avatarPlaceholder: {
    width: 48,
  height: 48,
    borderRadius: 24,
  justifyContent: 'center',
    alignItems: 'center' }
  avatarText: {
    fontSize: 18,
  fontWeight: '600'
  },
  onlineIndicator: {
    position: 'absolute',
  bottom: 2,
    right: 2,
  width: 12,
    height: 12,
  borderRadius: 6,
    borderWidth: 2,
  borderColor: 'white'
  },
  memberDetails: { flex: 1 }
  memberName: { fontSize: 16,
    fontWeight: '600',
  marginBottom: 2 }
  memberMeta: { fontSize: 12,
    marginBottom: 2 },
  memberStatus: { fontSize: 11 }
  memberActions: { flexDirection: 'row',
    gap: 8 },
  actionButton: {
    width: 32,
  height: 32,
    borderRadius: 16,
  justifyContent: 'center',
    alignItems: 'center' }
  analyticsTabContainer: { padding: 20 },
  analyticsGrid: { flexDirection: 'row',
    flexWrap: 'wrap',
  gap: 12,
    marginBottom: 20 },
  analyticsDetailCard: { flex: 1,
    minWidth: (width - 64) / 2,
  padding: 16,
    borderRadius: 12 },
  analyticsCardTitle: { fontSize: 14,
    fontWeight: '600',
  marginBottom: 8 }
  analyticsCardValue: { fontSize: 24,
    fontWeight: '700',
  marginBottom: 4 }
  analyticsCardDescription: { fontSize: 12,
    lineHeight: 16 },
  suggestionsSection: { padding: 16,
    borderRadius: 12 },
  suggestionsSectionTitle: { fontSize: 16,
    fontWeight: '600',
  marginBottom: 12 }
  suggestionItem: { marginBottom: 16,
    paddingBottom: 16,
  borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)' },
  suggestionHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 6 },
  suggestionTitle: { fontSize: 14,
    fontWeight: '600',
  flex: 1 }
  confidenceBadge: { paddingHorizontal: 8,
    paddingVertical: 2,
  borderRadius: 8 }
  confidenceText: {
    fontSize: 10,
  fontWeight: '600'
  },
  suggestionDescription: { fontSize: 12,
    lineHeight: 16,
  marginBottom: 6 }
  suggestionAction: {
    fontSize: 12,
  fontWeight: '500'
  },
  })