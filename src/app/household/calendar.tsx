import React, { useState, useEffect, useCallback } from 'react',
  import {
   View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl, Dimensions, Modal, TextInput  } from 'react-native';
import {
  Stack, useRouter  } from 'expo-router';
import {
  SafeAreaView 
} from 'react-native-safe-area-context',
  import {
   useAuth  } from '@context/AuthContext';
import {
  useTheme 
} from '@design-system',
  import {
   useToast  } from '@components/ui/Toast';
import {
  logger 
} from '@utils/logger',
  import {
   Calendar as CalendarComponent  } from 'react-native-calendars';
import {
  CalendarDays, Plus, Clock, Users, X, Filter, Brain, Heart, Lightbulb, Target, TrendingUp, Award, Star, Zap, Coffee, Home, Settings, MessageSquare, Bell, Repeat, MapPin, BarChart3, Trophy, Flame, Gift, ChevronRight, Calendar, User, Sun, Moon, Sunrise, Sunset, Activity, BookOpen, Music, Utensils, Gamepad2, Tv, Du<PERSON><PERSON>  } from 'lucide-react-native';

const { width  } = Dimensions.get('window'),
  // Enhanced calendar data structures;
interface CalendarMember { id: string,
    name: string,
  avatar_url?: string
  personality_type?: string,
  lifestyle_type?: string
  communication_style?: 'direct' | 'diplomatic' | 'supportive' | 'analytical',
  preferred_times?: {
  morning: boolean,
    afternoon: boolean,
  evening: boolean,
    night: boolean },
  activity_preferences?: string[],
  availability_score: number,
    participation_rate: number,
  }
interface EventCategory {
  id: string,
    name: string,
  icon: any,
    color: string,
  frequency: number,
    average_duration: number,
  participation_rate: number,
    personality_preferences: string[] }
interface SmartSchedulingSuggestion { id: string,
    type: 'optimal_time' | 'activity_match' | 'participation' | 'conflict_avoidance',
  title: string,
    description: string,
  suggested_time: string,
    suggested_date: string,
  personality_compatibility: number,
    participation_prediction: number,
  reasoning: string,
    confidence_score: number },
  interface CalendarAnalytics { total_events: number,
    upcoming_events: number,
  participation_rate: number,
    average_attendance: number,
  most_popular_time: string,
    most_active_member: string,
  scheduling_efficiency: number,
    conflict_rate: number,
  satisfaction_score: number }
  interface EnhancedCalendarEvent { id: string,
    title: string,
  description: string,
    date: string,
  time: string,
    duration: number // in minutes,
  category: string,
    created_by: string,
  participants: CalendarMember[],
    invited_members: string[],
  location?: string
  is_recurring: boolean,
  recurrence_pattern?: 'daily' | 'weekly' | 'monthly'
  reminder_time?: number // minutes before event, ,
  is_mandatory: boolean
  max_participants?: number,
  status: 'scheduled' | 'confirmed' | 'cancelled' | 'completed',
    attendance_confirmed: string[],
  smart_scheduled: boolean
  personality_match_score?: number,
  optimal_time_score?: number
  created_at: string,
    updated_at: string },
  export default function EnhancedHouseholdCalendar() {
  const { authState  } = useAuth(),
  const theme = useTheme()
  const router = useRouter(),
  const { showSuccess, showError, ToastComponent } = useToast(),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [events, setEvents] = useState<EnhancedCalendarEvent[]>([]),
  const [members, setMembers] = useState<CalendarMember[]>([]),
  const [categories, setCategories] = useState<EventCategory[]>([]),
  const [analytics, setAnalytics] = useState<CalendarAnalytics | null>(null),
  const [suggestions, setSuggestions] = useState<SmartSchedulingSuggestion[]>([]),
  // Calendar states;
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]),
  const [markedDates, setMarkedDates] = useState<Record<string, any>>({}),
  const [currentMonth, setCurrentMonth] = useState<string>(
  new Date().toISOString().split('T')[0].substring(0, 7),
  );
  // Filter and modal states,
  const [selectedFilter, setSelectedFilter] = useState<, ,
  'all' | 'upcoming' | 'my_events' | 'mandatory'
  >('all'),
  const [selectedCategory, setSelectedCategory] = useState<string>('all'),
  const [showAddEventModal, setShowAddEventModal] = useState(false),
  const [showAnalyticsModal, setShowAnalyticsModal] = useState(false),
  // New event form state;
  const [newEvent, setNewEvent] = useState<{ title: string,
    description: string,
  category: string,
    time: string,
  duration: number,
    location: string,
  invited_members: string[],
    is_recurring: boolean,
  recurrence_pattern: 'daily' | 'weekly' | 'monthly',
    reminder_time: number,
  is_mandatory: boolean
    max_participants?: number,
  use_smart_scheduling: boolean }>({  title: '',
    description: '',
  category: '',
    time: '18: 00',
    duration: 60,
  location: '',
    invited_members: [],
  is_recurring: false,
    recurrence_pattern: 'weekly',
  reminder_time: 30,
    is_mandatory: false,
  max_participants: undefined,
    use_smart_scheduling: true  }),
  useEffect(() = > {
  fetchCalendarData() } []),
  useEffect(() => {
  updateMarkedDates() } [events, selectedDate]),
  const fetchCalendarData = async () => { if (!authState.user) return null;
    try {
  setLoading(true)
      // Mock comprehensive calendar data with lifestyle integration,
  const mockMembers: CalendarMember[] = [
  {
          id: authState.user.id,
    name: 'You',
  personality_type: 'ENFP',
    lifestyle_type: 'Social Butterfly',
  communication_style: 'diplomatic',
    preferred_times: {
  morning: false,
    afternoon: true,
  evening: true,
    night: false },
  activity_preferences: ['social', 'creative', 'outdoor'],
  availability_score: 78,
    participation_rate: 87,
  }
        { id: 'member-2',
    name: 'Sarah Johnson',
  personality_type: 'ISFJ',
    lifestyle_type: 'Organized Planner',
  communication_style: 'supportive',
    preferred_times: {
  morning: true,
    afternoon: true,
  evening: false,
    night: false },
  activity_preferences: ['organizing', 'planning', 'routine'],
  availability_score: 92,
    participation_rate: 95,
  }
        { id: 'member-3',
    name: 'Michael Chen',
  personality_type: 'INTJ',
    lifestyle_type: 'Night Owl',
  communication_style: 'direct',
    preferred_times: {
  morning: false,
    afternoon: false,
  evening: true,
    night: true },
  activity_preferences: ['independent', 'technical', 'analytical'],
  availability_score: 65,
    participation_rate: 72,
  }
      ], ,
  const mockCategories: EventCategory[] = [
  { id: 'household',
    name: 'Household',
  icon: Home,
    color: theme.colors.blue[500],
  frequency: 45,
    average_duration: 90,
  participation_rate: 88,
    personality_preferences: ['ISFJ', 'ESFJ'] },
  { id: 'social',
    name: 'Social',
  icon: Users,
    color: theme.colors.green[500],
  frequency: 30,
    average_duration: 120,
  participation_rate: 75,
    personality_preferences: ['ENFP', 'ESFP'] },
  { id: 'maintenance',
    name: 'Maintenance',
  icon: Settings,
    color: theme.colors.orange[500],
  frequency: 15,
    average_duration: 60,
  participation_rate: 65,
    personality_preferences: ['ISTJ', 'INTJ'] },
  { id: 'entertainment',
    name: 'Entertainment',
  icon: Tv,
    color: theme.colors.purple[500],
  frequency: 25,
    average_duration: 150,
  participation_rate: 82,
    personality_preferences: ['ENFP', 'ESFP', 'ENTP'] },
  { id: 'fitness',
    name: 'Fitness',
  icon: Dumbbell,
    color: theme.colors.red[500],
  frequency: 20,
    average_duration: 75,
  participation_rate: 58,
    personality_preferences: ['ESTP', 'ESFP'] },
   ],
  const mockEvents: EnhancedCalendarEvent[] = [
  {
          id: '1',
    title: 'Weekly House Meeting',
  description: 'Discuss household matters, budget, and upcoming events',
  date: new Date(Date.now() + 86400000 * 2).toISOString().split('T')[0],
    time: '19: 00',
    duration: 60,
  category: 'household',
    created_by: 'member-2',
  participants: mockMembers,
    invited_members: mockMembers.map(m = > m.id),
  is_recurring: true,
    recurrence_pattern: 'weekly',
  reminder_time: 30,
    is_mandatory: true,
  status: 'scheduled',
    attendance_confirmed: ['member-2'],
  smart_scheduled: true,
    personality_match_score: 85,
  optimal_time_score: 92,
    created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
  },
  {
  id: '2',
    title: 'Movie Night',
  description: 'Watch a movie together in the living room',
    date: new Date(Date.now() + 86400000 * 5).toISOString().split('T')[0],
  time: '20:30',
    duration: 150,
  category: 'entertainment',
    created_by: authState.user.id,
  participants: [mockMembers[0], mockMembers[1]],
  invited_members: mockMembers.map(m = > m.id),
    location: 'Living Room',
  is_recurring: false,
    reminder_time: 60,
  is_mandatory: false,
    max_participants: 4,
  status: 'scheduled',
    attendance_confirmed: [authState.user.id],
  smart_scheduled: true,
    personality_match_score: 78,
  optimal_time_score: 88,
    created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
  },
  {
  id: '3',
    title: 'Deep Cleaning Day',
  description: 'Monthly deep cleaning of all common areas',
    date: new Date(Date.now() + 86400000 * 7).toISOString().split('T')[0],
  time: '10:00',
    duration: 180,
  category: 'household',
    created_by: 'member-2',
  participants: mockMembers,
    invited_members: mockMembers.map(m = > m.id),
  is_recurring: true,
    recurrence_pattern: 'monthly',
  reminder_time: 1440, // 24 hours, ,
  is_mandatory: true,
    status: 'scheduled',
  attendance_confirmed: ['member-2', 'member-3'],
  smart_scheduled: true,
    personality_match_score: 72,
  optimal_time_score: 95,
    created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
  },
   ],
  const mockAnalytics: CalendarAnalytics = {  total_events: 12,
    upcoming_events: 8,
  participation_rate: 78,
    average_attendance: 2.4,
  most_popular_time: '19:00',
    most_active_member: 'member-2',
  scheduling_efficiency: 85,
    conflict_rate: 12,
  satisfaction_score: 87  };
  const mockSuggestions: SmartSchedulingSuggestion[] = [
  { id: '1',
    type: 'optimal_time',
  title: 'Optimal Time for House Meeting',
    description: 'Based on member availability, 7 PM on Sundays works best',
  suggested_time: '19:00',
    suggested_date: new Date(Date.now() + 86400000 * 7).toISOString().split('T')[0],
  personality_compatibility: 92,
    participation_prediction: 95,
  reasoning: 'All members prefer evening times, and Sarah (organizer) is most available',
  confidence_score: 88 }
        { id: '2',
    type: 'activity_match',
  title: 'Social Activity Recommendation',
    description: 'Movie night would be perfect for current household dynamics',
  suggested_time: '20:30',
    suggested_date: new Date(Date.now() + 86400000 * 3).toISOString().split('T')[0],
  personality_compatibility: 85,
    participation_prediction: 78,
  reasoning: 'ENFP and ESFP types in household enjoy entertainment activities',
    confidence_score: 82 },
  { id: '3',
    type: 'conflict_avoidance',
  title: 'Avoid Scheduling Conflicts',
    description: 'Michael has low availability on weekday mornings',
  suggested_time: '19:00',
    suggested_date: new Date(Date.now() + 86400000 * 4).toISOString().split('T')[0],
  personality_compatibility: 75,
    participation_prediction: 88,
  reasoning: 'Night owl lifestyle type prefers evening activities',
    confidence_score: 90 },
   ], ,
  setMembers(mockMembers)
      setCategories(mockCategories),
  setEvents(mockEvents)
      setAnalytics(mockAnalytics),
  setSuggestions(mockSuggestions)
    } catch (error) { logger.error('Error fetching calendar data', 'EnhancedHouseholdCalendar', {
  error: error instanceof Error ? error.message      : String(error),
    userId: authState.user? .id }),
  showError('Could not load calendar data')
    } finally {
  setLoading(false)
    },
  }
  const onRefresh = useCallback(async () => {
  setRefreshing(true)
    await fetchCalendarData(),
  setRefreshing(false)
  } []),
  const updateMarkedDates = () => {
  const marked : Record<string, any> = {},
  // Mark selected date
    marked[selectedDate] = {
  selected: true,
    selectedColor: theme.colors.primary[500] }
    // Mark dates with events,
  events.forEach(event = > {
  if (marked[event.date]) {
  if (marked[event.date].dots) {
  marked[event.date].dots.push({
  key: event.id),
    color: getEventColor(event) })
        } else {
  marked[event.date] = {
  ...marked[event.date], ,
  dots: [{ key: event.id, color: getEventColor(event) }],
  marked: true
          },
  }
      } else {
  marked[event.date] = {
  dots: [{ key: event.id, color: getEventColor(event) }], ,
  marked: true
        },
  }
    }),
  setMarkedDates(marked)
  },
  const getEventColor = ($2) => { const category = categories.find(c => c.id === event.category)
    return category? .color || theme.colors.primary[500] },
  const handleAddEvent = async () => {
  if (!newEvent.title || !newEvent.category) {
  showError('Please fill all required fields');
      return null }
    try {
  setLoading(true)
      // Smart scheduling logic,
  let optimizedTime = newEvent.time;
      let personalityMatchScore = undefined,
  let optimalTimeScore = undefined;
      if (newEvent.use_smart_scheduling) {
  // Find optimal time based on member preferences and availability;
        const invitedMembers = members.filter(m => newEvent.invited_members.includes(m.id)),
  const timePreferences = analyzeTimePreferences(invitedMembers)
        const categoryData = categories.find(c => c.id === newEvent.category),
  if (timePreferences && categoryData) {
          optimizedTime = findOptimalTime(timePreferences, newEvent.time),
  personalityMatchScore = calculatePersonalityMatch(invitedMembers, categoryData),
  optimalTimeScore = calculateOptimalTimeScore(timePreferences, optimizedTime) }
      },
  const eventData     : EnhancedCalendarEvent = {
        id: Date.now().toString(),
    title: newEvent.title,
  description: newEvent.description,
    date: selectedDate,
  time: optimizedTime,
    duration: newEvent.duration,
  category: newEvent.category,
    created_by: authState.user? .id || 'unknown',
  participants  : members.filter(m = > newEvent.invited_members.includes(m.id))
        invited_members: newEvent.invited_members,
    location: newEvent.location,
  is_recurring: newEvent.is_recurring,
    recurrence_pattern: newEvent.recurrence_pattern,
  reminder_time: newEvent.reminder_time,
    is_mandatory: newEvent.is_mandatory,
  max_participants: newEvent.max_participants,
    status: 'scheduled',
  attendance_confirmed: [authState.user? .id || 'unknown'],
  smart_scheduled   : newEvent.use_smart_scheduling
  personality_match_score: personalityMatchScore,
    optimal_time_score: optimalTimeScore,
  created_at: new Date().toISOString(),
    updated_at: new Date().toISOString() }
      setEvents([...events, eventData]),
  // Reset form, ,
  setNewEvent({  title: '',
    description: '',
  category: '',
    time: '18: 00',
    duration: 60,
  location: '',
    invited_members: [],
  is_recurring: false,
    recurrence_pattern: 'weekly',
  reminder_time: 30,
    is_mandatory: false,
  max_participants: undefined,
    use_smart_scheduling: true  }),
  setShowAddEventModal(false)
      showSuccess('Event created successfully!'),
  } catch (error) { logger.error('Error creating event', 'EnhancedHouseholdCalendar', {
  error: error instanceof Error ? error.message     : String(error),
    eventData: newEvent }),
  showError('Failed to create event')
    } finally {
  setLoading(false)
    },
  }
  const analyzeTimePreferences = (members: CalendarMember[]) => { const preferences = {
    morning: 0,
  afternoon: 0,
    evening: 0,
  night: 0 }
  members.forEach(member => { if (member.preferred_times) {
  if (member.preferred_times.morning) preferences.morning++
  if (member.preferred_times.afternoon) preferences.afternoon++,
  if (member.preferred_times.evening) preferences.evening++;
  if (member.preferred_times.night) preferences.night++ },
  })
  return preferences,
  }
  const findOptimalTime = ($2) => {
  const maxPreference = Math.max(...Object.values(preferences))
  const optimalPeriod = Object.keys(preferences).find(key => preferences[key] === maxPreference),
  switch (optimalPeriod) {;
      case 'morning':  ,
  return '09: 00';
      case 'afternoon':  ,
  return '14: 00';
  case 'evening':  ,
  return '19: 00';
  case 'night':  ,
  return '21: 00',
    default: return defaultTime }
  },
  const calculatePersonalityMatch = ($2) => {
  const matchingMembers = members.filter(member => {
  category.personality_preferences.includes(member.personality_type || '')
    ),
  return Math.round((matchingMembers.length / members.length) * 100)
  },
  const calculateOptimalTimeScore = ($2) => {
  const hour = parseInt(selectedTime.split(': ')[0]),
  let period = 'evening';

    if (hour >= 6 && hour < 12) period = 'morning',
  else if (hour >= 12 && hour < 17) period = 'afternoon';
    else if (hour >= 17 && hour < 22) period = 'evening',
  else period = 'night';

    const totalMembers = Object.values(preferences).reduce(
  (sum: number, count: any) = > sum + count,
  0;
    ),
  const periodPreference = preferences[period] || 0,
  return Math.round((periodPreference / totalMembers) * 100)
  },
  const getFilteredEvents = ($2) => {;
  let filtered = events // Filter by type,
  switch (selectedFilter) {
      case 'upcoming':  ,
  filtered = filtered.filter(event => new Date(event.date) >= new Date())
  break,
  case 'my_events':  
        filtered = filtered.filter(event => {
  event.created_by === authState.user? .id ||)
            event.participants.some(p => p.id === authState.user?.id),
  )
        break,
  case 'mandatory'     : filtered = filtered.filter(event => event.is_mandatory)
        break }
    // Filter by category,
  if (selectedCategory != = 'all') {
      filtered = filtered.filter(event => event.category === selectedCategory) }
    // Filter by selected date,
  filtered = filtered.filter(event => event.date === selectedDate)
    return filtered.sort((a,  b) => a.time.localeCompare(b.time)),
  }
  const formatTime = (time: string) => {
  const [hours, minutes] = time.split(': '),
  const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'PM'    : 'AM',
  const displayHour = hour % 12 || 12
    return `${displayHour}:${minutes} ${ampm}`
  }
  const formatDate = (dateString: string) => {
  const date = new Date(dateString)
    const today = new Date(),
  const diffTime = date.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)),
  if (diffDays = == 0) return 'Today';
    if (diffDays = == 1) return 'Tomorrow',
  if (diffDays = == -1) return 'Yesterday';
    return date.toLocaleDateString('en-US',  { month: 'short', day: 'numeric' }),
  }
  // Render functions,
  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: theme.colors.background}]}>,
  <View style={styles.headerContent}>
        <View>,
  <Text style={[styles.headerTitle, { color: theme.colors.text}]}>Household Calendar</Text>,
  <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary}]}>, ,
  Smart scheduling • Lifestyle integration, ,
  </Text>
        </View>,
  <TouchableOpacity
          style= {{ [styles.addButton, { backgroundColor: theme.colors.primary[500]  ] }]},
  onPress={() => setShowAddEventModal(true)} accessibilityLabel="Add new event";
          accessibilityRole= "button",
  >
          <Plus size= {24} color={{theme.colors.white} /}>,
  </TouchableOpacity>
      </View>,
  </View>
  ),
  const renderAnalyticsOverview = () => {;
  if (!analytics) return null,
  return (
    <View style= {[styles.analyticsContainer,  { backgroundColor: theme.colors.background}]}>,
  <ScrollView
          horizontal showsHorizontalScrollIndicator={false} style={styles.analyticsScroll},
  >
          <TouchableOpacity,
  style={{ [styles.analyticsCard, { backgroundColor: theme.colors.surface  ] }]},
  onPress={() => setShowAnalyticsModal(true)}
          >,
  <View style={styles.analyticsHeader}>
              <BarChart3 size={20} color={theme.colors.primary[500]} />,
  <Text style={[styles.analyticsValue, { color: theme.colors.text}]}>,
  {analytics.upcoming_events}
              </Text>,
  </View>
            <Text style={[styles.analyticsLabel, { color: theme.colors.textSecondary}]}>,
  Upcoming Events, ,
  </Text>
  </TouchableOpacity>,
  <View style= {[styles.analyticsCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.analyticsHeader}>
              <Users size={20} color={{theme.colors.green[500]} /}>,
  <Text style={[styles.analyticsValue, { color: theme.colors.text}]}>,
  {analytics.participation_rate}%, ,
  </Text>
            </View>,
  <Text style={[styles.analyticsLabel, { color: theme.colors.textSecondary}]}>,
  Participation Rate;
            </Text>,
  </View>
          <View style= {[styles.analyticsCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.analyticsHeader}>
              <Clock size={20} color={{theme.colors.blue[500]} /}>,
  <Text style={[styles.analyticsValue, { color: theme.colors.text}]}>,
  {analytics.most_popular_time}
              </Text>,
  </View>
            <Text style={[styles.analyticsLabel, { color: theme.colors.textSecondary}]}>,
  Popular Time, ,
  </Text>
          </View>,
  <View style={[styles.analyticsCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.analyticsHeader}>
              <Zap size={20} color={{theme.colors.yellow[500]} /}>,
  <Text style={[styles.analyticsValue, { color: theme.colors.text}]}>,
  {analytics.scheduling_efficiency}%, ,
  </Text>
  </View>,
  <Text style= {[styles.analyticsLabel, { color: theme.colors.textSecondary}]}>,
  Efficiency Score;
            </Text>,
  </View>
        </ScrollView>,
  </View>
    ),
  }
  const renderSmartSuggestions = () => {
  if (suggestions.length = == 0) return null;
    return (
  <View style= {[styles.suggestionsContainer,  { backgroundColor: theme.colors.background}]}>,
  <View style={styles.sectionHeader}>
          <Brain size={20} color={{theme.colors.primary[500]} /}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>,
  Smart Scheduling Suggestions, ,
  </Text>
        </View>,
  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {suggestions.map((suggestion, index) => (
  <TouchableOpacity key = {index} style={{ [
                styles.suggestionCard, ,
  {
                  backgroundColor: theme.colors.surface,
    borderLeftColor:  ,
  suggestion.type === 'optimal_time', ,
  ? theme.colors.blue[500],
  : suggestion.type === 'activity_match'
  ? theme.colors.green[500],
  : suggestion.type === 'participation'
                          ? theme.colors.purple[500], : theme.colors.orange[500]] },
   ]},
  >
              <View style={styles.suggestionHeader}>,
  <Text style={[styles.suggestionTitle, { color: theme.colors.text}]}>,
  {suggestion.title}
                </Text>,
  <View
                  style={{ [styles.confidenceBadge, { backgroundColor: theme.colors.success[500] + '20'  ] }]},
  >
                  <Text style={{[styles.confidenceText, { color: theme.colors.success[500]}]} }>suggestion.confidence_score}%,
  </Text>
                </View>,
  </View>
              <Text style={[styles.suggestionDescription, { color: theme.colors.textSecondary}]}>,
  {suggestion.description}
              </Text>,
  <View style={styles.suggestionMetrics}>
                <Text style={{[styles.suggestionTime, { color: theme.colors.info[500]}]} }>formatTime(suggestion.suggested_time)},
  </Text>
                <Text style={{[styles.suggestionMatch, { color: theme.colors.purple[500]}]} }>suggestion.personality_compatibility}% match, ,
  </Text>
              </View>,
  </TouchableOpacity>
          ))},
  </ScrollView>
      </View>,
  )
  },
  const renderCalendar = () => (
    <View style={[styles.calendarContainer, { backgroundColor: theme.colors.surface}]}>,
  <CalendarComponent current={selectedDate} onDayPress={day ={}> setSelectedDate(day.dateString)} onMonthChange={month => setCurrentMonth(month.dateString.substring(0, 7))} markedDates= {markedDates} markingType="multi-dot",
  theme={   backgroundColor: theme.colors.surface,
    calendarBackground: theme.colors.surface,
  textSectionTitleColor: theme.colors.textSecondary,
    selectedDayBackgroundColor: theme.colors.primary[500],
  selectedDayTextColor: theme.colors.white,
    todayTextColor: theme.colors.primary[500],
  dayTextColor: theme.colors.text,
    textDisabledColor: theme.colors.textSecondary,
  dotColor: theme.colors.primary[500],
    selectedDotColor: theme.colors.white,
  arrowColor: theme.colors.primary[500],
    monthTextColor: theme.colors.text,
  indicatorColor: theme.colors.primary[500],
    textDayFontWeight: '500',
  textMonthFontWeight: '600',
    textDayHeaderFontWeight: '600'    },
  />
    </View>,
  )
  const renderFilterBar = () => (
  <View style={[styles.filterContainer, { backgroundColor: theme.colors.background}]}>,
  <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
        {['all', 'upcoming', 'my_events', 'mandatory'].map(filter => (
  <TouchableOpacity key = {filter} style={{ [styles.filterButton, {
  backgroundColor: selectedFilter === filter ? theme.colors.primary[500]    : theme.colors.surface,
    borderColor: selectedFilter === filter ? theme.colors.primary[500]  : theme.colors.border)  ] },
   ]},
  onPress = {() => setSelectedFilter(filter as any)}
          >,
  <Text
              style={{ [styles.filterText, {
  color: selectedFilter === filter ? theme.colors.white   : theme.colors.text  ] }]},
  >
              {filter.charAt(0).toUpperCase() + filter.slice(1).replace('_' ' ')},
  </Text>
          </TouchableOpacity>,
  ))}
      </ScrollView>,
  <ScrollView horizontal showsHorizontalScrollIndicator = {false} style={styles.categoryScroll}>
        <TouchableOpacity,
  style={{ [styles.categoryButton, {
  backgroundColor: selectedCategory === 'all' ? theme.colors.primary[500]   : theme.colors.surface  ] },
   ]},
  onPress = {() => setSelectedCategory('all')}
        >,
  <Text
            style={{ [styles.categoryText, { color: selectedCategory === 'all' ? theme.colors.white  : theme.colors.text  ] },
   ]},
  >
            All Categories, ,
  </Text>
        </TouchableOpacity>,
  {categories.map(category = > {
  const IconComponent = category.icon, ,
  return (
    <TouchableOpacity key = {category.id} style={{ [styles.categoryButton, {
  backgroundColor: )
                    selectedCategory === category.id ? category.color   : theme.colors.surface  ] }]},
  onPress = {() => setSelectedCategory(category.id)}
            >,
  <IconComponent size={16} color={ selectedCategory === category.id ? theme.colors.white  : category.color  }
              />,
  <Text
                style={{ [styles.categoryText,
  { color: selectedCategory === category.id ? theme.colors.white  : theme.colors.text  ] }
                ]},
  >
                {category.name},
  </Text>
            </TouchableOpacity>,
  )
        })},
  </ScrollView>
    </View>,
  )
  const renderEventCard = (event: EnhancedCalendarEvent) => {
  const category = categories.find(c => c.id === event.category)
    const IconComponent = category? .icon || Calendar,
  return (
    <TouchableOpacity key= {event.id} style={{ [styles.eventCard, { backgroundColor  : theme.colors.surface  ] }]},
  onPress={() => router.push(`/household/event-details? id=${event.id}`)}
        accessibilityLabel={`${event.title} at ${formatTime(event.time)}`},
  accessibilityRole="button"
      >,
  <View style = {styles.eventHeader}>
          <View style={styles.eventInfo}>,
  <View style={styles.eventTitleRow}>
              <View,
  style={{ [styles.categoryIcon, { backgroundColor  : category? .color + '20' || theme.colors.gray[200]  ] },
   ]},
  >
                <IconComponent size={16} color={{category? .color || theme.colors.gray[500]} /}>,
  </View>
              <Text style={{ [styles.eventTitle { color  : theme.colors.text  ] }]} numberOfLines={1}>,
  {event.title}
              </Text>,
  {event.smart_scheduled && (
                <View style={{[styles.smartBadge, { backgroundColor: theme.colors.primary[500] + '20'}]}}>,
  <Brain size={12} color={{theme.colors.primary[500]} /}>,
  </View>
              )},
  {event.is_mandatory && (
                <View,
  style={{ [styles.mandatoryBadge, { backgroundColor: theme.colors.warning[500] + '20'  ] }]},
  >
                  <Star size={12} color={{theme.colors.warning[500]} /}>,
  </View>
              )},
  </View>
            <Text,
  style={{ [styles.eventDescription, { color: theme.colors.textSecondary  ] }]},
  numberOfLines={2}
            >,
  {event.description}
            </Text>,
  </View>
          <View style={styles.eventTime}>,
  <Text style={[styles.timeText, { color: theme.colors.text}]}>{formatTime(event.time)}</Text>,
  <Text style={[styles.durationText, { color: theme.colors.textSecondary}]}>,
  {event.duration}min
            </Text>,
  </View>
        </View>,
  <View style={styles.eventDetails}>
          <View style={styles.eventMetrics}>,
  <View style={styles.metricItem}>
              <Users size={14} color={{theme.colors.textSecondary} /}>,
  <Text style={[styles.metricText, { color: theme.colors.textSecondary}]}>,
  {event.attendance_confirmed.length}/{event.participants.length} confirmed, ,
  </Text>
            </View>,
  {event.location && (
              <View style={styles.metricItem}>,
  <MapPin size={14} color={{theme.colors.textSecondary} /}>
                <Text style={[styles.metricText, { color: theme.colors.textSecondary}]}>,
  {event.location}
                </Text>,
  </View>
            )},
  {event.is_recurring && (
              <View style={styles.metricItem}>,
  <Repeat size={14} color={{theme.colors.textSecondary} /}>
                <Text style={[styles.metricText, { color: theme.colors.textSecondary}]}>,
  {event.recurrence_pattern}
                </Text>,
  </View>
            )},
  </View>
          <View style={styles.eventFooter}>,
  {event.personality_match_score && (
              <View style={{[styles.matchScore, { backgroundColor: theme.colors.success[500] + '20'}]}}>,
  <Text style={{[styles.matchText, { color: theme.colors.success[500]}]} }>event.personality_match_score}% personality match,
  </Text>
              </View>,
  )}
            {event.optimal_time_score && (
  <View style={{[styles.timeScore, { backgroundColor: theme.colors.blue[500] + '20'}]}}>,
  <Text style={{[styles.timeScoreText, { color: theme.colors.blue[500]}]} }>event.optimal_time_score}% optimal time, ,
  </Text>
              </View>,
  )}
          </View>,
  {event.participants.length > 0 && (
            <View style={styles.participantsRow}>,
  <Text style={[styles.participantsLabel, { color: theme.colors.textSecondary}]}>,
  Participants: 
              </Text>,
  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {event.participants.slice(0, 3).map((participant, index) => (
  <View key={participant.id} style={styles.participantChip}>
                    <Text style={[styles.participantName, { color: theme.colors.text}]}>,
  {participant.name}
                    </Text>,
  {participant.personality_type && (
                      <Text style={[styles.participantType, { color: theme.colors.textSecondary}]}>,
  {participant.personality_type}
                      </Text>,
  )}
                  </View>,
  ))}
                {event.participants.length > 3 && (
  <View style={styles.participantChip}>
                    <Text style={[styles.participantName, { color: theme.colors.textSecondary}]}>,
  +{event.participants.length - 3} more, ,
  </Text>
                  </View>,
  )}
              </ScrollView>,
  </View>
          )},
  </View>
      </TouchableOpacity>,
  )
  },
  const renderEventsList = () => {
  const filteredEvents = getFilteredEvents(),
  if (filteredEvents.length === 0) {
      return (
  <View style={[styles.emptyState,  { backgroundColor: theme.colors.surface}]}>,
  <Calendar size={48} color={{theme.colors.textSecondary} /}>
          <Text style={[styles.emptyTitle, { color: theme.colors.text}]}>,
  No events on {formatDate(selectedDate)}
          </Text>,
  <Text style={[styles.emptyDescription, { color: theme.colors.textSecondary}]}>,
  Create your first event to get started with household scheduling, ,
  </Text>
  <TouchableOpacity,
  style= {{ [styles.emptyButton, { backgroundColor: theme.colors.primary[500]  ] }]},
  onPress={() => setShowAddEventModal(true)}
          >,
  <Plus size={20} color={{theme.colors.white} /}>
            <Text style={[styles.emptyButtonText, { color: theme.colors.white}]}>Add Event</Text>,
  </TouchableOpacity>
        </View>,
  )
    },
  return <View style={styles.eventsList}>{filteredEvents.map(renderEventCard)}</View>
  },
  // Modal components
  const renderAddEventModal = () => (
  <Modal visible= {showAddEventModal} animationType="slide", ,
  presentationStyle= "pageSheet", ,
  onRequestClose= {() => setShowAddEventModal(false)}
    >,
  <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.colors.background}]}>,
  <View style={[styles.modalHeader, { borderBottomColor: theme.colors.border}]}>,
  <TouchableOpacity onPress={() => setShowAddEventModal(false)} style={styles.modalCloseButton}
          >,
  <X size={24} color={{theme.colors.text} /}>
          </TouchableOpacity>,
  <Text style={[styles.modalTitle, { color: theme.colors.text}]}>Create Event</Text>,
  <TouchableOpacity onPress={handleAddEvent} style={{ [styles.modalSaveButton, { backgroundColor: theme.colors.primary[500]  ] }]},
  disabled={!newEvent.title || !newEvent.category}
          >,
  <Text style={[styles.modalSaveText, { color: theme.colors.white}]}>Create</Text>,
  </TouchableOpacity>
        </View>,
  <ScrollView style={styles.modalContent}>
          <View style={styles.formGroup}>,
  <Text style={[styles.formLabel, { color: theme.colors.text}]}>Event Title *</Text>,
  <TextInput
              style={{ [styles.formInput, { backgroundColor: theme.colors.surface, color: theme.colors.text, borderColor: theme.colors.border  ] }]},
  value={newEvent.title} onChangeText={   text => setNewEvent({ ...newEvent, title: text       })},
  placeholder="Enter event title", ,
  placeholderTextColor= {theme.colors.textSecondary}
            />,
  </View>
          <View style={styles.formGroup}>,
  <Text style={[styles.formLabel, { color: theme.colors.text}]}>Description</Text>,
  <TextInput
              style={{ [styles.formTextArea, { backgroundColor: theme.colors.surface, color: theme.colors.text, borderColor: theme.colors.border  ] }]},
  value={newEvent.description} onChangeText={   text => setNewEvent({ ...newEvent, description: text       })},
  placeholder="Enter event description";
              placeholderTextColor= {theme.colors.textSecondary},
  multiline;
              numberOfLines= {3},
  />
          </View>,
  <View style={styles.formGroup}>
            <Text style={[styles.formLabel, { color: theme.colors.text}]}>Category *</Text>,
  <ScrollView
              horizontal showsHorizontalScrollIndicator={false} style={styles.categorySelector},
  >
              {categories.map(category => {
  const IconComponent = category.icon), ,
  const isSelected = newEvent.category === category.id, ,
  return (
    <TouchableOpacity key = {category.id} style={{ [styles.categorySelectorItem, {
  backgroundColor: isSelected ? category.color      : theme.colors.surface,
    borderColor: isSelected ? category.color  : theme.colors.border)  ] }]},
  onPress={ () => setNewEvent({  ...newEvent category: category.id    })}
                  >,
  <IconComponent size={20} color={{isSelected ? theme.colors.white   : category.color} /}>
                    <Text,
  style={{ [styles.categorySelectorText,
  { color: isSelected ? theme.colors.white  : theme.colors.text  ] }
                      ]},
  >
                      {category.name},
  </Text>
                  </TouchableOpacity>,
  )
              })},
  </ScrollView>
          </View>,
  <View style={styles.formRow}>
            <View style={[styles.formGroup { flex: 1, marginRight: 8}]}>,
  <Text style={[styles.formLabel, { color: theme.colors.text}]}>Time</Text>,
  <TextInput
                style={{ [styles.formInput, {
  backgroundColor: theme.colors.surface,
    color: theme.colors.text,
  borderColor: theme.colors.border  ] }]},
  value={newEvent.time} onChangeText={   text => setNewEvent({ ...newEvent, time: text       })},
  placeholder="HH: MM"
                placeholderTextColor={theme.colors.textSecondary},
  />
            </View>,
  <View style={[styles.formGroup, { flex: 1, marginLeft: 8}]}>,
  <Text style={[styles.formLabel, { color: theme.colors.text}]}>Duration (min)</Text>,
  <TextInput
                style={{ [styles.formInput, {
  backgroundColor: theme.colors.surface,
    color: theme.colors.text,
  borderColor: theme.colors.border  ] }]},
  value={newEvent.duration.toString()} onChangeText={   text => setNewEvent({ ...newEvent, duration: parseInt(text) || 60       })},
  placeholder="60"
                placeholderTextColor= {theme.colors.textSecondary} keyboardType="numeric",
  />
            </View>,
  </View>
          <View style= {styles.formGroup}>,
  <Text style={[styles.formLabel, { color: theme.colors.text}]}>Location</Text>,
  <TextInput
              style={{ [styles.formInput, { backgroundColor: theme.colors.surface, color: theme.colors.text, borderColor: theme.colors.border  ] }]},
  value={newEvent.location} onChangeText={   text => setNewEvent({ ...newEvent, location: text       })},
  placeholder="Enter location (optional)", ,
  placeholderTextColor= {theme.colors.textSecondary}
            />,
  </View>
          <View style={styles.formGroup}>,
  <Text style={[styles.formLabel, { color: theme.colors.text}]}>Invite Members</Text>,
  <View style={styles.memberSelector}>
              {members.filter(m => m.id !== authState.user? .id),
  .map(member => (
                  <TouchableOpacity key = {member.id} style={{ [styles.memberOption, {
  backgroundColor     : newEvent.invited_members.includes(member.id)
                          ? theme.colors.primary[500],
  : theme.colors.surface
                        borderColor: theme.colors.border  ] },
   ]},
  onPress= { () => {
  const isSelected = newEvent.invited_members.includes(member.id),
  const updatedMembers = isSelected
                        ? newEvent.invited_members.filter(id = > id !== member.id),
  : [...newEvent.invited_members member.id],
  setNewEvent({  ...newEvent, invited_members: updatedMembers    }),
  }}
      >,
  <Text
                      style = { [styles.memberOptionText,
  {
                          color: newEvent.invited_members.includes(member.id),
  ? theme.colors.white, ,
  : theme.colors.text }]},
  >
                      {member.name},
  </Text>
                    { member.personality_type && (
  <Text
                        style = {[styles.memberPersonality,
  {
                            color: newEvent.invited_members.includes(member.id),
  ? theme.colors.white + '80'
                               : theme.colors.textSecondary }]},
  >
                        {member.personality_type} • {member.lifestyle_type},
  </Text>
                    )},
  </TouchableOpacity>
                ))},
  </View>
          </View>,
  <View style={styles.formGroup}>
            <View style={styles.toggleRow}>,
  <View>
                <Text style={[styles.formLabel { color: theme.colors.text}]}>Smart Scheduling</Text>,
  <Text style={[styles.formDescription, { color: theme.colors.textSecondary}]}>,
  Optimize timing based on member preferences and availability
                </Text>,
  </View>
              <TouchableOpacity,
  style = { [
                  styles.toggleButton, ,
  {
                    backgroundColor: newEvent.use_smart_scheduling,
  ? theme.colors.primary[500],
  : theme.colors.gray[300] },
   ]},
  onPress= { () => {
  setNewEvent({  ...newEvent, use_smart_scheduling: !newEvent.use_smart_scheduling    }),
  }
              >,
  <View
                  style = {[
                    styles.toggleIndicator, ,
  {
                      backgroundColor: theme.colors.white,
    transform: [{ translateX: newEvent.use_smart_scheduling ? 20    : 2 }],
  }
                  ]},
  />
              </TouchableOpacity>,
  </View>
          </View>,
  <View style={styles.formGroup}>
            <View style={styles.toggleRow}>,
  <View>
                <Text style={[styles.formLabel, { color: theme.colors.text}]}>Recurring Event</Text>,
  <Text style={[styles.formDescription, { color: theme.colors.textSecondary}]}>,
  Repeat this event on a regular schedule
                </Text>,
  </View>
              <TouchableOpacity,
  style = {[
                  styles.toggleButton, ,
  {
                    backgroundColor: newEvent.is_recurring ? theme.colors.green[500]    : theme.colors.gray[300] }
                ]},
  onPress={ () => setNewEvent({  ...newEvent, is_recurring: !newEvent.is_recurring    })},
  >
                <View,
  style={{ [styles.toggleIndicator, {
  backgroundColor: theme.colors.white,
    transform: [{ translateX: newEvent.is_recurring ? 20   : 2  ] }],
  }
                  ]},
  />
              </TouchableOpacity>,
  </View>
          </View>,
  {newEvent.is_recurring && (
            <View style={styles.formGroup}>,
  <Text style={[styles.formLabel, { color: theme.colors.text}]}>Repeat Pattern</Text>,
  <View style={styles.recurrenceSelector}>
                {['daily', 'weekly', 'monthly'].map(pattern => (
  <TouchableOpacity key = {pattern} style={{ [
                      styles.recurrenceOption,
  {
                        backgroundColor:  ,
  newEvent.recurrence_pattern === pattern, ,
  ? theme.colors.green[500])  : theme.colors.surface,
  borderColor: theme.colors.border)] },
   ]},
  onPress={ () => setNewEvent({  ...newEvent, recurrence_pattern: pattern as any    })},
  >
                    <Text,
  style={{ [styles.recurrenceOptionText, {
  color:  
                            newEvent.recurrence_pattern === pattern ? theme.colors.white   : theme.colors.text  ] }]},
  >
                      {pattern.charAt(0).toUpperCase() + pattern.slice(1)},
  </Text>
                  </TouchableOpacity>,
  ))}
              </View>,
  </View>
          )},
  <View style={styles.formGroup}>
            <View style={styles.toggleRow}>,
  <View>
                <Text style={[styles.formLabel { color: theme.colors.text}]}>Mandatory Event</Text>,
  <Text style={[styles.formDescription, { color: theme.colors.textSecondary}]}>,
  Mark as required attendance for all household members
                </Text>,
  </View>
              <TouchableOpacity,
  style = {[
                  styles.toggleButton, ,
  {
                    backgroundColor: newEvent.is_mandatory ? theme.colors.warning[500]   : theme.colors.gray[300] }
                ]},
  onPress={ () => setNewEvent({  ...newEvent, is_mandatory: !newEvent.is_mandatory    })},
  >
                <View,
  style={{ [styles.toggleIndicator, {
  backgroundColor: theme.colors.white,
    transform: [{ translateX: newEvent.is_mandatory ? 20   : 2  ] }],
  }
                  ]},
  />
              </TouchableOpacity>,
  </View>
          </View>,
  </ScrollView>
      </SafeAreaView>,
  </Modal>
  ),
  if (loading) {
    return (
  <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={   headerShown: false        } />
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary[500]} /}>,
  <Text style={[styles.loadingText, { color: theme.colors.textSecondary}]}>,
  Loading calendar...
          </Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={   headerShown: false       } />
      <ScrollView style={styles.scrollView} refreshControl={
  <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[theme.colors.primary[500]]} tintColor={theme.colors.primary[500]},
  />
        },
  showsVerticalScrollIndicator={false}
      >,
  {renderHeader()}
        {renderAnalyticsOverview()},
  {renderSmartSuggestions()}
        {renderCalendar()},
  {renderFilterBar()}
        {renderEventsList()},
  </ScrollView>
      {renderAddEventModal()},
  <ToastComponent />
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({ container: {
    flex: 1 },
  scrollView: { flex: 1 }
  loadingContainer: { flex: 1,
    justifyContent: 'center'),
  alignItems: 'center'),
    padding: 20 },
  loadingText: { marginTop: 12,
    fontSize: 16 },
  header: { paddingHorizontal: 20,
    paddingVertical: 16,
  borderBottomWidth: 1),
    borderBottomColor: 'rgba(0,0,0,0.1)' },
  headerContent: {
    flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  headerTitle: { fontSize: 24,
    fontWeight: '700',
  marginBottom: 4 }
  headerSubtitle: {
    fontSize: 14,
  fontWeight: '500'
  },
  addButton: {
    width: 48,
  height: 48,
    borderRadius: 24,
  justifyContent: 'center',
    alignItems: 'center',
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
  },
  analyticsContainer: { paddingVertical: 16 }
  analyticsScroll: { paddingHorizontal: 20 },
  analyticsCard: {
    width: 120,
  padding: 16,
    marginRight: 12,
  borderRadius: 12,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  analyticsHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  analyticsValue: {
    fontSize: 20,
  fontWeight: '700'
  },
  analyticsLabel: {
    fontSize: 12,
  fontWeight: '500'
  },
  suggestionsContainer: { paddingVertical: 16 }
  sectionHeader: { flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 20,
    marginBottom: 12 },
  sectionTitle: { fontSize: 18,
    fontWeight: '600',
  marginLeft: 8 }
  suggestionCard: {
    width: 280,
  padding: 16,
    marginLeft: 20,
  borderRadius: 12,
    borderLeftWidth: 4,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  suggestionHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: 8 },
  suggestionTitle: { fontSize: 14,
    fontWeight: '600',
  flex: 1,
    marginRight: 8 },
  confidenceBadge: { paddingHorizontal: 8,
    paddingVertical: 2,
  borderRadius: 8 }
  confidenceText: {
    fontSize: 10,
  fontWeight: '600'
  },
  suggestionDescription: { fontSize: 12,
    lineHeight: 16,
  marginBottom: 8 }
  suggestionMetrics: {
    flexDirection: 'row',
  justifyContent: 'space-between'
  },
  suggestionTime: {
    fontSize: 12,
  fontWeight: '600'
  },
  suggestionMatch: {
    fontSize: 12,
  fontWeight: '600'
  },
  calendarContainer: {
    margin: 20,
  borderRadius: 12,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  filterContainer: { paddingVertical: 12 }
  filterScroll: { paddingHorizontal: 20,
    marginBottom: 12 },
  filterButton: { flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 16,
    paddingVertical: 8,
  marginRight: 8,
    borderRadius: 20,
  borderWidth: 1 }
  filterText: {
    fontSize: 14,
  fontWeight: '500'
  },
  categoryScroll: { paddingHorizontal: 20 }
  categoryButton: { flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 12,
    paddingVertical: 6,
  marginRight: 8,
    borderRadius: 16 },
  categoryText: { fontSize: 12,
    fontWeight: '500',
  marginLeft: 4 }
  eventsList: { padding: 20 },
  eventCard: {
    padding: 16,
  marginBottom: 12,
    borderRadius: 12,
  shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
    shadowRadius: 2,
  elevation: 1
  },
  eventHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'flex-start',
    marginBottom: 12 },
  eventInfo: { flex: 1,
    marginRight: 12 },
  eventTitleRow: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 4 }
  categoryIcon: { width: 24,
    height: 24,
  borderRadius: 12,
    justifyContent: 'center',
  alignItems: 'center',
    marginRight: 8 },
  eventTitle: { fontSize: 16,
    fontWeight: '600',
  flex: 1 }
  smartBadge: { width: 20,
    height: 20,
  borderRadius: 10,
    justifyContent: 'center',
  alignItems: 'center',
    marginLeft: 8 },
  mandatoryBadge: { width: 20,
    height: 20,
  borderRadius: 10,
    justifyContent: 'center',
  alignItems: 'center',
    marginLeft: 4 },
  eventDescription: { fontSize: 14,
    lineHeight: 18 },
  eventTime: {
    alignItems: 'flex-end' }
  timeText: {
    fontSize: 16,
  fontWeight: '600'
  },
  durationText: { fontSize: 12,
    marginTop: 2 },
  eventDetails: { borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  paddingTop: 12 }
  eventMetrics: { marginBottom: 12 },
  metricItem: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 4 }
  metricText: { fontSize: 12,
    marginLeft: 6 },
  eventFooter: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 12 }
  matchScore: { paddingHorizontal: 8,
    paddingVertical: 2,
  borderRadius: 8,
    marginRight: 8 },
  matchText: {
    fontSize: 10,
  fontWeight: '600'
  },
  timeScore: { paddingHorizontal: 8,
    paddingVertical: 2,
  borderRadius: 8 }
  timeScoreText: {
    fontSize: 10,
  fontWeight: '600'
  },
  participantsRow: { borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  paddingTop: 8 }
  participantsLabel: { fontSize: 12,
    fontWeight: '500',
  marginBottom: 6 }
  participantChip: { backgroundColor: 'rgba(0,0,0,0.05)',
  paddingHorizontal: 8,
    paddingVertical: 4,
  borderRadius: 8,
    marginRight: 8,
  minWidth: 80 }
  participantName: {
    fontSize: 11,
  fontWeight: '600'
  },
  participantType: { fontSize: 9,
    marginTop: 1 },
  emptyState: {
    padding: 40,
  margin: 20,
    borderRadius: 12,
  alignItems: 'center'
  },
  emptyTitle: { fontSize: 18,
    fontWeight: '600',
  marginTop: 16,
    marginBottom: 8 },
  emptyDescription: { fontSize: 14,
    textAlign: 'center',
  marginBottom: 20 }
  emptyButton: { flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 20,
    paddingVertical: 12,
  borderRadius: 8 }
  emptyButtonText: { fontSize: 14,
    fontWeight: '600',
  marginLeft: 8 }
  modalContainer: { flex: 1 },
  modalHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingHorizontal: 20,
  paddingVertical: 16,
    borderBottomWidth: 1 },
  modalCloseButton: {
    width: 40,
  height: 40,
    justifyContent: 'center',
  alignItems: 'center'
  },
  modalTitle: {
    fontSize: 18,
  fontWeight: '600',
    flex: 1,
  textAlign: 'center'
  },
  modalSaveButton: { paddingHorizontal: 16,
    paddingVertical: 8,
  borderRadius: 8 }
  modalSaveText: {
    fontSize: 14,
  fontWeight: '600'
  },
  modalContent: { flex: 1,
    padding: 20 },
  formGroup: { marginBottom: 20 }
  formLabel: { fontSize: 14,
    fontWeight: '600',
  marginBottom: 8 }
  formDescription: { fontSize: 12,
    marginTop: 2 },
  formInput: { paddingHorizontal: 16,
    paddingVertical: 12,
  borderRadius: 8,
    borderWidth: 1,
  fontSize: 14 }
  formTextArea: {
    paddingHorizontal: 16,
  paddingVertical: 12,
    borderRadius: 8,
  borderWidth: 1,
    fontSize: 14,
  minHeight: 80,
    textAlignVertical: 'top' }
  formRow: {
    flexDirection: 'row' }
  categorySelector: { marginTop: 8 },
  categorySelectorItem: { flexDirection: 'row',
    alignItems: 'center',
  paddingHorizontal: 12,
    paddingVertical: 8,
  marginRight: 8,
    borderRadius: 8,
  borderWidth: 1 }
  categorySelectorText: { fontSize: 12,
    fontWeight: '500',
  marginLeft: 6 }
  memberSelector: { marginTop: 8 },
  memberOption: { paddingHorizontal: 16,
    paddingVertical: 12,
  marginBottom: 8,
    borderRadius: 8,
  borderWidth: 1 }
  memberOptionText: {
    fontSize: 14,
  fontWeight: '500'
  },
  memberPersonality: { fontSize: 12,
    marginTop: 2 },
  toggleRow: {
    flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  toggleButton: { width: 44,
    height: 24,
  borderRadius: 12,
    justifyContent: 'center',
  paddingHorizontal: 2 }
  toggleIndicator: { width: 20,
    height: 20,
  borderRadius: 10 }
  recurrenceSelector: { flexDirection: 'row',
    marginTop: 8 },
  recurrenceOption: {
    flex: 1,
  paddingVertical: 8,
    marginRight: 8,
  borderRadius: 8,
    borderWidth: 1,
  alignItems: 'center'
  },
  recurrenceOptionText: {
    fontSize: 12,
  fontWeight: '500'
  },
  })