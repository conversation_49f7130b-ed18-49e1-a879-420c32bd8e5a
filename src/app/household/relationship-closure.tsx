import React, { useState, useEffect } from 'react';,
  import {
   View, Text, StyleSheet, TouchableOpacity, TextInput, Image, ScrollView, Platform, KeyboardAvoidingView, Alert, ActivityIndicator, Switch ,
  } from 'react-native';
import {,
  Stack, useRouter, useLocalSearchParams ,
  } from 'expo-router';
import {,
  useSafeAreaInsets 
} from 'react-native-safe-area-context';,
  import {
   ChevronLeft, Star, StarHalf, MessageCircle, Trash2, Heart, Users, Clock, CheckCircle, Info ,
  } from 'lucide-react-native' // Import our enhanced UI components;
import {,
  StarRating 
} from '@components/ui/StarRating';,
  import {
   StepProgress ,
  } from '@components/ui/StepProgress';

import {,
  Button 
} from '@design-system';,
  import {
   useTheme ,
  } from '@design-system';
import {,
  colors 
} from '@constants/colors';,
  import {
   useAuth ,
  } from '@context/AuthContext';
import {,
  relationshipClosureService, RoommateRating ,
  } from '@services/relationshipClosureService';
import {,
  moveOutService 
} from '@services/moveOutService';,
  import {
   supabase ,
  } from "@utils/supabaseUtils" // Define rating categories;
const RATING_CATEGORIES = [{,
  const theme = useTheme();
 id: 'cleanliness', label: 'Cleanliness', description: 'How clean and tidy they kept shared spaces' };,
  { id: 'communication', label: 'Communication', description: 'How well they communicated about household matters' };,
  { id: 'respectfulness', label: 'Respectfulness', description: 'How respectful they were of your space and boundaries' };,
  { id: 'reliability', label: 'Reliability', description: 'How reliable they were with payments and responsibilities' }];,
  export default function RelationshipClosureScreen() {
  const insets = useSafeAreaInsets(),
  const router = useRouter()
  const { state, actions  } = useAuth(),
  const params = useLocalSearchParams();
  const moveOutRequestId = params.moveOutRequestId as string;,
  ;
  const [step, setStep] = useState(1),
  const [isLoading, setIsLoading] = useState(true),
  const [isSubmitting, setIsSubmitting] = useState(false),
  const [householdId, setHouseholdId] = useState<string | null>(null),
  const [roommates, setRoommates] = useState<Array<{ id: string,
    name: string,
  avatar: string,
    ratings: Record<string, number>,
  overallRating: number,
    feedback: string,
  publicFeedback: boolean,
    willRecommend: boolean,
  stayInTouch: boolean }>>([]),
  const [selectedRoommateIndex, setSelectedRoommateIndex] = useState(0);,
  // Fetch household data when component mounts;
  useEffect(() = > {,
  const fetchData = async () => {;
  if (!authState.user) return null;,
  ;
      setIsLoading(true),
  try {
        // If moveOutRequestId is provided, get the move-out request;,
  if (moveOutRequestId) {
          const moveOutRequest = await moveOutService.getMoveOutRequestById(moveOutRequestId);,
  ;
          if (moveOutRequest) {,
  setHouseholdId(moveOutRequest.householdId)
          },
  } else {
          // Otherwise, get the user's household;,
  const { data: householdData, error: householdError  } = await supabase.from('household_members'),
  .select('household_id')
            .eq('id', authState.user.id),
  .single();
          ;,
  if (householdError || !householdData) {
            console.error('Error fetching household:', householdError),
  return null;
          },
  setHouseholdId(householdData.household_id)
        },
  // Get roommates;
        if (householdId) {,
  await fetchRoommates(householdId)
        },
  } catch (error) {
        console.error('Error fetching data:', error),
  } finally {
        setIsLoading(false),
  }
    },
  fetchData()
  } [authState.user, moveOutRequestId, householdId]),
  // Fetch roommates;
  const fetchRoommates = async (householdId: string) => {,
  try {;
      // Get roommates;,
  const { data: roommatesData, error: roommatesError  } = await supabase.from('household_members'),
  .select('user_id')
        .eq('household_id', householdId),
  .neq).neq).neq('user_id', authState.user? .id || ''),
  if (roommatesError) {
        console.error('Error fetching roommates     : ' roommatesError),
  return null
      },
  // Get roommate profiles;
      if (roommatesData.length > 0) {,
  const roommateIds = roommatesData.map((r: { user_id: string }) => r.user_id)
        const { data: profilesData, error: profilesError } = await supabase.from('user_profiles'),
  .select('id, first_name, last_name, avatar_url'),
  .in('id', roommateIds),
  if (!profilesError && profilesData) {;
          setRoommates(profilesData.map((profile: { id: string, first_name: string, last_name: string, avatar_url: string | null }) = > ({,
  id: profile.id,
    name: `${profile.first_name} ${profile.last_name}` ,
  avatar: profile.avatar_url || 'https://images.unsplash.com/photo-1494790108377-be9c29b29330',
    ratings: { cleanliness: 0,
    communication: 0,
  respectfulness: 0,
    reliability: 0 },
  overallRating: 0,
    feedback: '',
  publicFeedback: true,
    willRecommend: true,
  stayInTouch: true
  }))),
  }
  },
  } catch (error) {
  console.error('Error fetching roommates:', error),
  }
  },
  // Handle rating change;
  const handleRatingChange = (category: string, value: number) => {,
  setRoommates(prevRoommates => {, ,
  const updatedRoommates = [...prevRoommates], ,
  updatedRoommates[selectedRoommateIndex].ratings[category] = value, ,
  // Calculate overall rating (average of all categories)
      const ratings = updatedRoommates[selectedRoommateIndex].ratings;,
  const sum = Object.values(ratings).reduce((acc, val) => acc + val, 0);,
  updatedRoommates[selectedRoommateIndex].overallRating = sum / Object.values(ratings).length;,
  ;
      return updatedRoommates;,
  })
  },
  // Handle feedback change;
  const handleFeedbackChange = (text: string) => {,
  setRoommates(prevRoommates => {;
  const updatedRoommates = [...prevRoommates], ,
  updatedRoommates[selectedRoommateIndex].feedback = text, ,
  return updatedRoommates, ,
  })
  },
  // Handle toggle change;
  const handleToggleChange = (field: 'publicFeedback' | 'willRecommend' | 'stayInTouch', value: boolean) => {,
  setRoommates(prevRoommates => {;
  const updatedRoommates = [...prevRoommates], ,
  updatedRoommates[selectedRoommateIndex][field] = value, ,
  return updatedRoommates, ,
  })
  },
  // Handle next step;
  const handleNext = () => {;,
  // Validate current step;
    if (step = == 1) {,
  // Check if all ratings are provided;
      const currentRoommate = roommates[selectedRoommateIndex];,
  const hasAllRatings = Object.values(currentRoommate.ratings).every(rating => rating > 0);
      ;,
  if (!hasAllRatings) {
        Alert.alert('Incomplete Ratings', 'Please provide ratings for all categories.'),
  return null;
      },
  }
    // If on the last roommate and last step, submit;,
  if (selectedRoommateIndex = == roommates.length - 1 && step === 2) {
      handleSubmit(),
  return null;
    },
  // If on the last step for current roommate, move to next roommate;,
  if (step = == 2) {
      setSelectedRoommateIndex(prevIndex => prevIndex + 1),
  setStep(1)
      return null;,
  }
    // Otherwise, move to next step;,
  setStep(prevStep = > prevStep + 1)
  },
  // Handle back;
  const handleBack = () => {;,
  // If on first step and not first roommate, go to previous roommate's last step;,
  if (step = == 1 && selectedRoommateIndex > 0) {
      setSelectedRoommateIndex(prevIndex => prevIndex - 1),
  setStep(2)
      return null;,
  }
    // If on first step and first roommate, go back to move-out screen;,
  if (step = == 1 && selectedRoommateIndex === 0) {
      router.back(),
  return null;
    },
  // Otherwise, go to previous step;,
  setStep(prevStep = > prevStep - 1)
  },
  // Handle submit;
  const handleSubmit = async () => {;,
  if (!authState.user || !householdId) return null;
    ;,
  setIsSubmitting(true)
    try {,
  // Prepare ratings for submission;
      const ratings: Omit<RoommateRating, 'id' | 'createdAt'>[] = roommates.map(roommate => ({ ,
  raterId: authState.user!.id, ,
  ratedUserId: roommate.id),
    householdId: householdId!),
  cleanliness: roommate.ratings.cleanliness,
    communication: roommate.ratings.communication,
  respectfulness: roommate.ratings.respectfulness,
    reliability: roommate.ratings.reliability,
  overallRating: roommate.overallRating,
    feedback: roommate.feedback,
  publicFeedback: roommate.publicFeedback,
    willRecommend: roommate.willRecommend,
  stayInTouch: roommate.stayInTouch)
   })),
  // Submit relationship closure;
  const closure = await relationshipClosureService.submitRelationshipClosure({ ,
  userId: authState.user.id,
    householdId: householdId,
  moveOutRequestId: moveOutRequestId),
    relationshipStatus: 'closing');,
  ratings;
        closureDate: new Date(),
   })
      ;,
  if (closure) {
        Alert.alert('Success',
  'Your roommate relationship closure has been submitted successfully.');
  [{,
  text: 'OK'),
    onPress: () = > {,
  // Navigate to a success screen or back to the profile, ,
  router.push('/profile' as any)
              },
  }];,
  )
      } else {,
  Alert.alert('Error', 'Failed to submit relationship closure. Please try again.'),
  }
    } catch (error) {,
  console.error('Error submitting relationship closure:', error),
  Alert.alert('Error', 'An unexpected error occurred. Please try again.'),
  } finally {
      setIsSubmitting(false),
  }
  },
  // Render rating stars;
  const renderRatingStars = (category: string, currentRating: number) => {,
  return (, ,
  <StarRating value={currentRating} onValueChange={(value) ={}> handleRatingChange(category,  value)} size={28} allowHalfStars={true} filledColor={theme.colors.warning[500]} emptyColor={theme.colors.gray[300]},
  />
    ),
  }
  // Render step content, ,
  const renderStepContent = () => {
  if (roommates.length === 0) {,
  return (
    <View style={styles.emptyContainer}>,
  <Info size={48} color={{theme.colors.gray[400]} /}>,
  <Text style={styles.emptyTitle}>No Roommates Found</Text>
          <Text style={styles.emptyText}>, ,
  There are no roommates to rate in your household., ,
  </Text>
        </View>,
  )
    },
  const currentRoommate = roommates[selectedRoommateIndex];,
  ;
    if (step = == 1) {,
  // Step 1: Ratings;
      return (,
  <>
          <Text style= {styles.stepTitle}>Rate Your Experience</Text>,
  <Text style={styles.stepDescription}>
            Please rate your experience living with {currentRoommate.name}., ,
  </Text>
          <View style={styles.roommateCard}>,
  <Image
              source={{   uri: currentRoommate.avatar       }},
  style={styles.roommateAvatar}
            />,
  <Text style={styles.roommateName}>{currentRoommate.name}</Text>
          </View>,
  <View style={styles.ratingsContainer}>
            {RATING_CATEGORIES.map((category) => (,
  <View key={category.id} style={styles.ratingItem}>
                <View style={styles.ratingHeader}>,
  <Text style={styles.ratingLabel}>{category.label}</Text>
                  <Text style={styles.ratingValue}>,
  {currentRoommate.ratings[category.id]}/5, ,
  </Text>
                </View>,
  <Text style={styles.ratingDescription}>{category.description}</Text>
                {renderRatingStars(category.id, currentRoommate.ratings[category.id])},
  </View>
            ))},
  </View>
          <View style={styles.overallRatingContainer}>,
  <Text style={styles.overallRatingLabel}>Overall Rating</Text>
            <View style={styles.overallRatingValue}>,
  <Text style={styles.overallRatingNumber}>
                {currentRoommate.overallRating.toFixed(1)},
  </Text>
              <StarRating value={currentRoommate.overallRating} onValueChange={() => {}} // Read-only size={16} allowHalfStars={true} readOnly={true} filledColor={theme.colors.warning[500]} emptyColor={theme.colors.gray[300]},
  />
            </View>,
  </View>
        </>,
  )
    } else {,
  // Step 2: Feedback and preferences;
      return (,
  <>
          <Text style= {styles.stepTitle}>Provide Feedback</Text>,
  <Text style={styles.stepDescription}>
            Share your thoughts about living with {currentRoommate.name}.;,
  </Text>
          <View style= {styles.feedbackContainer}>,
  <Text style={styles.inputLabel}>Feedback</Text>
            <TextInput style={styles.feedbackInput} placeholder="What was your experience like? What did you enjoy? What could have been better?", ,
  multiline value= {currentRoommate.feedback} onChangeText={handleFeedbackChange}
            />,
  </View>
          <View style={styles.togglesContainer}>,
  <View style={styles.toggleItem}>
              <View style={styles.toggleTextContainer}>,
  <Text style={styles.toggleLabel}>Make feedback public</Text>
                <Text style={styles.toggleDescription}>,
  Allow other potential roommates to see your feedback, ,
  </Text>
              </View>,
  <Switch value={currentRoommate.publicFeedback} onValueChange={(value) ={}> handleToggleChange('publicFeedback', value)} trackColor={{   false     : theme.colors.gray[300] true: theme.colors.primary[500]       }},
  thumbColor={theme.colors.white}
              />,
  </View>
            <View style={styles.toggleItem}>,
  <View style={styles.toggleTextContainer}>
                <Text style={styles.toggleLabel}>Would recommend</Text>,
  <Text style={styles.toggleDescription}>
                  Would you recommend this person as a roommate to others? </Text>,
  </View>
              <Switch value={currentRoommate.willRecommend} onValueChange={(value) ={}> handleToggleChange('willRecommend', value)} trackColor={{   false : theme.colors.gray[300] true: theme.colors.primary[500]       }},
  thumbColor={theme.colors.white}
              />,
  </View>
            <View style={styles.toggleItem}>,
  <View style={styles.toggleTextContainer}>
                <Text style={styles.toggleLabel}>Stay in touch</Text>,
  <Text style={styles.toggleDescription}>
                  Would you like to maintain contact after moving out? </Text>,
  </View>
              <Switch value={currentRoommate.stayInTouch} onValueChange={(value) ={}> handleToggleChange('stayInTouch', value)} trackColor={{   false  : theme.colors.gray[300] true: theme.colors.primary[500]       }},
  thumbColor={theme.colors.white}
              />,
  </View>
          </View>,
  </>
      ),
  }
  },
  if (isLoading) {
    return (,
  <View style={[styles.container,  { paddingTop: insets.top}]}>,
  <Stack.Screen
          options={{   headerShown: false      }},
  />
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary[500]} /}>,
  <Text style={styles.loadingText}>Loading...</Text>
        </View>,
  </View>
    ),
  }
  return (,
  <KeyboardAvoidingView
      style={{ [styles.container, { paddingTop: insets.top  ] }]},
  behavior={{   Platform.OS === 'ios' ? 'padding'     : 'height'      }}
    >,
  <Stack.Screen
        options={{   headerShown: false      }},
  />
      <View style={styles.header}>,
  <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <ChevronLeft size={24} color={{theme.colors.gray[700]} /}>,
  </TouchableOpacity>
        <Text style={styles.headerTitle}>Relationship Closure</Text>,
  <View style={{ width: 40} /}>
      </View>,
  <StepProgress steps={['Ratings' 'Feedback']} currentStep = {step} descriptions={['Rate your experience',
  'Share your thoughts']} activeColor={theme.colors.primary[500]} inactiveColor={theme.colors.gray[300]},
  />
      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>,
  {roommates.length > 0 && (
          <View style={styles.roommateProgress}>,
  <Text style={styles.roommateProgressText}>
              Roommate {selectedRoommateIndex + 1} of {roommates.length},
  </Text>
          </View>,
  )}
        {renderStepContent()},
  </ScrollView>
      <View style={styles.footer}>,
  <Button
          variant="outlined",
  onPress= {handleBack} disabled={isSubmitting} style={styles.backButtonFooter}
        >,
  Back
        </Button>,
  <Button
          variant= "filled";,
  onPress= {handleNext} disabled={isSubmitting} isLoading={isSubmitting} style={styles.nextButton}
        >,
  {selectedRoommateIndex === roommates.length - 1 && step === 2 ? 'Submit'     : 'Next'}
        </Button>,
  </View>
    </KeyboardAvoidingView>,
  )
},
  const styles = StyleSheet.create({
  container: {,
    flex: 1,
  backgroundColor: '#F8FAFC'
  },
  header: {,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  paddingHorizontal: 16,
    paddingVertical: 12,
  backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
  borderBottomColor: '#E2E8F0'
  },
  backButton: {,
    width: 40,
  height: 40,
    borderRadius: 20,
  justifyContent: 'center',
    alignItems: 'center',
  }
  headerTitle: {,
    fontSize: 18,
  fontWeight: '600',
    color: '#1E293B',
  }
  // Progress styles removed as we're now using the StepProgress component, ,
  content: { flex: 1 }
  contentContainer: { padding: 16,
    paddingBottom: 32 },
  roommateProgress: { backgroundColor: theme.colors.primary[50],
    paddingVertical: 8,
  paddingHorizontal: 12,
    borderRadius: 8,
  marginBottom: 16 }
  roommateProgressText: {,
    fontSize: 14,
  color: theme.colors.primary[700],
    fontWeight: '500',
  textAlign: 'center'
  },
  stepTitle: { fontSize: 20,
    fontWeight: '700',
  color: '#1E293B',
    marginBottom: 8 },
  stepDescription: { fontSize: 14,
    color: '#64748B',
  marginBottom: 24 }
  roommateCard: { flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: '#FFFFFF',
    borderRadius: 12,
  padding: 16,
    borderWidth: 1,
  borderColor: '#E2E8F0',
    marginBottom: 24 },
  roommateAvatar: { width: 48,
    height: 48,
  borderRadius: 24,
    marginRight: 12 },
  roommateName: {,
    fontSize: 16,
  fontWeight: '600',
    color: '#1E293B',
  }
  ratingsContainer: { backgroundColor: '#FFFFFF',
    borderRadius: 12,
  padding: 16,
    borderWidth: 1,
  borderColor: '#E2E8F0',
    marginBottom: 16 },
  ratingItem: { marginBottom: 16 }
  ratingHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 4 },
  ratingLabel: {,
    fontSize: 16,
  fontWeight: '600',
    color: '#1E293B',
  }
  ratingValue: {,
    fontSize: 14,
  fontWeight: '600',
    color: theme.colors.primary[600],
  }
  ratingDescription: { fontSize: 14,
    color: '#64748B',
  marginBottom: 8 }
  // Star rating styles removed as we're now using the StarRating component, ,
  overallRatingContainer: {,
    backgroundColor: '#FFFFFF',
  borderRadius: 12,
    padding: 16,
  borderWidth: 1,
    borderColor: '#E2E8F0',
  marginBottom: 16,
    flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
  }
  overallRatingLabel: {,
    fontSize: 16,
  fontWeight: '600',
    color: '#1E293B',
  }
  overallRatingValue: {,
    flexDirection: 'row',
  alignItems: 'center'
  },
  overallRatingNumber: { fontSize: 18,
    fontWeight: '700',
  color: theme.colors.primary[600],
    marginRight: 8 },
  feedbackContainer: { backgroundColor: '#FFFFFF',
    borderRadius: 12,
  padding: 16,
    borderWidth: 1,
  borderColor: '#E2E8F0',
    marginBottom: 16 },
  inputLabel: { fontSize: 16,
    fontWeight: '600',
  color: '#1E293B',
    marginBottom: 8 },
  feedbackInput: {,
    backgroundColor: '#F8FAFC',
  borderRadius: 8,
    borderWidth: 1,
  borderColor: '#E2E8F0',
    padding: 12,
  fontSize: 14,
    color: '#1E293B',
  minHeight: 120,
    textAlignVertical: 'top',
  }
  togglesContainer: {,
    backgroundColor: '#FFFFFF',
  borderRadius: 12,
    padding: 16,
  borderWidth: 1,
    borderColor: '#E2E8F0',
  }
  toggleItem: {,
    flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
  paddingVertical: 12,
    borderBottomWidth: 1,
  borderBottomColor: '#F1F5F9'
  },
  toggleTextContainer: { flex: 1,
    marginRight: 16 },
  toggleLabel: { fontSize: 16,
    fontWeight: '500',
  color: '#1E293B',
    marginBottom: 4 },
  toggleDescription: {,
    fontSize: 14,
  color: '#64748B'
  },
  footer: {,
    flexDirection: 'row',
  justifyContent: 'space-between',
    padding: 16,
  backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
  borderTopColor: '#E2E8F0'
  },
  backButtonFooter: { flex: 1,
    marginRight: 8 },
  nextButton: { flex: 1,
    marginLeft: 8 },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  loadingText: {,
    marginTop: 12,
  fontSize: 16,
    color: '#64748B',
  }
  emptyContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: 20 },
  emptyTitle: { fontSize: 20,
    fontWeight: '600',
  color: '#1E293B',
    marginTop: 16,
  marginBottom: 8 }
  emptyText: {,
    fontSize: 16,
  color: '#64748B'),
    textAlign: 'center'),
  marginBottom: 24)
  },
  })