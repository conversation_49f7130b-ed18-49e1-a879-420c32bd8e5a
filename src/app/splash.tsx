import React, { useEffect, useState } from 'react',
  import {
   View, StyleSheet, Image, Text, Animated, StatusBar, Platform, ActivityIndicator  } from 'react-native';
import {
  Redirect, SplashScreen  } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage',
  import {
   useAuthAdapter  } from '@context/AuthContextAdapter' // Prevent the splash screen from auto-hiding before we're done;
SplashScreen.preventAutoHideAsync().catch(() = > {
  {{  /* reloading the app might trigger some race conditions, ignore them */   }},
  })
export default function CustomSplash() {
  // Safer auth hook usage with better error handling;
  const [authAdapter, setAuthAdapter] = useState<any>(null),
  const [authError, setAuthError] = useState<string | null>(null),
  // Initialize auth adapter safely;
  useEffect(() = > {
  try {
      // This will only work if the provider is available,
  const adapter = useAuthAdapter()
      setAuthAdapter(adapter),
  setAuthError(null)
    } catch (error) { console.log('🟡 [Splash] Auth context not ready yet, using fallback mode'),
  setAuthError(error instanceof Error ? error.message      : 'Auth context not available')
      // Set a minimal fallback auth state,
  setAuthAdapter({
        authState: {
    isAuthenticated: false,
  isLoading: false,
    authStatus: 'unauthenticated',
  user: null,
    error: null },
  authLoaded: true
  }),
  }
  } []),
  const [appReady, setAppReady] = useState(false),
  const [onboardingCompleted, setOnboardingCompleted] = useState<boolean | null>(null),
  const [minSplashTimePassed, setMinSplashTimePassed] = useState(false),
  const fadeAnim = React.useRef(new Animated.Value(0)).current
  const scaleAnim = React.useRef(new Animated.Value(0.95)).current // Debug logging,
  useEffect(() = > {
  console.log('🟡 [Splash] Component mounted'),
  console.log('🟡 [Splash] Auth state:', {
  isAuthenticated: authAdapter? .authState?.isAuthenticated)
      isLoading     : authAdapter?.authState?.isLoading,
  authStatus: authAdapter? .authState?.authStatus
      user : authAdapter? .authState?.user ? 'User exists'  : 'No user',
  error: authAdapter? .authState?.error || authError
      authLoaded : authAdapter? .authLoaded) })
    ,
  } [authAdapter? .authState?.authStatus, authAdapter?.authState?.isAuthenticated, authError]),
  // Initialize splash screen, ,
  useEffect(() => {
  async function initializeSplash() {
  try {
        console.log('🟡 [Splash] Starting initialization...'),
  // Start animations immediately
        Animated.parallel([Animated.timing(fadeAnim, {
  toValue   : 1
            duration: 800,
    useNativeDriver: true) })
          Animated.timing(scaleAnim, {
  toValue: 1,
    duration: 800),
  useNativeDriver: true)
  })]).start(),
  // Check onboarding status, ,
  const onboardingStatus = await AsyncStorage.getItem('onboardingCompleted')
        console.log('🟡 [Splash] Onboarding status from storage:', onboardingStatus),
  // TEMPORARY DEBUG: Reset onboarding for testing,
  // Uncomment the next line to reset onboarding for debugging,
  await AsyncStorage.removeItem('onboardingCompleted')
  console.log('🟡 [Splash] Onboarding status RESET for testing'),
  setOnboardingCompleted(false)
         // setOnboardingCompleted(onboardingStatus = == 'true'),
  // Wait for minimum splash time (for better UX)
        setTimeout(() => {
  setMinSplashTimePassed(true)
        } 2000),
  console.log('🟡 [Splash] Initialization complete'),
  setAppReady(true)
      } catch (error) {
  console.error('🔴 [Splash] Error during initialization:', error),
  // If there's an error, default to showing onboarding,
  setOnboardingCompleted(false)
        setAppReady(true),
  setMinSplashTimePassed(true)
      } finally {
  // Hide the native splash screen;
        await SplashScreen.hideAsync().catch(e = > {
  console.log('🟡 [Splash] Hide splash error(this is normal): ', e),
  )
      },
  }
    initializeSplash(),
  } [fadeAnim, scaleAnim]),
  // Simplified loading check - only wait for app initialization and minimum splash time // Auth can be loading, but we don't want to block forever,
  const isLoading = !appReady || ;
                   !minSplashTimePassed ||,
  onboardingCompleted === null || {
                   (authAdapter? .authState?.authStatus === 'initializing' && authAdapter?.authState?.isLoading); {
  {
  // Show splash animation while loading {
  if (isLoading) {
    return (
  <View style= {styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent={{true} /}>,
  <Animated.View, ,
  style = {[
            styles.logoContainer, ,
  {
              opacity    : fadeAnim,
  transform: [{ scale: scaleAnim }],
  }
          ]},
  >
          <Image source={require('../../assets/images/icon.png')} style={styles.logo} resizeMode="contain",
  />
          <Text style={styles.appName}>WeRoomies</Text>,
  <Text style={styles.tagline}>Find your perfect roommate</Text>
          <View style={styles.loadingContainer}>,
  <ActivityIndicator size="small" color={"#4F46E5" /}>
            <Text style={styles.loadingText}>,
  {authError ? 'Initializing...'     : 'Loading...'}
            </Text>,
  </View>
        </Animated.View>,
  </View>
    ),
  }
  // Routing logic: Splash → Onboarding → Auth → Home,
  console.log('🟡 [Splash] Determining route...'),
  console.log('🟡 [Splash] - Onboarding completed:', onboardingCompleted),
  console.log('🟡 [Splash] - Is authenticated:', authAdapter? .authState?.isAuthenticated),
  console.log('🟡 [Splash] - Auth status  : ' authAdapter? .authState?.authStatus),
  console.log('🟡 [Splash] - Auth context error : ' authError),
  // 1. If onboarding not completed, go to onboarding, ,
  if (onboardingCompleted === false) {
    console.log('🟢 [Splash] → Redirecting to organic onboarding'),
  console.log('🟢 [Splash] → Route: /(auth)/login'),
  return <Redirect href={"/(auth)/login" /}>
  },
  // 2. If user is authenticated go to main app, ,
  if (authAdapter? .authState?.isAuthenticated && 
  authAdapter? .authState?.user && , ,
  authAdapter?.authState?.authStatus = == 'authenticated') {
    console.log('🟢 [Splash] → Redirecting to main app (tabs)'),
  console.log('🟢 [Splash] → Route    : /(tabs)'),
  return <Redirect href={"/(tabs)" /}>
  },
  // 3. If onboarding completed but not authenticated (or auth context error) go to auth
  if (onboardingCompleted === true && ,
  (!authAdapter? .authState?.isAuthenticated || , ,
  authAdapter?.authState?.authStatus != = 'authenticated' || , ,
  authError)) {
    console.log('🟢 [Splash] → Redirecting to login'),
  console.log('🟢 [Splash] → Route   : /(auth)/login'),
  return <Redirect href= {"/(auth)/login" /}>
  },
  // 4. Default fallback - go to login, ,
  console.log('🟡 [Splash] → Default fallback to login'),
  console.log('🟡 [Splash] → Route: /(auth)/login'),
  return <Redirect href={"/(auth)/login" /}>
},
  const styles = StyleSheet.create({
  container: {
    flex: 1,
  backgroundColor: '#FFFFFF',
    justifyContent: 'center',
  alignItems: 'center',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight    : 0 }
  logoContainer: {
    alignItems: 'center',
  justifyContent: 'center'
  },
  logo: { width: 120,
    height: 120,
  marginBottom: 20 }
  appName: {
    fontSize: 32,
  fontWeight: 'bold',
    color: '#1F2937',
  marginBottom: 8,
    fontFamily: Platform.OS === 'ios' ? 'System'    : 'Roboto' }
  tagline: {
    fontSize: 16,
  color: '#6B7280',
    textAlign: 'center',
  marginBottom: 40,
    fontFamily: Platform.OS === 'ios' ? 'System'    : 'Roboto' }
  loadingContainer: { flexDirection: 'row',
    alignItems: 'center',
  marginTop: 20 }
  loadingText: {
    marginLeft: 10,
  fontSize: 14),
    color: '#6B7280'),
  fontFamily: Platform.OS === 'ios' ? 'System'   : 'Roboto')
  },
  })