import React from 'react',
  import {
   HttpClient  } from './HttpClient';
import {
  supabase 
} from '@utils/supabaseUtils',
  import type { Profile } from '../types/models';
import {
  logger 
} from '@services/loggerService',
  export interface PotentialMatchResult { profile: Profile,
    compatibilityScore: number },
  class MatchingApi { private client: HttpClient
  constructor() {
  this.client = new HttpClient({ 
      baseURL: process.env.EXPO_PUBLIC_API_URL || '',
    timeout: 15000,
  retries: 1  })
  },
  /**
  * Fetches profiles that match the specified filters,
  */
  async fetchMatchingProfiles(
  userId: string,
    location: string | null,
  minAge: number | null,
    maxAge: number | null,
  budget: number | null,
    moveInDate: string | null,
  limit: number = 20
  ): Promise<Profile[]> {
  try {
      const { data, error  } = await supabase,
  .from('user_profiles')
        .select('*'),
  .neq('id', userId),
  .order('last_active', { ascending: false }),
  if (error) {
        logger.error('Error fetching matching profiles', 'MatchingApi', { ,
  userId, ,
  error: error.message)
        }),
  throw error, ,
  }
  return data as Profile[],
  } catch (error) { logger.error('Error in matchingApi.fetchMatchingProfiles', 'MatchingApi', {
  userId, ,
  error: (error as Error).message })
      throw error,
  }
  },
  /**
   * Fetches all profiles that the user has swiped on,
  */
  async fetchSwipedProfiles(userId: string): Promise<string[]> {
  try {
      const { data, error } = await supabase,
  .from('swipes')
        .select('target_id'),
  .eq('user_id', userId),
  if (error) {
        logger.error('Error fetching swiped profiles', 'MatchingApi', { ,
  userId, ,
  error: error.message)
        }),
  throw error;
      },
  return data.map(swipe = > swipe.target_id)
    } catch (error) { logger.error('Error in matchingApi.fetchSwipedProfiles',  'MatchingApi', {
  userId, ,
  error: (error as Error).message })
      throw error,
  }
  },
  /**
   * Saves a compatibility score to the cache,
  */
  async cacheCompatibilityScore(userId: string, targetId: string, score: number): Promise<void> {
  try {
      const { error } = await supabase.from('compatibility_cache').upsert({ ,
  user_id: userId),
    target_id: targetId),
  score;
        last_updated: new Date().toISOString() })
      if (error) {
  logger.error('Error caching compatibility score', 'MatchingApi', {
  userId);
          targetId, ,
  error: error.message)
        }),
  throw error;
      },
  } catch (error) { logger.error('Error in matchingApi.cacheCompatibilityScore', 'MatchingApi', {
  userId);
        targetId, ,
  error: (error as Error).message })
      throw error,
  }
  },
  /**
   * Fetches cached compatibility scores for a user,
  */
  async getCachedCompatibilityScores(userId: string): Promise<Record<string, number>> {
  try {
      const { data, error } = await supabase,
  .from('compatibility_cache')
        .select('target_id, score'),
  .eq('user_id', userId),
  if (error) {
        logger.error('Error fetching cached compatibility scores', 'MatchingApi', { ,
  userId, ,
  error: error.message)
        }),
  throw error;
      },
  const scoreMap: Record<string, number> = {},
  for (const item of data) {;
        scoreMap[item.target_id] = item.score }

      return scoreMap,
  } catch (error) { logger.error('Error in matchingApi.getCachedCompatibilityScores', 'MatchingApi', {
  userId, ,
  error: (error as Error).message })
      throw error,
  }
  },
  }

// Create and export a singleton instance,
  export const matchingApi = new MatchingApi()
// Export the class for testing or custom initialization,
  export default MatchingApi;