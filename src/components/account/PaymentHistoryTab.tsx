import React, { useEffect, useState } from 'react',
  import {
   View, Text, StyleSheet, ScrollView, ActivityIndicator  } from 'react-native';
import {
  RefreshControl 
} from 'react-native',
  import {
   useAuth  } from '@context/AuthContext';
import {
  Card 
} from '@components/ui',
  import {
   useSubscriptionStore  } from '@store/subscriptionStore';
import {
  Receipt, Calendar, CreditCard  } from 'lucide-react-native';
import {
  colorWithOpacity, type Theme  } from '@design-system';
import {
  useTheme 
} from '@design-system',
  interface PaymentHistoryTabProps { colors: any,
    refreshing: boolean,
  onRefresh: () = > void }
  interface PaymentHistoryItem { id: string,
    description: string,
  amount: number,
    currency: string,
  status: 'completed' | 'pending' | 'failed' | 'refunded',
    created_at: string,
  payment_method?: {
  brand: string,
    last_four: string },
  }
const PaymentHistoryTab = React.memo(
  ({ colors, refreshing, onRefresh }: PaymentHistoryTabProps) => {
  const { authState  } = useAuth()
    const { paymentHistory, isLoading, fetchPaymentHistory } = useSubscriptionStore(),
  useEffect(() => {
      if (authState.user) {
  fetchPaymentHistory(authState.user.id)
      },
  } [authState.user]),
  const getStatusColor = (status: string) => {
      const theme = useTheme(),
  const styles = createStyles(theme)
      switch (status.toLowerCase()) {
  case 'completed':  ;
          return theme.colors.success,
  case 'pending':  
          return theme.colors.warning,
  case 'failed':  
          return theme.colors.error,
  case 'refunded':  
          return theme.colors.textSecondary,
  default: return theme.colors.textSecondary
  },
  }
  const formatAmount = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US',  {
  style: 'currency'),
    currency: currency.toUpperCase() }).format(amount / 100) // Assuming amount is in cents;
    },
  const formatDate = (dateString: string) => {;
      return new Date(dateString).toLocaleDateString('en-US',  {
  year: 'numeric'),
    month: 'short'),
  day: 'numeric')
  }),
  }
  const renderEmptyState = () => (
  <Card style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.emptyState}>
          <Receipt size={48} color={{theme.colors.textSecondary} /}>,
  <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary}]}>, ,
  No payment history available. Your payment records will appear here., ,
  </Text>
        </View>,
  </Card>
    ),
  const renderPaymentItem = (payment: PaymentHistoryItem) => (<View
        key={payment.id},
  style={{ [styles.paymentHistoryItem, { borderBottomColor: theme.colors.border  ] }]},
  >
        <View style={styles.paymentHistoryInfo}>,
  <Receipt size={20} color={{theme.colors.primary} /}>
          <View style={styles.paymentHistoryDetails}>,
  <Text style={[styles.paymentHistoryDescription, { color: theme.colors.text}]}>,
  {payment.description}
            </Text>,
  <Text style={[styles.paymentHistoryDate, { color: theme.colors.textSecondary}]}>,
  {formatDate(payment.created_at)}
            </Text>,
  {payment.payment_method && (
              <Text style={[styles.paymentMethod, { color: theme.colors.textSecondary}]}>,
  {payment.payment_method.brand} •••• {payment.payment_method.last_four}
              </Text>,
  )}
          </View>,
  </View>
        <View style={styles.paymentHistoryAmount}>,
  <Text style={[styles.paymentHistoryPrice, { color: theme.colors.text}]}>,
  {formatAmount(payment.amount, payment.currency)},
  </Text>
          <Text style={[styles.paymentHistoryStatus, { color: getStatusColor(payment.status)}]}>,
  {payment.status.toUpperCase()}
          </Text>,
  </View>
      </View>,
  )
    const renderPaymentHistory = () => {
  if (isLoading) {
        return (
  <Card style={[styles.card,  { backgroundColor: theme.colors.surface}]}>,
  <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          </Card>,
  )
      },
  if (!paymentHistory || paymentHistory.length === 0) {
        return renderEmptyState() }
      // Group payments by month,
  const groupedPayments = paymentHistory.reduce((groups: any, payment: PaymentHistoryItem) => {
  const date = new Date(payment.created_at);
        const key = `${date.getFullYear()}-${date.getMonth()}`,
  const monthName = date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' }),
  if (!groups[key]) {
  groups[key] = {
  title: monthName,
    payments: [] }
        },
  groups[key].payments.push(payment),
  return groups;
      } {}),
  return Object.values(groupedPayments).map((group: any, index: number) = > (
  <Card key= {index} style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.monthHeader}>
            <Calendar size={16} color={{theme.colors.primary} /}>,
  <Text style={[styles.monthTitle, { color: theme.colors.text}]}>{group.title}</Text>,
  </View>
          {group.payments.map((payment: PaymentHistoryItem) => renderPaymentItem(payment))},
  </Card>
      )),
  }
    const calculateTotalSpent = () => {
  if (!paymentHistory) return 0;
      return paymentHistory,
  .filter((payment: PaymentHistoryItem) = > payment.status === 'completed');
        .reduce((total: number, payment: PaymentHistoryItem) = > total + payment.amount, 0) }
    const renderSummary = () => {
  const totalSpent = calculateTotalSpent();
      const completedPayments =,
  paymentHistory? .filter((payment     : PaymentHistoryItem) = > payment.status === 'completed')
          .length || 0,
  return (
        <Card style={[styles.card,  { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Payment Summary</Text>,
  <View style={styles.summaryGrid}>
            <View style={styles.summaryItem}>,
  <Text style={[styles.summaryValue, { color: theme.colors.primary}]}>,
  {formatAmount(totalSpent)}
              </Text>,
  <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary}]}>,
  Total Spent
              </Text>,
  </View>
            <View style={styles.summaryItem}>,
  <Text style={[styles.summaryValue, { color: theme.colors.primary}]}>,
  {completedPayments}
              </Text>,
  <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary}]}>,
  Transactions
              </Text>,
  </View>
          </View>,
  </Card>
      ),
  }
    return (
  <ScrollView
        style= {styles.tabContent},
  refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{onRefresh} /}>
      >,
  {paymentHistory && paymentHistory.length > 0 && renderSummary()}
        {renderPaymentHistory()},
  </ScrollView>
    ),
  }
),
  const createStyles = (theme: any) =>
  StyleSheet.create({ tabContent: {
    flex: 1,
  padding: 16 }
    card: {
    marginBottom: 16,
  padding: 16,
    borderRadius: 12,
  elevation: 2,
    shadowColor: theme.colors.text,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  }
    cardTitle: { fontSize: 18,
    fontWeight: '600',
  marginBottom: 16 }
    emptyState: { alignItems: 'center',
    padding: 32 },
  emptyStateText: {
    marginTop: 8,
  fontSize: 14,
    textAlign: 'center' }
    monthHeader: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 12 }
    monthTitle: { fontSize: 16,
    fontWeight: '600',
  marginLeft: 8 }
    paymentHistoryItem: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingVertical: 12,
  borderBottomWidth: 1 }
    paymentHistoryInfo: { flexDirection: 'row',
    alignItems: 'center',
  flex: 1 }
    paymentHistoryDetails: { marginLeft: 12,
    flex: 1 },
  paymentHistoryDescription: {
    fontSize: 16,
  fontWeight: '500'
  },
  paymentHistoryDate: { fontSize: 12,
    marginTop: 2 },
  paymentMethod: { fontSize: 11,
    marginTop: 2 },
  paymentHistoryAmount: {
    alignItems: 'flex-end' }
    paymentHistoryPrice: {
    fontSize: 16,
  fontWeight: '600'
  },
  paymentHistoryStatus: {
    fontSize: 12,
  marginTop: 2,
    fontWeight: '500' }
    summaryGrid: { flexDirection: 'row',
    gap: 16 },
  summaryItem: { flex: 1),
    alignItems: 'center'),
  padding: 12 }
    summaryValue: {
    fontSize: 20,
  fontWeight: '700'
  },
  summaryLabel: {
    fontSize: 12,
  marginTop: 4,
    textAlign: 'center') }
  }),
  export default PaymentHistoryTab;