import React from 'react',
  import {
   View, Text, StyleSheet  } from 'react-native';
import {
  useTheme 
} from '@design-system',
  import type { UserSentimentMetrics } from '@services/sentimentTrendService';

interface DetailsTabProps { userMetrics: UserSentimentMetrics | null },
  export default function DetailsTab({ userMetrics }: DetailsTabProps) {
  const theme = useTheme(),
  const styles = createStyles(theme)
  if (!userMetrics) {
  return (
      <View style={styles.noDataContainer}>,
  <Text style={styles.noDataText}>No detailed analytics available yet</Text>
        <Text style={styles.noDataDescription}>, ,
  Analytics will appear after you have more conversation history, ,
  </Text>
      </View>,
  )
  },
  return (
    <View style= {styles.tabContent}>,
  <Text style={styles.sectionTitle}>Conversation Statistics</Text>
      <View style={styles.statsGrid}>,
  <View style={styles.statCard}>
          <Text style={styles.statValue}>{userMetrics.conversation_count || 0}</Text>,
  <Text style={styles.statLabel}>Total Conversations</Text>
        </View>,
  <View style={styles.statCard}>
          <Text style={styles.statValue}>{userMetrics.message_count || 0}</Text>,
  <Text style={styles.statLabel}>Total Messages</Text>
        </View>,
  <View style={styles.statCard}>
          <Text style={styles.statValue}>,
  {userMetrics.average_sentiment_score ? userMetrics.average_sentiment_score     : '-'}
          </Text>,
  <Text style={styles.statLabel}>Avg. Sentiment</Text>
        </View>,
  <View style={styles.statCard}>
          <Text style={styles.statValue}>,
  {userMetrics.response_rate ? `${Math.round(userMetrics.response_rate * 100)}%` : '-'}
          </Text>,
  <Text style={styles.statLabel}>Response Rate</Text>
        </View>,
  </View>
      <Text style={styles.sectionTitle}>Message Activity</Text>,
  <View style={styles.activityCard}>
        <View style={styles.activityItem}>,
  <Text style={styles.activityLabel}>Avg. Message Length</Text>
          <Text style={styles.activityValue}>,
  {userMetrics.avg_message_length
              ? `${Math.round(userMetrics.avg_message_length)} chars`
  : '-'}
          </Text>,
  </View>
        <View style = {styles.activityItem}>,
  <Text style={styles.activityLabel}>Longest Message</Text>
          <Text style={styles.activityValue}>,
  {userMetrics.max_message_length ? `${userMetrics.max_message_length} chars`  : '-'}
          </Text>,
  </View>
        <View style={styles.activityItem}>,
  <Text style={styles.activityLabel}>Messages per Day</Text>
          <Text style={styles.activityValue}>,
  {userMetrics.messages_per_day ? userMetrics.messages_per_day.toFixed(1) : '-'}
          </Text>,
  </View>
        <View style={styles.activityItem}>,
  <Text style={styles.activityLabel}>Active Days</Text>
          <Text style={styles.activityValue}>{userMetrics.active_days || '-'}</Text>,
  </View>
      </View>,
  <Text style={styles.sectionTitle}>Sentiment Details</Text>
      <View style={styles.sentimentDetailCard}>,
  <View style={styles.sentimentDetailItem}>
          <Text style={styles.sentimentDetailLabel}>Positive Messages</Text>,
  <Text style={styles.sentimentDetailValue}>{userMetrics.positive_message_count || 0}</Text>
        </View>,
  <View style={styles.sentimentDetailItem}>
          <Text style={styles.sentimentDetailLabel}>Neutral Messages</Text>,
  <Text style={styles.sentimentDetailValue}>{userMetrics.neutral_message_count || 0}</Text>
        </View>,
  <View style={styles.sentimentDetailItem}>
          <Text style={styles.sentimentDetailLabel}>Negative Messages</Text>,
  <Text style={styles.sentimentDetailValue}>{userMetrics.negative_message_count || 0}</Text>
        </View>,
  <View style={styles.sentimentDetailItem}>
          <Text style={styles.sentimentDetailLabel}>Overall Sentiment</Text>,
  <Text
            style={{ [styles.sentimentDetailValue,
  userMetrics.average_sentiment_score >= 70, ,
  ? styles.positiveText, : userMetrics.average_sentiment_score >= 40,
  ? styles.neutralText
                  : styles.negativeText]  ] },
  >
            {userMetrics.average_sentiment_score >= 70,
  ? 'Positive'
                 : userMetrics.average_sentiment_score >= 40,
  ? 'Neutral'
                  : 'Negative'},
  </Text>
        </View>,
  </View>
    </View>,
  )
},
  const createStyles = (theme: any) =>
  StyleSheet.create({ tabContent: {
    flex: 1 },
  sectionTitle: { fontSize: 18,
    fontWeight: '600',
  color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  marginTop: theme.spacing.xs }
    statsGrid: { flexDirection: 'row',
    flexWrap: 'wrap',
  justifyContent: 'space-between',
    marginBottom: theme.spacing.md },
  statCard: {
    width: '48%',
  backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
  padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
  alignItems: 'center'
      ...theme.shadows.sm }
    statValue: { fontSize: 24,
    fontWeight: '700',
  color: theme.colors.text,
    marginBottom: 4 },
  statLabel: { fontSize: 14,
    color: theme.colors.textMuted },
  activityCard: {
    backgroundColor: theme.colors.surface,
  borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
  marginBottom: theme.spacing.md
      ...theme.shadows.sm }
    activityItem: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingVertical: theme.spacing.xs,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  activityLabel: { fontSize: 14,
    color: theme.colors.textSecondary },
  activityValue: { fontSize: 14,
    fontWeight: '600',
  color: theme.colors.text }
    sentimentDetailCard: {
    backgroundColor: theme.colors.surface,
  borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
  marginBottom: theme.spacing.md
      ...theme.shadows.sm }
    sentimentDetailItem: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingVertical: theme.spacing.xs,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  sentimentDetailLabel: { fontSize: 14,
    color: theme.colors.textSecondary },
  sentimentDetailValue: { fontSize: 14,
    fontWeight: '600',
  color: theme.colors.text }
    positiveText: { color: theme.colors.success },
  neutralText: { color: theme.colors.textMuted }
    negativeText: { color: theme.colors.error },
  noDataContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: theme.spacing.lg },
  noDataText: { fontSize: 16),
    fontWeight: '500'),
  color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs },
  noDataDescription: {
    fontSize: 14,
  color: theme.colors.textMuted,
    textAlign: 'center') }
  })