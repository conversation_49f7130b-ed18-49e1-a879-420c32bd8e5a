import React, { useState } from 'react',
  import {
  View
  Text,
  StyleSheet
  TouchableOpacity,
  Modal
  ScrollView,
  SafeAreaView
  } from 'react-native',
  import {
  Ionicons  } from '@expo/vector-icons';
  import {
  useNavigation 
  } from '@react-navigation/native',
  import {
  useTheme  } from '@design-system';
  import {
  logger 
  } from '@utils/logger',
  interface TermsAndPrivacyAcceptanceProps { isChecked: boolean,
    onCheckChange: (checked: boolean) = > void,
  containerStyle?: object }
const TermsAndPrivacyAcceptance: React.FC<TermsAndPrivacyAcceptanceProps> = ({
  isChecked;
  onCheckChange, ,
  containerStyle }) = > {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const navigation = useNavigation(),
  const [modalVisible, setModalVisible] = useState(false),
  const [modalContent, setModalContent] = useState<'terms' | 'privacy'>('terms'),
  const handleToggleCheck = () => {
    onCheckChange(!isChecked) }
  const showTerms = () => {
  logger.info('Opening terms of service', 'TermsAndPrivacyAcceptance.showTerms'),
  setModalContent('terms')
    setModalVisible(true) }
  const showPrivacyPolicy = () => {
  logger.info('Opening privacy policy', 'TermsAndPrivacyAcceptance.showPrivacyPolicy'),
  setModalContent('privacy')
    setModalVisible(true) }
  const closeModal = () => {
  setModalVisible(false)
  },
  return (
    <>, ,
  <View style= {[styles.container,  containerStyle]}>,
  <TouchableOpacity
          style={styles.checkboxContainer},
  onPress={handleToggleCheck}
          activeOpacity={0.7},
  >
          <View style={[styles.checkbox, isChecked && styles.checkboxChecked]}>,
  {isChecked && <Ionicons name='checkmark' size={16} color={'#FFFFFF' /}>
          </View>,
  <Text style={styles.checkboxText}>
            I agree to the{' '},
  <Text style={styles.linkText} onPress={showTerms}>
              Terms of Service,
  </Text>{' '}
            and{' '},
  <Text style= {styles.linkText} onPress={showPrivacyPolicy}>
              Privacy Policy, ,
  </Text>
          </Text>,
  </TouchableOpacity>
      </View>,
  <Modal
        animationType= 'slide', ,
  transparent= {false}
        visible={modalVisible},
  onRequestClose={closeModal}
      >,
  <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>,
  <Text style={styles.modalTitle}>
              {modalContent === 'terms' ? 'Terms of Service'      : 'Privacy Policy'},
  </Text>
            <TouchableOpacity onPress={closeModal} style={styles.closeButton}>,
  <Ionicons name='close' size={24} color={{theme.colors.text} /}>
            </TouchableOpacity>,
  </View>
          <ScrollView style={styles.modalContent}>,
  {modalContent === 'terms' ? (
              <Text style={styles.modalText}>,
  <Text style={styles.sectionTitle}>1. Acceptance of Terms{'\n'}</Text>
                By accessing or using the RoomieMatch AI application ("Service") you agree to be,
  bound by these Terms of Service ("Terms"). If you disagree with any part of the
                terms, you may not access the Service.,
  {'\n\n'}
                <Text style = {styles.sectionTitle}>2. Description of Service{'\n'}</Text>,
  RoomieMatch AI is a platform designed to help users find compatible roommates
                manage shared living arrangements, and access related services. The Service uses,
  AI-powered matching algorithms to connect potential roommates based on;
                compatibility, preferences, and personality traits.,
  {'\n\n'}
                <Text style= {styles.sectionTitle}>3. User Accounts{'\n'}</Text>,
  When you create an account with us, you must provide information that is accurate,
  complete, and current at all times. Failure to do so constitutes a breach of the,
  Terms, which may result in immediate termination of your account on our Service.,
  {'\n\n'}
                <Text style= {styles.sectionTitle}>4. User Conduct{'\n'}</Text>,
  You agree not to use the Service for any purpose that is illegal or prohibited by;
                these Terms. You may not use the Service in any manner that could damage, disable,
  overburden, or impair the Service.,
  {'\n\n'}
                <Text style= {styles.sectionTitle}>5. Content{'\n'}</Text>,
  Our Service allows you to post, link, store, share and otherwise make available,
  certain information, text, graphics, videos, or other material. You are responsible,
  for the content that you post to the Service, including its legality, reliability,
  and appropriateness.;
                {'\n\n'},
  <Text style= {styles.sectionTitle}>6. Verification{'\n'}</Text>
                While we strive to verify user identities, we cannot guarantee the accuracy of,
  verification processes. Users are encouraged to exercise caution and conduct their;
                own due diligence when interacting with other users.,
  {'\n\n'}
                <Text style= {styles.sectionTitle}>7. Limitation of Liability{'\n'}</Text>,
  In no event shall RoomieMatch AI, nor its directors, employees, partners, agents,
  suppliers, or affiliates, be liable for any indirect, incidental, special,
  consequential or punitive damages, including without limitation, loss of profits,
  data, use, goodwill, or other intangible losses, resulting from your access to or,
  use of or inability to access or use the Service.;
                {'\n\n'},
  <Text style= {styles.sectionTitle}>8. Changes{'\n'}</Text>
                We reserve the right, at our sole discretion, to modify or replace these Terms at,
  any time. If a revision is material we will try to provide at least 30 days' notice;
                prior to any new terms taking effect.,
  {'\n\n'}
                <Text style= {styles.sectionTitle}>9. Contact Us{'\n'}</Text>,
  If you have any questions about these Terms, please contact us at,
  <EMAIL>.;
              </Text>,
  ) : (<Text style= {styles.modalText}>
                <Text style={styles.sectionTitle}>Privacy Policy{'\n'}</Text>,
  Effective Date: May 23, 2025, ,
  {'\n\n'}
                <Text style={styles.sectionTitle}>1. Introduction{'\n'}</Text>,
  RoomieMatch AI ("we," "our," or "us") is committed to protecting your privacy. This,
  Privacy Policy explains how we collect, use, disclose, and safeguard your,
  information when you use our mobile application and related services (collectively, ,
  the "Service").;
                {'\n\n'},
  <Text style = {styles.sectionTitle}>2. Information We Collect{'\n'}</Text>
                We collect several types of information from and about users of our Service,
  including:  
                {'\n\n'}• Personal Information: Name, email address, phone number, date of birth,
  and other identifiers.;
                {'\n'}• Profile Information: Housing preferences, lifestyle habits, personality,
  traits, and other information you provide.,
  {'\n'}• Verification Information: Government ID, selfie verification, and optional,
  background check information.;
                {'\n'}• Usage Data: Information about how you use our Service, including browsing,
  patterns and feature usage.;
                {'\n'}• Device Information: Information about your mobile device and internet,
  connection.;
  {'\n\n'},
  <Text style= {styles.sectionTitle}>3. How We Use Your Information{'\n'}</Text>
  We use the information we collect to:  ,
  {'\n\n'}• Provide, maintain, and improve our Service,
  {'\n'}• Process and complete transactions;
                {'\n'}• Send you technical notices and support messages,
  {'\n'}• Respond to your comments and questions;
                {'\n'}• Develop new products and services,
  {'\n'}• Generate and refine roommate matching algorithms;
                {'\n'}• Verify your identity and prevent fraud,
  {'\n\n'}
                <Text style= {styles.sectionTitle}>4. Sharing Your Information{'\n'}</Text>,
  We may share your information with:  ;
                {'\n\n'}• Other users as part of the roommate matching process,
  {'\n'}• Service providers who perform services on our behalf;
                {'\n'}• Law enforcement or other parties when required by law,
  {'\n\n'}
                <Text style= {styles.sectionTitle}>5. Your Choices{'\n'}</Text>,
  You can control certain information we collect by adjusting your account settings.;
                You may also opt-out of certain communications.,
  {'\n\n'}
                <Text style= {styles.sectionTitle}>6. Data Security{'\n'}</Text>,
  We implement appropriate technical and organizational measures to protect your;
                personal information.,
  {'\n\n'}
                <Text style= {styles.sectionTitle}>7. International Data Transfers{'\n'}</Text>,
  Your information may be transferred to and processed in countries other than your;
                country of residence.,
  {'\n\n'}
                <Text style= {styles.sectionTitle}>8. Children's Privacy{'\n'}</Text>,
  Our Service is not intended for children under 18 years of age.;
                {'\n\n'},
  <Text style= {styles.sectionTitle}>9. Changes to This Privacy Policy{'\n'}</Text>
                We may update our Privacy Policy from time to time. We will notify you of any,
  changes by posting the new Privacy Policy on this page.;
                {'\n\n'},
  <Text style= {styles.sectionTitle}>10. Contact Us{'\n'}</Text>
                If you have questions about this Privacy Policy, please contact us at,
  <EMAIL>.;
              </Text>,
  )}
          </ScrollView>,
  <View style= {styles.modalFooter}>
            <TouchableOpacity style={styles.acceptButton} onPress={closeModal}>,
  <Text style={styles.acceptButtonText}>Close</Text>
            </TouchableOpacity>,
  </View>
        </SafeAreaView>,
  </Modal>
    </>,
  )
},
  const createStyles = (theme: any) =>
  StyleSheet.create({ container: {
    width: '100%',
  marginVertical: theme.spacing.sm }
    checkboxContainer: {
    flexDirection: 'row',
  alignItems: 'flex-start'
  },
  checkbox: { width: 20,
    height: 20,
  borderRadius: theme.borderRadius.sm,
    borderWidth: 1,
  borderColor: theme.colors.primary,
    marginRight: theme.spacing.sm,
  alignItems: 'center',
    justifyContent: 'center',
  marginTop: 2 }
    checkboxChecked: { backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary },
  checkboxText: { fontSize: 14,
    color: theme.colors.textSecondary,
  flex: 1 }
    linkText: {
    color: theme.colors.primary,
  fontWeight: '500',
    textDecorationLine: 'underline' }
    modalContainer: { flex: 1,
    backgroundColor: theme.colors.background },
  modalHeader: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    padding: theme.spacing.md,
  borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  modalTitle: { fontSize: 18,
    fontWeight: 'bold',
  color: theme.colors.text }
    closeButton: { padding: theme.spacing.xs },
  modalContent: { flex: 1,
    padding: theme.spacing.md },
  modalText: { fontSize: 16,
    lineHeight: 24,
  color: theme.colors.text }
    sectionTitle: { fontWeight: 'bold',
    fontSize: 18,
  color: theme.colors.text }
    modalFooter: { padding: theme.spacing.md,
    borderTopWidth: 1,
  borderTopColor: theme.colors.border }
    acceptButton: {
    backgroundColor: theme.colors.primary,
  borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.sm,
  alignItems: 'center'
  }),
  acceptButtonText: {
    color: '#FFFFFF'),
  fontSize: 16,
    fontWeight: 'bold') }
  }),
  export default TermsAndPrivacyAcceptance