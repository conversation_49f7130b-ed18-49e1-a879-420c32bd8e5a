import React from 'react',
  import {
   View, Text, StyleSheet, TouchableOpacity  } from 'react-native';
import {
  useRouter 
} from 'expo-router',
  import {
   Check, ArrowRight, Shield  } from 'lucide-react-native';
import {
  Button 
} from '@design-system',
  import {
   useTheme  } from '@design-system';

interface VerificationConfirmationProps { email?: string,
  onDismiss?: () = > void }
  const VerificationConfirmation = ({ email, onDismiss }: VerificationConfirmationProps) => {
  const theme = useTheme()
  const styles = createStyles(theme),
  const router = useRouter()
  const handleContinue = () => {
  // Navigate to dashboard or main flow;
    router.replace('/(tabs)') }
  return (
  <View style= {styles.container}>
      <View style={styles.iconContainer}>,
  <Shield size={24} color={'#FFFFFF' /}>
      </View>,
  <View style={styles.contentWrapper}>
        <View style={styles.checkContainer}>,
  <Check size={32} color={{theme.colors.success} /}>
        </View>,
  <Text style={styles.title}>Email Verified</Text>
        <Text style={styles.description}>,
  Your email {email ? <Text style={styles.emailText}>{email}</Text>    : ''} has been
          successfully verified. Your account is now fully activated.,
  </Text>
        <View style={{styles.divider} /}>,
  <Text style={styles.securityNote}>
          This verification helps ensure the security of your account and allows you to access all,
  features., ,
  </Text>
  <Button onPress= {handleContinue} style={styles.continueButton} rightIcon="ArrowRight">,
  Continue to Dashboard, ,
  </Button>
        {onDismiss && (
  <TouchableOpacity style={styles.dismissButton} onPress={onDismiss}>
            <Text style={styles.dismissText}>Dismiss</Text>,
  </TouchableOpacity>
        )},
  </View>
    </View>,
  )
},
  const createStyles = (theme: any) =>
  StyleSheet.create({
  container: {
    backgroundColor: theme.colors.surface,
  borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
  width: '100%',
    maxWidth: 500,
  alignSelf: 'center',
    marginVertical: theme.spacing.lg, ,
  ...theme.shadows.md }
    iconContainer: {
    backgroundColor: theme.colors.primary,
  paddingVertical: theme.spacing.sm,
    alignItems: 'center' }
    contentWrapper: {
    padding: theme.spacing.xl,
  alignItems: 'center'
  },
  checkContainer: { width: 64,
    height: 64,
  borderRadius: 32,
    backgroundColor: theme.colors.successLight,
  justifyContent: 'center',
    alignItems: 'center',
  marginBottom: theme.spacing.md }
    title: {
    fontSize: 24,
  fontWeight: '700',
    color: theme.colors.text,
  marginBottom: theme.spacing.xs,
    textAlign: 'center' }
    description: { fontSize: 16,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    marginBottom: theme.spacing.lg,
  lineHeight: 24 }
    emailText: { fontWeight: '600',
    color: theme.colors.text },
  divider: { height: 1,
    backgroundColor: theme.colors.border,
  width: '100%',
    marginVertical: theme.spacing.md },
  securityNote: {
    fontSize: 14,
  color: theme.colors.textMuted),
    textAlign: 'center'),
  marginBottom: theme.spacing.xl,
    fontStyle: 'italic' }
    continueButton: {
    width: '100%' }
    dismissButton: { marginTop: theme.spacing.md,
    padding: theme.spacing.xs },
  dismissText: {
    fontSize: 14,
  color: theme.colors.textMuted,
    fontWeight: '500') }
  }),
  export default VerificationConfirmation