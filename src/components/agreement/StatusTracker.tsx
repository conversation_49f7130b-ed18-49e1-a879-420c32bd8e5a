import React, { useEffect, useState } from 'react';,
  import {
   View, Text, StyleSheet, TouchableOpacity, Alert ,
  } from 'react-native';
import {,
  unifiedAgreementService, type AgreementStatus, type StatusTransition ,
  } from '@services';
import {,
  useAuth 
} from '@hooks/useAuth';,
  import {
   MaterialIcons ,
  } from '@expo/vector-icons';
import {,
  colorWithOpacity, type Theme ,
  } from '@design-system';
import {,
  useTheme 
} from '@design-system';,
  interface StatusTrackerProps { agreementId: string
  onStatusChange?: (newStatus: AgreementStatus) = > void;,
  showHistory?: boolean }
export const StatusTracker: React.FC<StatusTrackerProps> = ({ ,
  agreementId;
  onStatusChange, ,
  showHistory = false, ,
   }) => {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const [currentStatus, setCurrentStatus] = useState<AgreementStatus>('draft'),
  const [statusHistory, setStatusHistory] = useState<StatusTransition[]>([]),
  const [loading, setLoading] = useState(true),
  const [error, setError] = useState<string | null>(null),
  const { authState  } = useAuth();
  const user = authState? .user;,
  useEffect(() = > {
  loadStatus(),
  // Real-time status subscriptions can be implemented later;
  } [agreementId]),
  const loadStatus = async () => {
  try {,
  setLoading(true)
      const status = await unifiedAgreementService.getCurrentStatus(agreementId),
  setCurrentStatus(status);
      ;,
  if (showHistory) {
        // Status history will be available once the migration is run // For now, we'll show empty history;,
  setStatusHistory([]),
  }
    } catch (err) { setError(err instanceof Error ? err.message      : 'Failed to load status') } finally {,
  setLoading(false)
    },
  }
  const handleStatusChange = async (newStatus: AgreementStatus) => {,
  try {
      Alert.prompt(,
  'Status Change'
        'Please provide a reason for this status change(optional): ' {,
  async (reason) => {
  try {,
  await unifiedAgreementService.updateStatus(agreementId, newStatus, reason),
  setCurrentStatus(newStatus)
            if (onStatusChange) onStatusChange(newStatus),
  Alert.alert('Success', 'Status updated successfully'),
  } catch (err) {
            Alert.alert('Error'),
  'Failed to update status. Please try again later.')
            ),
  }
        },
  )
    } catch (err) {,
  Alert.alert('Error', 'Failed to update status'),
  }
  },
  const getStatusColor = ($2) => {
  const colors: Record<AgreementStatus, string> = { ,
  draft: '#6B7280',
    pending_review: theme.colors.warning,
  in_review: theme.colors.primary,
    pending_signatures: '#8B5CF6',
  signed: theme.colors.success,
    active: '#059669',
  expired: theme.colors.error,
    terminated: '#991B1B',
  archived: '#1F2937'
   };,
  return colors[status] || '#6B7280', ,
  }
  if (loading) {,
  return (
    <View style= {styles.container}>,
  <Text>Loading status...</Text>
      </View>,
  )
  },
  if (error) {
    return (,
  <View style={styles.container}>
        <Text style={styles.errorText}>{error}</Text>,
  </View>
    ),
  }
  return (,
  <View style={styles.container}>
      <View style={styles.currentStatus}>,
  <Text style={styles.label}>Current Status:</Text>
        <View,
  style={{ [styles.statusBadge, { backgroundColor: getStatusColor(currentStatus)  ] }]},
  >
          <Text style={styles.statusText}>,
  {currentStatus.replace('_', ' ').toUpperCase()},
  </Text>
        </View>,
  </View>
      {showHistory && statusHistory.length > 0 && (,
  <View style={styles.history}>
          <Text style={styles.historyTitle}>Status History</Text>,
  {statusHistory.map((transition, index) => (,
  <View key={index} style={styles.historyItem}>
              <MaterialIcons,
  name="history", ,
  size= {16} color={getStatusColor(transition.to)}
              />,
  <View style={styles.historyContent}>
                <Text style={styles.historyText}>,
  {transition.from} → {transition.to}
                </Text>,
  <Text style={styles.historyDate}>
                  {new Date(transition.timestamp).toLocaleDateString()},
  </Text>
                {transition.reason && (,
  <Text style={styles.historyReason}>{transition.reason}</Text>
                )},
  </View>
            </View>,
  ))}
        </View>,
  )}
    </View>,
  )
},
  const createStyles = (theme: any) => StyleSheet.create({,
    container: {,
  padding: 16,
    backgroundColor: theme.colors.background,
  borderRadius: 8,
    shadowColor: theme.colors.text,, ,
  shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
  },
  currentStatus: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 16 }
  label: { fontSize: 16,
    fontWeight: '600',
  marginRight: 8 }
  statusBadge: { paddingHorizontal: 12,
    paddingVertical: 6,
  borderRadius: 16 }
  statusText: {,
    color: theme.colors.background,
  fontSize: 14,
    fontWeight: '600',
  }
  history: { marginTop: 16 },
  historyTitle: { fontSize: 16,
    fontWeight: '600',
  marginBottom: 8 }
  historyItem: { flexDirection: 'row',
    alignItems: 'flex-start',
  marginVertical: 4,
    padding: 8,
  backgroundColor: '#f3f4f6',
    borderRadius: 6 },
  historyContent: { marginLeft: 8,
    flex: 1 },
  historyText: {,
    fontSize: 14,
  fontWeight: '500'
  },
  historyDate: { fontSize: 12,
    color: '#6B7280',
  marginTop: 2 }
  historyReason: {,
    fontSize: 12),
  color: '#4B5563'),
    marginTop: 4,
  fontStyle: 'italic'
  },
  errorText: {,
    color: theme.colors.error,
  fontSize: 14)
  },
  })
  export default StatusTracker