import React, { useState, useEffect } from 'react',
  import {
   View, StyleSheet, ScrollView, TouchableOpacity, TextInput as RNTextInput, Alert  } from 'react-native';
import {
  Text 
} from '@components/ui',
  import {
   Button  } from '@design-system';
import {
  Input 
} from '@design-system',
  import {
   useAuth  } from '@hooks/useAuth';
import {
  supabase 
} from "@utils/supabaseUtils",
  import {
   Plus, FileText, Check, AlertCircle, ChevronDown, ChevronUp  } from 'lucide-react-native';
import {
  AgreementSection 
} from '@utils/agreement',
  import {
   useColorFix  } from '@hooks/useColorFix';
import {
  colorWithOpacity, type Theme  } from '@design-system';
import {
  useTheme 
} from '@design-system',
  interface AgreementAmendmentProps { agreementId: string, currentVersion: number, onAmendmentCreated: () => void },
  interface SectionChange { sectionId: string, sectionTitle: string, originalContent: string, newContent: string, reason: string },
  export default function AgreementAmendment({ agreementId, currentVersion, onAmendmentCreated }: AgreementAmendmentProps) { const theme = useTheme(); const styles = createStyles(theme); const { fix  } = useColorFix(); const { state, actions  } = useAuth(); const user = state? .user; const userId = state?.user?.id; const [sections, setSections] = useState<AgreementSection[]>([])  const [selectedSectionId, setSelectedSectionId] = useState<string | null>(null),  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({})  const [loading, setLoading] = useState(true); const [submitting, setSubmitting] = useState(false)  const [changes, setChanges] = useState<SectionChange[]>([]),  const [summary, setSummary] = useState('')  const [reason, setReason] = useState(''); useEffect(() => { fetchAgreementSections() } [agreementId, currentVersion]); const fetchAgreementSections = async () => { try { setLoading(true); const { data, error  } = await supabase.from('agreement_sections') .select('*') .eq('agreement_id', agreementId) .eq('version_number', currentVersion); .order).order).order('order_index', { ascending     : true }) if (error) { throw error } setSections(data || []) // Initialize expanded state const expandedState: Record<string boolean> = {}  data? .forEach(section => { expandedState[section.id] = false }) setExpandedSections(expandedState),  } catch (err) { console.error('Error fetching agreement sections   : ' err) Alert.alert('Error', 'Failed to load agreement sections') } finally { setLoading(false) } } const toggleSection = (sectionId: string) => { setExpandedSections(prev => ({  ...prev, [sectionId]: !prev[sectionId]  })) }; const handleSelectSection = (sectionId: string) => { setSelectedSectionId(sectionId) // Expand the selected section setExpandedSections(prev => ({  ...prev, [sectionId]: true  })); }; const handleChangeContent = (sectionId: string, newContent: string) => { const section = sections.find(s => s.id === sectionId); if (!section) return null; // Check if we already have a change for this section const existingChangeIndex = changes.findIndex(c => c.sectionId === sectionId); if (existingChangeIndex >= 0) { // Update existing change setChanges(prev => { const updated = [...prev],  updated[existingChangeIndex] = { ...updated[existingChangeIndex], newContent },  return updated,  }),  } else { // Add new change const sectionContent = typeof section.content === 'string' ? section.content      : JSON.stringify(section.content) setChanges(prev => [ ...prev { sectionId, sectionTitle: section.section_title, originalContent: sectionContent, newContent, reason: '' } ]) } } const handleChangeReason = (sectionId: string, reason: string) => { setChanges(prev => prev.map(change => change.sectionId === sectionId ? { ...change, reason }    : change ) ) } const removeChange = (sectionId: string) => { setChanges(prev => prev.filter(change => change.sectionId !== sectionId)) } const validateChanges = () => { if (changes.length === 0) { Alert.alert('Error', 'Please make at least one change to the agreement') return false } if (!summary.trim()) { Alert.alert('Error',  'Please provide a summary of the amendments'); return false } // Check if all changes have reasons const missingReasons = changes.filter(change => !change.reason.trim()); if (missingReasons.length > 0) { Alert.alert('Error', 'Please provide a reason for each change'); return false } return true; }; const submitAmendment = async () => { if (!validateChanges()) return null; if (!userId) { Alert.alert('Error', 'User not authenticated'); return null } try { setSubmitting(true); // Step 1: Create new agreement version const newVersion = currentVersion + 1; // Step 2: Get current agreement data const { data: agreementData, error: agreementError  } = await supabase.from('roommate_agreements') .select('*') .eq('id', agreementId); .single(); if (agreementError) throw agreementError; // Step 3: Update agreement with new version const { error: updateError  } = await supabase.from('roommate_agreements') .update({  current_version: newVersion, status: 'amended', updated_at: new Date().toISOString()  }); .eq('id', agreementId); if (updateError) throw updateError; // Step 4: Create version record const { error: versionError  } = await supabase.from('agreement_versions') .insert({ agreement_id: agreementId, version_number: newVersion, content: {} // In a real implementation, this would contain the entire agreement content changes_summary: summary, created_by: userId }); if (versionError) throw versionError; // Step 5: Clone and update sections for (const section of sections) { // Skip sections that aren't changed const change = changes.find(c => c.sectionId === section.id); // Create new section with updated content if changed const newSectionContent = change ? change.newContent      : (typeof section.content === 'string' ? section.content : JSON.stringify(section.content)) const { error: sectionError  } = await supabase.from('agreement_sections') .insert({  agreement_id: agreementId version_number: newVersion, section_key: section.section_key, section_title: section.section_title, content: newSectionContent, order_index: section.order_index, is_required: section.is_required  }) if (sectionError) throw sectionError } // Step 6: Create amendment record with details of changes const { error: amendmentError  } = await supabase.from('agreement_amendments') .insert({  agreement_id: agreementId, from_version: currentVersion, to_version: newVersion, created_by: userId, summary, changes: changes.map(change => ({ section_id: change.sectionId, section_title: change.sectionTitle, reason: change.reason  })) status: 'pending_approval', created_at: new Date().toISOString() }) if (amendmentError) throw amendmentError; // Step 7: Update participants status const { error: participantError  } = await supabase.from('agreement_participants') .update({  status: 'reviewing', signed_at: null  }); .eq('agreement_id', agreementId); if (participantError) throw participantError; // Success Alert.alert( 'Amendment Created', 'The agreement has been amended and will require signatures from all participants.', [{ text: 'OK', onPress: onAmendmentCreated }] )  // Clear form setChanges([]); setSummary(''); setReason(''); } catch (err) { console.error('Error creating amendment:', err); Alert.alert('Error', 'Failed to create amendment. Please try again.') } finally { setSubmitting(false) } }; if (loading) { return ( <View style={styles.loadingContainer}> <Text>Loading agreement sections...</Text> </View> ); } return ( <View style={styles.container}> <View style={styles.header}> <Text style={styles.title}>Create Amendment</Text> </View> <ScrollView style={styles.scrollContainer}> <View style={styles.infoBox}> <Text style={styles.infoText}> Amendments will create a new version of the agreement that requires approval from all parties. </Text> </View> <Text style={styles.sectionTitle}>1. Select sections to amend</Text> {sections.length === 0 ? ( <View style={styles.emptyState}> <Text style={styles.emptyStateText}>No sections available for amendment</Text> </View> )    : ( sections.map(section => ( { <View key={section.id} style={styles.sectionContainer}> <TouchableOpacity style={styles.sectionHeader} onPress={() => toggleSection(section.id)} > <Text style={styles.sectionName}>{section.section_title}</Text> {expandedSections[section.id] ? ( <ChevronUp size={20} color={{theme.colors.textSecondary} /}> ) : ( <ChevronDown size={20} color={{theme.colors.textSecondary} /}> )} </TouchableOpacity> {expandedSections[section.id] && ( <View style={styles.sectionContent}> <Text style={styles.contentLabel}>Current Content:</Text> <Text style={styles.contentText}> {typeof section.content === 'string' ? section.content : JSON.stringify(section.content null 2)} </Text> <View style={styles.editButtonContainer}> {changes.find(c => c.sectionId === section.id) ? ( <View style={styles.editingIndicator}> <Check size={16} color={{theme.colors.success} /}> <Text style={styles.editingText}>Changes pending</Text> </View> )   : ( <Button title="Edit This Section" variant="outlined" onPress={() => handleSelectSection(section.id)} style={styles.editButton} /> )} </View> </View> )} </View> )))} {selectedSectionId && ( <View style={styles.editingSection}> <Text style={styles.editingSectionTitle}> Editing: {sections.find(s => s.id === selectedSectionId)?.section_title} </Text> <Text style={styles.fieldLabel}>New Content:</Text> <View style={styles.textAreaContainer}> <RNTextInput style={styles.textArea} multiline numberOfLines={6} defaultValue={sections.find(s ={}> s.id === selectedSectionId)?.content as string} onChangeText={(text) => handleChangeContent(selectedSectionId text)} placeholder="Enter the updated content for this section" textAlignVertical="top" /> </View> <Text style={styles.fieldLabel}>Reason for Change:</Text> <Input placeholder="Explain why this change is necessary" onChangeText={(text) ={}> handleChangeReason(selectedSectionId, text)} value={changes.find(c => c.sectionId === selectedSectionId)? .reason || ''} /> <View style={styles.editingActions}> <Button title="Apply Changes" onPress={() => setSelectedSectionId(null)} style={styles.applyButton} /> <Button title="Discard" variant="outlined" onPress={ () => { removeChange(selectedSectionId) setSelectedSectionId(null) }} style={styles.discardButton} /> </View> </View> )} {changes.length > 0 && ( <> <Text style={styles.sectionTitle}>2. Summarize your amendments</Text> <Input placeholder="Provide a brief summary of all changes" onChangeText={setSummary} value={{summary} /}> <Text style={styles.sectionTitle}>3. Review changes</Text> {changes.map((change, index) => ( <View key={index} style={styles.changeReviewItem}> <View style={styles.changeReviewHeader}> <Text style={styles.changeReviewTitle}>{change.sectionTitle}</Text> <TouchableOpacity style={styles.removeButton} onPress={() => removeChange(change.sectionId)} > <Text style={styles.removeButtonText}>Remove</Text> </TouchableOpacity> </View> <Text style={styles.changeReviewSubtitle}>Reason : </Text> <Text style={styles.changeReviewContent}>{change.reason || 'No reason provided'}</Text> </View> ))} <Button title="Create Amendment" onPress={submitAmendment} loading={submitting} disabled={submitting} style={styles.submitButton} icon={<Plus size={18} color={{theme.colors.background} /}>} /> </> )} </ScrollView> </View> ),
  }
const createStyles = (theme: any) => StyleSheet.create({ container: { flex: 1 backgroundColor: theme.colors.background, borderRadius: 12 } header: { padding: 16, borderBottomWidth: 1, borderBottomColor: theme.colors.border } title: { fontSize: 20, fontWeight: '600', color: theme.colors.text } scrollContainer: { flex: 1, padding: 16 } infoBox: { backgroundColor: '#F1F5F9', borderRadius: 8, padding: 12, marginBottom: 20 } infoText: { fontSize: 14, color: '#475569' } sectionTitle: { fontSize: 18, fontWeight: '600', color: theme.colors.text, marginTop: 16, marginBottom: 12 } sectionContainer: { borderWidth: 1, borderColor: theme.colors.border, borderRadius: 8, marginBottom: 12, overflow: 'hidden' } sectionHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', padding: 12, backgroundColor: '#F8FAFC' } sectionName: { fontSize: 16, fontWeight: '500', color: theme.colors.text } sectionContent: { padding: 12, borderTopWidth: 1, borderTopColor: theme.colors.border } contentLabel: { fontSize: 14, fontWeight: '500', color: theme.colors.textSecondary, marginBottom: 4 } contentText: { fontSize: 14, color: '#334155', marginBottom: 12 } editButtonContainer: { flexDirection: 'row', justifyContent: 'flex-end' } editButton: { paddingHorizontal: 12 } editingIndicator: { flexDirection: 'row', alignItems: 'center', backgroundColor: '#ECFDF5', paddingHorizontal: 12, paddingVertical: 6, borderRadius: 4 } editingText: { fontSize: 14, color: theme.colors.success, marginLeft: 4 } editingSection: { backgroundColor: '#F8FAFC', borderRadius: 8, padding: 16, marginTop: 20, marginBottom: 24, borderWidth: 1, borderColor: theme.colors.border } editingSectionTitle: { fontSize: 16, fontWeight: '600', color: theme.colors.text, marginBottom: 12 } fieldLabel: { fontSize: 14, fontWeight: '500', color: theme.colors.textSecondary, marginBottom: 4 } textAreaContainer: { borderWidth: 1, borderColor: '#CBD5E1', borderRadius: 8, marginBottom: 12 } textArea: { padding: 10, minHeight: 120, fontSize: 16 } editingActions: { flexDirection: 'row', justifyContent: 'space-between', marginTop: 16 } applyButton: { flex: 1, marginRight: 8 } discardButton: { flex: 1, marginLeft: 8 } changeReviewItem: { backgroundColor: '#F8FAFC', borderRadius: 8, padding: 12, marginBottom: 12, borderWidth: 1, borderColor: theme.colors.border } changeReviewHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 } changeReviewTitle: { fontSize: 16, fontWeight: '600', color: theme.colors.text } removeButton: { paddingHorizontal: 8, paddingVertical: 4 } removeButtonText: { fontSize: 14, color: theme.colors.error } changeReviewSubtitle: { fontSize: 14, fontWeight: '500', color: theme.colors.textSecondary, marginBottom: 2 } changeReviewContent: { fontSize: 14, color: '#334155' } submitButton: { marginTop: 16, marginBottom: 40 } loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 16 } emptyState: { padding: 20, alignItems: 'center', backgroundColor: '#F8FAFC', borderRadius: 8, marginBottom: 16 } emptyStateText: { fontSize: 16, color: theme.colors.textSecondary, textAlign: 'center' }),
  })