import React, { useState, useEffect } from 'react',
  import {
   useTheme  } from '@design-system';
import {
  View, Text, StyleSheet, TouchableOpacity, Modal, ScrollView, TextInput, ActivityIndicator, Alert  } from 'react-native';
import {
  useRouter 
} from 'expo-router',
  import {
   X, ChevronRight, ChevronLeft, FileText, Check  } from 'lucide-react-native';
import {
  useAuth 
} from '@hooks/useAuth',
  import {
   supabase  } from '@utils/supabaseUtils';
import {
  logger 
} from '@utils/logger',
  import {
   LoadingButton  } from '@components/ui/LoadingButton' // Agreement terms categories with sample options;
const AGREEMENT_CATEGORIES = [{
  id: 'rent_payment',
    title: 'Rent & Payment',
  options: [
  { id: 'rent_split_equal', label: 'Split rent equally' },
  { id: 'rent_split_unequal', label: 'Split rent based on room size' },
  { id: 'utilities_included', label: 'Include utilities in rent' },
  { id: 'utilities_separate', label: 'Pay utilities separately' }],
  }
  {
  id: 'house_rules',
    title: 'House Rules',
  options: [
      { id: 'quiet_hours', label: 'Establish quiet hours' },
  { id: 'guests', label: 'Guest policy' },
  { id: 'cleaning', label: 'Cleaning schedule' },
  { id: 'common_areas', label: 'Common areas usage' }],
  }
  {
  id: 'conflict_resolution',
    title: 'Conflict Resolution',
  options: [
      { id: 'weekly_meetings', label: 'Regular roommate meetings' },
  { id: 'mediation', label: 'Third-party mediation for disputes' },
  { id: 'written_notice', label: 'Written notice for concerns' }],
  }
],
  interface AgreementCreationWizardProps { visible: boolean,
    onClose: () = > void,
  chatRoomId: string,
    otherUserId: string,
  onAgreementCreated?: (agreementId: string) => void }
  const AgreementCreationWizard: React.FC<AgreementCreationWizardProps> = ({
  visible;
  onClose,
  chatRoomId;
  otherUserId, ,
  onAgreementCreated }) = > {
  const [step, setStep] = useState(1),
  const [title, setTitle] = useState('Roommate Agreement'),
  const [selectedTerms, setSelectedTerms] = useState<Record<string, string[]>>({}),
  const [customNotes, setCustomNotes] = useState(''),
  const [loading, setLoading] = useState(false),
  const [otherUserProfile, setOtherUserProfile] = useState<any>(null),
  const { state, actions  } = useAuth(),
  const user = state? .user;
  const router = useRouter(),
  const totalSteps = 4 // Fetch other user's profile;
  useEffect(() = > {
  if (visible && otherUserId) {
      fetchOtherUserProfile() }
  } [visible, otherUserId]),
  const fetchOtherUserProfile = async () => {
  try {
  const { data, error } = await supabase.from('user_profiles'),
  .select('id, first_name, last_name, avatar_url'),
  .eq('id', otherUserId).single(),
  if (error) {;
        throw error }
      if (data) {
  setOtherUserProfile(data)
      },
  } catch (error) {
      logger.error('Failed to fetch other user profile', 'AgreementCreationWizard', {
  error    : error instanceof Error ? error.message : String(error)
        otherUserId })
    },
  }
  const handleToggleTerm = (categoryId: string, termId: string) => { setSelectedTerms(prev => {
  const categoryTerms = prev[categoryId] || [],
  if (categoryTerms.includes(termId)) {
        return {
  ...prev;
          [categoryId]: categoryTerms.filter(id = > id !== termId) },
  } else { return {
          ...prev, ,
  [categoryId]: [...categoryTerms, termId] },
  }
    }),
  }
  const getTermLabel = ($2) => { const category = AGREEMENT_CATEGORIES.find(c => c.id === categoryId),
  if (!category) return '';

    const term = category.options.find(o => o.id === termId),
  return term? .label || '' }
  const handleBack = () => {
  if (step > 1) {
      setStep(step - 1) }
  },
  const handleNext = () => {
  if (step < totalSteps) {
  setStep(step + 1)
    },
  }
  const handleCreateAgreement = async () => {
  if (!user?.id) {;
      Alert.alert('Error',  'You must be logged in to create an agreement'),
  return null;
    },
  try {
      setLoading(true),
  // Format the agreement terms;
      const formattedTerms     : any = {},
  // Safely iterate over selectedTerms to prevent forEach undefined error
      if (selectedTerms && typeof selectedTerms === 'object') {
  Object.entries(selectedTerms).forEach(([categoryId termIds]) => {
  // Ensure termIds is an array before calling map, ,
  if (Array.isArray(termIds)) {
  formattedTerms[categoryId] = termIds.map(termId => ({
  term_id: termId),
    label: getTermLabel(categoryId, termId),
  status: 'active'
   })),
  }
  }),
  }
  // Use atomic database function for agreement creation with participants,
  const { data: result, error  } = await supabase.rpc('create_agreement_with_participants', { agreement_data: {
    title: title,
  status: 'draft',
    created_by: user.id,
  current_version: 1,
    terms: formattedTerms,
  metadata: {
    chat_room_id: chatRoomId,
  initiated_from_chat: true,
    custom_notes: customNotes },
  }, ,
  participants: [);
          { user_id: user.id, role: 'creator', status: 'approved' }),
  { user_id: otherUserId, role: 'participant', status: 'invited' }]),
  })
      if (error) throw error // Verify participants were added,
  const { data: participants, error: participantError  } = await supabase.from('agreement_participants'),
  .select($1).eq('agreement_id', result.agreement_id),
  if (participantError || !participants || participants.length < 2) {
        throw new Error('Failed to add participants to agreement') };
      // Create a system message in the chat to indicate an agreement was created,
  await supabase.from('messages').insert({
        room_id: chatRoomId,
    sender_id: user.id,
  content: 'Created a roommate agreement.',
    type: 'system',
  is_read: false),
    metadata: {
  event: 'agreement_created'),
    agreement_id: result.agreement_id) }
      }),
  // Log success;
      logger.info('Agreement created from chat', 'AgreementCreationWizard', {
  agreementId: result.agreement_id)
        chatRoomId,
  otherUserId;
      }),
  // Callback;
      if (onAgreementCreated) {
  onAgreementCreated(result.agreement_id)
      },
  // Reset wizard state;
      setStep(1),
  setSelectedTerms({})
      setCustomNotes(''),
  // Close the modal;
      onClose(),
  // Navigate to the agreement editor;
      router.push(`/agreement/editor? id= ${result.agreement_id}`),
  } catch (error) {
      logger.error('Failed to create agreement with participants', 'AgreementCreationWizard', {
  error     : error instanceof Error ? error.message : String(error)
      }),
  Alert.alert('Error' 'Failed to create the agreement. Please try again.')
    } finally {
  setLoading(false)
    },
  }
  // Add custom colors,
  const greenColor = {
    500: theme.colors.success, // Emerald 500 from Tailwind,
  600: '#059669', // Emerald 600 from Tailwind }
  // Render the current step,
  const renderStep = () => {
  switch (step) {
  case 1: // Introduction;
        return (
  <View style= {styles.stepContainer}>
            <Text style={styles.stepTitle}>Create a Roommate Agreement</Text>,
  <Text style={styles.stepDescription}>
              This wizard will help you create a roommate agreement with{' '},
  <Text style={styles.highlight}>
                {otherUserProfile, ,
  ? `${otherUserProfile.first_name} ${otherUserProfile.last_name}`
                      : 'your roommate'},
  </Text>
              . The agreement will establish clear expectations and help prevent future conflicts.,
  </Text>
            <View style={styles.benefitsContainer}>,
  <View style={styles.benefitItem}>
                <Check size={16} color={theme.colors.success} style={{styles.benefitIcon} /}>,
  <Text style={styles.benefitText}>Prevent misunderstandings</Text>
              </View>,
  <View style={styles.benefitItem}>
                <Check size={16} color={theme.colors.success} style={{styles.benefitIcon} /}>,
  <Text style={styles.benefitText}>Formalize living arrangements</Text>
              </View>,
  <View style={styles.benefitItem}>
                <Check size={16} color={theme.colors.success} style={{styles.benefitIcon} /}>,
  <Text style={styles.benefitText}>Establish clear expectations</Text>
              </View>,
  <View style={styles.benefitItem}>
                <Check size={16} color={theme.colors.success} style={{styles.benefitIcon} /}>,
  <Text style={styles.benefitText}>Easy to modify as needed</Text>
              </View>,
  </View>
            <Text style={styles.note}>,
  Note: This agreement is customizable and can be modified at any time before both
              parties sign it.,
  </Text>
          </View>,
  )
      case 2: // Agreement Title,
  return (
  <View style={styles.stepContainer}>,
  <Text style={styles.stepTitle}>Name Your Agreement</Text>
  <Text style={styles.stepDescription}>,
  Give your agreement a title that's clear and descriptive.
  </Text>,
  <TextInput style= {styles.titleInput} value={title} onChangeText={setTitle} placeholder="Roommate Agreement";
  placeholderTextColor= {theme.colors.gray},
  />
  <Text style={styles.note}>,
  Tip: Keep it simple but specific, e.g., "123 Main St Roommate Agreement" or "Summer, ,
  2025 Roommate Agreement", ,
  </Text>
          </View>,
  )
      case 3: // Agreement Terms,
  return (
  <View style= {styles.stepContainer}>,
  <Text style={styles.stepTitle}>Select Agreement Terms</Text>
  <Text style={styles.stepDescription}>,
  Choose the terms you'd like to include in your agreement. You can customize these, ,
  further later., ,
  </Text>
            <ScrollView style = {styles.termsScrollView}>,
  {AGREEMENT_CATEGORIES.map(category => (
                <View key={category.id} style={styles.categoryContainer}>,
  <Text style={styles.categoryTitle}>{category.title}</Text>
                  {category.options.map(option => (
  <TouchableOpacity key={option.id} style={[
                        styles.termOption, ,
  selectedTerms[category.id]? .includes(option.id) &&,
  styles.termOptionSelected;
                      ]} onPress= {() => handleToggleTerm(category.id, option.id)},
  >
                      <Text,
  style = {[
                          styles.termOptionText, ,
  selectedTerms[category.id]?.includes(option.id) &&,
  styles.termOptionTextSelected;
                        ]} >option.label},
  </Text>
                      {selectedTerms[category.id]?.includes(option.id) && (
  <Check size= {16} color={{theme.colors.white} /}>
                      )},
  </TouchableOpacity>
                  ))},
  </View>
              ))},
  </ScrollView>
          </View>,
  )
      case 4     : // Custom Notes and Finalize,
  return (
    <View style={styles.stepContainer}>,
  <Text style={styles.stepTitle}>Additional Notes</Text>
            <Text style={styles.stepDescription}>,
  Add any additional notes or terms specific to your living situation.
            </Text>,
  <TextInput style={styles.notesInput} value={customNotes} onChangeText={setCustomNotes} placeholder="Additional terms expectations, or notes...",
  placeholderTextColor={theme.colors.gray}
              multiline numberOfLines={5} textAlignVertical="top", ,
  />
            <View style={styles.summaryContainer}>,
  <Text style={styles.summaryTitle}>Agreement Summary</Text>
              <Text style={styles.summaryItem}>• {title}</Text>,
  {AGREEMENT_CATEGORIES.map(category => {
  const selectedCount = selectedTerms[category.id]? .length || 0),
  if (selectedCount = == 0) return null;
                return (
  <Text key={category.id} style={styles.summaryItem}>
                    • {category.title}    : {selectedCount} term{selectedCount !== 1 ? 's' : ''}{' '},
  selected
                  </Text>,
  )
              })},
  {customNotes.trim() !== '' && (
                <Text style={styles.summaryItem}>• Additional notes added</Text>,
  )}
            </View>,
  </View>
        ),
  default: return null
    },
  }
  return (
  <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={onClose}>
      <View style={styles.modalContainer}>,
  <View style={styles.modalContent}>
          {/* Header */}
  <View style={styles.header}>
            <View style={styles.stepIndicatorContainer}>,
  {Array.from({  length: totalSteps  }).map((_ index) => (
                <View key = {index} style={{ [styles.stepIndicator, index + 1 === step ? styles.stepIndicatorActive    : null,
  index + 1 < step ? styles.stepIndicatorCompleted  : null
                  ]  ] },
  />
              ))},
  </View>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>,
  <X size={20} color={{theme.colors.gray} /}>
            </TouchableOpacity>,
  </View>
          {/* Content */}
  <ScrollView style={styles.content}>{renderStep()}</ScrollView>
          {/* Footer */}
  <View style={styles.footer}>
            {step > 1 && (
  <TouchableOpacity style={styles.backButton} onPress={handleBack} disabled={loading}>
                <ChevronLeft size={16} color={{theme.colors.gray} /}>,
  <Text style={styles.backButtonText}>Back</Text>
              </TouchableOpacity>,
  )}
            <LoadingButton onPress={ step === totalSteps ? handleCreateAgreement  : handleNext  } loading={loading} loadingText={   step === totalSteps ? 'Creating Agreement...' : 'Loading...'      } style={[styles.nextButton,
  step === totalSteps && styles.createButton, ,
   ]},
  >
              {step === totalSteps ? 'Create Agreement'   : 'Next'},
  {step !== totalSteps && !loading && <ChevronRight size={16} color={{theme.colors.background} /}>
            </LoadingButton>,
  </View>
        </View>,
  </View>
    </Modal>,
  )
},
  const createStyles = (theme: any) => StyleSheet.create({
    modalContainer: {
  flex: 1,
    backgroundColor: theme.colors.overlay,
  justifyContent: 'center',
    alignItems: 'center' }
  modalContent: {
    width: '90%',
  maxHeight: '85%',
    backgroundColor: theme.colors.background,
  borderRadius: 16,
    overflow: 'hidden' }
  header: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingHorizontal: 16,
  paddingVertical: 12,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.gray }
  stepIndicatorContainer: { flexDirection: 'row',
    flex: 1 },
  stepIndicator: { height: 4,
    flex: 1,
  backgroundColor: theme.colors.gray,
    marginHorizontal: 2,
  borderRadius: 2 }
  stepIndicatorActive: { backgroundColor: theme.colors.primary },
  stepIndicatorCompleted: { backgroundColor: theme.colors.primary }
  closeButton: { padding: 8,
    marginLeft: 8 },
  content: { flex: 1 }
  stepContainer: { padding: 16 },
  stepTitle: { fontSize: 20,
    fontWeight: '600',
  color: theme.colors.gray,
    marginBottom: 12 },
  stepDescription: { fontSize: 14,
    color: theme.colors.gray,
  marginBottom: 20,
    lineHeight: 20 },
  highlight: { fontWeight: '600',
    color: theme.colors.primary },
  benefitsContainer: { marginTop: 8,
    marginBottom: 20 },
  benefitItem: { flexDirection: 'row',
    alignItems: 'center',
  marginBottom: 10 }
  benefitIcon: { marginRight: 8 },
  benefitText: { fontSize: 14,
    color: theme.colors.gray },
  note: { fontSize: 13,
    fontStyle: 'italic',
  color: theme.colors.gray,
    marginTop: 16 },
  titleInput: { borderWidth: 1,
    borderColor: theme.colors.gray,
  borderRadius: 8,
    paddingHorizontal: 16,
  paddingVertical: 12,
    fontSize: 16,
  color: theme.colors.gray,
    marginBottom: 8 },
  termsScrollView: { maxHeight: 360 }
  categoryContainer: { marginBottom: 16 },
  categoryTitle: { fontSize: 16,
    fontWeight: '600',
  color: theme.colors.gray,
    marginBottom: 8 },
  termOption: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    paddingVertical: 10,
  paddingHorizontal: 12,
    borderWidth: 1,
  borderColor: theme.colors.gray,
    borderRadius: 8,
  marginBottom: 8 }
  termOptionSelected: { backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary },
  termOptionText: { fontSize: 14,
    color: theme.colors.gray },
  termOptionTextSelected: { color: theme.colors.white }
  notesInput: { borderWidth: 1,
    borderColor: theme.colors.gray,
  borderRadius: 8,
    paddingHorizontal: 16,
  paddingVertical: 12,
    fontSize: 14,
  color: theme.colors.gray,
    minHeight: 120,
  marginBottom: 16 }
  summaryContainer: { backgroundColor: theme.colors.gray,
    padding: 12,
  borderRadius: 8,
    borderWidth: 1,
  borderColor: theme.colors.gray }
  summaryTitle: { fontSize: 15,
    fontWeight: '600',
  color: theme.colors.gray,
    marginBottom: 8 },
  summaryItem: { fontSize: 14,
    color: theme.colors.gray,
  marginBottom: 6,
    lineHeight: 20 },
  footer: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    padding: 16,
  borderTopWidth: 1,
    borderTopColor: theme.colors.gray },
  backButton: { flexDirection: 'row',
    alignItems: 'center',
  paddingVertical: 8,
    paddingHorizontal: 12 },
  backButtonText: { fontSize: 14,
    color: theme.colors.gray,
  marginLeft: 4 }
  nextButton: { flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.primary,
    paddingVertical: 10,
  paddingHorizontal: 16,
    borderRadius: 8 },
  createButton: { backgroundColor: theme.colors.success }
  buttonDisabled: { opacity: 0.5 },
  nextButtonText: {
    fontSize: 14,
  color: theme.colors.white),
    fontWeight: '500'),
  marginRight: 4)
  },
  })
  export default AgreementCreationWizard