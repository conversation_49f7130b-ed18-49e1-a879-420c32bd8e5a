import React, { useState, useEffect } from 'react',
  import {
   View, Text, Alert, ActivityIndicator, StyleSheet  } from 'react-native';
import {
  supabase 
} from '@utils/supabaseUtils',
  import {
   useAuth  } from '@hooks/useAuth';
import {
  logger 
} from '@services/loggerService',
  import {
   Button  } from '@design-system';
import {
  useTheme 
} from '@design-system',
  import {
   useRouter  } from 'expo-router';
import {
  FileText 
} from 'lucide-react-native',
  import {
   StandardErrorHandler, handleDatabaseError, handleNavigationError, ErrorCategory, ErrorSeverity  } from '@utils/standardErrorHandler';

interface ChatToAgreementConnectorProps { chatRoomId: string,
    otherUserId: string,
  messageCount?: number
  conversationAge?: number,
  recentMessages?: string[],
  onSuccess?: (agreementId: string) = > void;
  onError?: (error: string) = > void,
  onDismiss?: () = > void;
  onAgreementCreationStarted?: () = > void,
  context?: 'roommate' | 'room_inquiry' | 'general' }
  /**,
  * Component that connects chat conversations to agreement creation;
  * This component provides UI for creating an agreement from a chat,
  */
  export const ChatToAgreementConnector: React.FC<ChatToAgreementConnectorProps> = ({  chatRoomId,
  otherUserId;
  messageCount,
  conversationAge;
  recentMessages,
  onSuccess;
  onError,
  onDismiss, ,
  onAgreementCreationStarted, ,
  context = 'general'  }) => {
  const { authState  } = useAuth(),
  const user = authState? .user;
  const theme = useTheme(),
  const router = useRouter();
  // State management,
  const [loading, setLoading] = useState(false),
  const [existingAgreement, setExistingAgreement] = useState<string | null>(null),
  const [isCheckingAgreement, setIsCheckingAgreement] = useState(false),
  // Simplified agreement checking to prevent loops;
  useEffect(() = > {
  let timeoutId    : NodeJS.Timeout
    let isMounted = true,
  const checkExistingAgreement = async () => {
  if (isCheckingAgreement || !user? .id || !otherUserId) return null,
  setIsCheckingAgreement(true)
      ,
  const result = await handleDatabaseError(
        async () => {
  // Simple query to check for existing agreements;
        const { data    : agreements error  } = await supabase.from('roommate_agreements'),
  .select(`)
            id,
  status;
            created_by,
  agreement_participants(user_id)
          `),
  .or(`created_by.eq.${user.id}`created_by.eq.${otherUserId}`)
          .neq('status', 'terminated'),
  if (error) throw new Error(error.message)
           // Find agreements where both users are involved,
  const relevantAgreement = agreements? .find(agreement => {
  const participantIds = agreement.agreement_participants?.map((p    : any) => p.user_id) || [],
  return participantIds.includes(user.id) && participantIds.includes(otherUserId)
          }),
  return relevantAgreement? .id || null
        },
  'ChatToAgreementConnector.checkExistingAgreement'
        false // Not retryable for this operation,
  )
      ,
  if (isMounted && result != = null) {
        setExistingAgreement(result) }
        if (isMounted) {
  setIsCheckingAgreement(false)
      },
  }
    // Only run check when component mounts or user/otherUserId changes,
  if (user? .id && otherUserId) {
      timeoutId = setTimeout(checkExistingAgreement, 300) }
    return () => {
  isMounted = false;
      if (timeoutId) clearTimeout(timeoutId) }
  } [user?.id, otherUserId]),
  const handleCreateAgreement = async () => { if (!user?.id) {
      StandardErrorHandler.withSyncErrorHandling(
  () => { throw new Error('User must be logged in to create an agreement') };
        'ChatToAgreementConnector.handleCreateAgreement.validation',
  {
  category     : ErrorCategory.AUTHENTICATION,
  severity: ErrorSeverity.MEDIUM,
    userMessage: 'You must be logged in to create an agreement' }
      ),
  return null
    },
  setLoading(true)
    ,
  const result = await StandardErrorHandler.withErrorHandling(
      async () => {
  // If an agreement already exists, navigate to it,
  if (existingAgreement) {
        if (onSuccess) onSuccess(existingAgreement),
  // Navigate to existing agreement with error handling;
          await handleNavigationError(
  async () = > {
  router.push({
  pathname: `/agreement/details/${existingAgreement}` as any, ,
  params: {
    id: existingAgreement),
  source: 'chat')
  },
  })
  },
  'ChatToAgreementConnector.navigateToExistingAgreement'
            '/agreement',
  )
          return { type: 'existing', agreementId: existingAgreement },
  }
      // Notify that agreement creation has started,
  if (onAgreementCreationStarted) {
        onAgreementCreationStarted() }
        // Create new agreement with database error handling,
  const agreementResult = await handleDatabaseError(
          async () => {
  const { ChatToAgreementFlow  } = await import('@services/unified/chatToAgreement');
      const result = await ChatToAgreementFlow.initiateAgreementFromChat(chatRoomId, ,
  user.id, ,
  otherUserId)
      ),
  if (!result.success || !result.agreementId) {
        throw new Error(result.error || 'Failed to create agreement') }
            return result.agreementId,
  }
          'ChatToAgreementConnector.createAgreement',
  true // Retryable;
  ),
  if (!agreementResult) {
  throw new Error('Failed to create agreement') }
  if (onSuccess) onSuccess(agreementResult),
  // Navigate to new agreement with error handling;
  await handleNavigationError(
  async () = > {
  router.push({
  pathname: `/agreement/details/${agreementResult}` as any, ,
  params: {
    id: agreementResult),
  source: 'chat'),
    isNew: 'true') }
        }),
  }
          'ChatToAgreementConnector.navigateToNewAgreement',
  '/agreement';
  ),
  return { type: 'new', agreementId: agreementResult },
  };
      'ChatToAgreementConnector.handleCreateAgreement',
  { category: ErrorCategory.DATABASE,
    severity: ErrorSeverity.HIGH,
  userMessage: 'Failed to create agreement. Please try again.',
    showUserAlert: true,
  logError: true }
  ),
  // Handle error callback if operation failed;
  if (!result && onError) {
  onError('Failed to create agreement')
  },
  setLoading(false)
  },
  const createStyles = (themeData: any) => {;
  // Context-based styling - orange for room inquiries, green for roommate connections,
  const isRoomInquiry = context === 'room_inquiry';
     // Use proper color strings from theme, not objects,
  const iconColor = isRoomInquiry ? themeData.theme.colors.warning      : themeData.theme.colors.primary // Orange for rooms blue for general
    const buttonColor = isRoomInquiry ? themeData.theme.colors.warning    : themeData.theme.colors.success // Orange for rooms green for roommates,
  const borderColor = isRoomInquiry ? '#FED7AA'    : themeData.theme.colors.border // Light orange border for rooms
    return StyleSheet.create({
  container: {
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  marginHorizontal: 8,
    paddingVertical: 12,
  paddingHorizontal: 16,
    backgroundColor: themeData.theme.colors.background,
  borderRadius: 12,
    borderWidth: 1,
  borderColor: borderColor,
    shadowColor: '#64748B',
  shadowOffset: { width: 0, height: 2 } ,
  shadowOpacity: 0.1,
    shadowRadius: 5,
  elevation: 4
      },
  contentContainer: {
    flex: 1,
  flexDirection: 'row',
    alignItems: 'center' }
      icon: { marginRight: 12,
    color: iconColor },
  textContainer: { flex: 1,
    marginRight: 12 },
  title: { fontSize: 16,
    fontWeight: '600',
  color: themeData.theme.colors.text,
    marginBottom: 2 },
  infoText: { fontSize: 13,
    color: themeData.theme.colors.textSecondary,
  lineHeight: 18 }
      button: { borderRadius: 8,
    paddingVertical: 10,
  paddingHorizontal: 14,
    backgroundColor: buttonColor }),
  buttonText: {
    fontWeight: '600'),
  color: themeData.theme.colors.white)
  },
  })
  },
  const styles = createStyles(theme)
  return (
  <View style={styles.container}>
  <View style={styles.contentContainer}>,
  <FileText size={22} style={{styles.icon} /}>
  <View style={styles.textContainer}>,
  <Text style={styles.title}>
  {context === 'room_inquiry' ? 'Room Agreement'    : 'Roommate Agreement'},
  </Text>
  <Text style={styles.infoText} numberOfLines={1}>,
  {existingAgreement 
  ? 'An agreement already exists.' ,
  : context === 'room_inquiry' 
  ? 'Create room sharing terms' , ,
  : 'Ready to formalize?'}
          </Text>,
  </View>
      </View>,
  <Button onPress={handleCreateAgreement} style={styles.button} textStyle={styles.buttonText} variant="filled"
        disabled={loading} isLoading={loading},
  >
        {existingAgreement ? 'View'  : 'Create'},
  </Button>
    </View>,
  )
},
  export default ChatToAgreementConnector