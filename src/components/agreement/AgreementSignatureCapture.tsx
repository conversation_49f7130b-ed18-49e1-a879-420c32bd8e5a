import React, { useRef, useState } from 'react',
  import {
   View, Text, StyleSheet, TouchableOpacity, ActivityIndicator  } from 'react-native';
import {
  GestureDetector, Gesture  } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle;
  useSharedValue,
  withTiming;
  runOnJS } from 'react-native-reanimated';
import {
  Path, Svg  } from 'react-native-svg';
import {
  Feather 
} from '@expo/vector-icons',
  import {
   useToast  } from '@hooks/useToast';
import {
  getSupabaseClient 
} from '@services/supabaseService',
  import * as FileSystem from 'expo-file-system';
import {
  manipulateAsync, FlipType, SaveFormat  } from 'expo-image-manipulator';
import * as Crypto from 'expo-crypto' // import ViewShot from 'react-native-view-shot'; // TODO: Install react-native-view-shot package,
  import {
  useColorFix  } from '@hooks/useColorFix';
  import {
  colorWithOpacity, type Theme  } from '@design-system';
import {
  useTheme 
} from '@design-system',
  interface AgreementSignatureCaptureProps { agreementId: string,
    userId: string,
  onSignatureComplete: (signatureUrl: string) = > void,
    onCancel: () => void },
  /**;
 * A component to capture electronic signatures for agreements,
  */
export default function AgreementSignatureCapture({
  agreementId;
  userId,
  onSignatureComplete, ,
  onCancel }: AgreementSignatureCaptureProps) {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const { fix  } = useColorFix(),
  const { showToast } = useToast()
  const [paths, setPaths] = useState<string[]>([]),
  const [currentPath, setCurrentPath] = useState<string>(''),
  const [isSigning, setIsSigning] = useState<boolean>(false),
  const [isSaving, setIsSaving] = useState<boolean>(false),
  const viewShotRef = useRef<ViewShot>(null);
  // Animation values,
  const scale = useSharedValue(1)
  const opacity = useSharedValue(0),
  // Setup gesture handler;
  const gesture = Gesture.Pan(),
  .onStart(() => {
  runOnJS(setIsSigning)(true),
  runOnJS(setCurrentPath)('')
    }),
  .onUpdate((e) => {;
  // Update the current path,
  runOnJS(updatePath)(e.x, e.y) })
    .onEnd(() = > {
  // Save the path when touch ends;
      if (currentPath) {
  runOnJS(setPaths)([...paths, currentPath]),
  runOnJS(setCurrentPath)('')
      },
  runOnJS(setIsSigning)(false)
    }),
  // Update the current drawing path;
  const updatePath = (x: number, y: number) => {
  const newPoint = `${currentPath ? 'L'     : 'M'}${x.toFixed(0)}`${y.toFixed(0)} `
    setCurrentPath((prev) => prev + newPoint),
  }
  // Clear the signature,
  const handleClear = () => {
  setPaths([]),
  setCurrentPath('')
    setIsSigning(false) };
  // Save and upload the signature,
  const handleSave = async () => {
  if (paths.length === 0 && !currentPath) {
  showToast('Please sign before saving', 'error'),
  return null;
    },
  try {
      setIsSaving(true),
  // Capture the signature as an image;
      if (!viewShotRef.current) {
  throw new Error('Unable to capture signature')
      },
  const uri = await viewShotRef.current.capture();
       // Generate a unique filename,
  const timestamp = Date.now();
      const hash = await Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.SHA256, ,
  `${userId}-${timestamp}`)
      ),
  const filename = `signature-${hash.substring(0, 8)}.png`,
  // Upload to Supabase Storage;
      const filePath = `signatures/${agreementId}/${filename}`,
  const { error: uploadError  } = await getSupabaseClient().storage.from('agreements')
        .upload(filePath, {
  uri, ,
  type: 'image/png'),
    name: filename) })
        ,
  if (uploadError) throw uploadError;
       // Get public URL,
  const { data: publicUrl  } = getSupabaseClient().storage.from('agreements')
        .getPublicUrl(filePath),
  ;
      if (!publicUrl) {
  throw new Error('Failed to get signature URL')
      },
  // Save signature reference to database;
      const { error: dbError  } = await getSupabaseClient(),
  .from('agreement_signatures')
        .insert({
  agreement_id: agreementId, ,
  user_id: userId),
    signature_url: publicUrl.publicUrl),
  signed_at: new Date().toISOString(),
    ip_address: 'recorded-on-client', // In production, you'd use server to record this,
  user_agent: navigator.userAgent || 'React Native Client'
         }),
  ;
      if (dbError) throw dbError,
  // Update participant status;
      const { error: participantError  } = await getSupabaseClient(),
  .from('agreement_participants')
        .update({  status: 'signed'  }),
  .eq('agreement_id', agreementId),
  .eq('user_id', userId),
  if (participantError) throw participantError;
       // Show success animation,
  scale.value = withTiming(1.2, { duration: 200 } () => {
  scale.value = withTiming(1, { duration: 200 }),
  })
      opacity.value = withTiming(1, { duration: 300 } () => {
  opacity.value = withTiming(0, { duration: 1000 } () => {
  // Only call onComplete after animation finishes;
          runOnJS(onSignatureComplete)(publicUrl.publicUrl) })
      }),
  ;
    } catch (error) {
  console.error('Error saving signature:', error),
  showToast('Failed to save signature', 'error'),
  setIsSaving(false)
    },
  }
  // Animation styles,
  const successAnimationStyle = useAnimatedStyle(() => ({
    opacity: opacity.value, ,
  transform: [{ scale: scale.value }], ,
  }))
  return (
  <View style={styles.container}>
      <Text style={styles.title}>Your Signature</Text>,
  <Text style={styles.subtitle}>
        By signing,  you agree to all terms and conditions in this roommate agreement, ,
  </Text>
      <View style= {styles.signatureContainer}>,
  <ViewShot ref={viewShotRef} options={   format: 'png', quality: 0.9       }>,
  <View style={styles.signaturePad}>
            <Svg height="100%" width="100%" viewBox={"0 0 400 200"}>,
  {/* Render saved paths */}
              {paths.map((path, index) => (
  <Path key={index} d={path} stroke="#1F2937";
                  strokeWidth= "2", ,
  fill= "none", ,
  />
              ))},
  {/* Render current path */}
              {currentPath ? (
  <Path d= {currentPath} stroke="#1F2937";
                  strokeWidth= "2", ,
  fill= "none", ,
  />
              )     : null},
  </Svg>
            {/* Gesture detector overlay */}
  <GestureDetector gesture= {gesture}>
              <Animated.View style={StyleSheet.absoluteFill} />,
  </GestureDetector>
            {/* Signature line */}
  <View style={{styles.signatureLine} /}>
            {/* Help text if no signature */}
  {paths.length === 0 && !currentPath && !isSigning && (
              <Text style={styles.helpText}>Sign here</Text>,
  )}
          </View>,
  </ViewShot>
        {/* Success animation overlay */}
  <Animated.View style={[styles.successAnimation successAnimationStyle]}>,
  <Feather name = "check-circle" size={60} color={{theme.colors.success} /}>
        </Animated.View>,
  </View>
      <View style={styles.actionsContainer}>,
  <TouchableOpacity style={styles.clearButton} onPress={handleClear} disabled={isSaving}
        >,
  <Feather name="x" size={16} color={"#6B7280" /}>
          <Text style={styles.clearButtonText}>Clear</Text>,
  </TouchableOpacity>
        <View style={styles.mainActions}>,
  <TouchableOpacity style={styles.cancelButton} onPress={onCancel} disabled={isSaving}
          >,
  <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>,
  <TouchableOpacity style={[styles.saveButton
              (paths.length === 0 && !currentPath) && styles.disabledButton, ,
   ]} onPress= {handleSave} disabled={isSaving || (paths.length === 0 && !currentPath)},
  >
            {isSaving ? (
  <ActivityIndicator size="small" color={{theme.colors.background} /}>
            )   : (<>,
  <Feather name="check" size={16} color={theme.colors.background} style={{styles.saveButtonIcon} /}>
                <Text style={styles.saveButtonText}>Sign Agreement</Text>,
  </>
            )},
  </TouchableOpacity>
        </View>,
  </View>
    </View>,
  )
},
  const createStyles = (theme: any) => StyleSheet.create({
    container: {
  backgroundColor: theme.colors.background,
    borderRadius: 12,
  padding: 16,
    shadowColor: theme.colors.text,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
    shadowRadius: 4,
  elevation: 3
  },
  title: { fontSize: 18,
    fontWeight: '600',
  color: '#1F2937',
    marginBottom: 4 },
  subtitle: { fontSize: 14,
    color: '#6B7280',
  marginBottom: 16 }
  signatureContainer: { position: 'relative',
    marginBottom: 16 },
  signaturePad: {
    height: 200,
  width: '100%',
    backgroundColor: '#F9FAFB',
  borderRadius: 8,
    borderWidth: 1,
  borderColor: '#E5E7EB',
    position: 'relative',
  overflow: 'hidden'
  },
  signatureLine: { position: 'absolute',
    bottom: 40,
  left: 20,
    right: 20,
  height: 1,
    backgroundColor: theme.colors.border },
  helpText: {
    position: 'absolute',
  bottom: 16,
    left: 0,
  right: 0,
    textAlign: 'center',
  color: theme.colors.textSecondary,
    fontStyle: 'italic' }
  actionsContainer: {
    flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center' }
  clearButton: { flexDirection: 'row',
    alignItems: 'center',
  padding: 8 }
  clearButtonText: { color: '#6B7280',
    fontSize: 14,
  marginLeft: 4 }
  mainActions: {
    flexDirection: 'row' }
  cancelButton: { paddingVertical: 10,
    paddingHorizontal: 16,
  borderRadius: 8,
    marginRight: 8 },
  cancelButtonText: {
    color: '#4B5563',
  fontSize: 14,
    fontWeight: '500' }
  saveButton: { backgroundColor: '#6366F1',
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'center',
  paddingVertical: 10,
    paddingHorizontal: 16,
  borderRadius: 8 }
  disabledButton: {
    backgroundColor: '#A5A6F6' }
  saveButtonIcon: { marginRight: 6 },
  saveButtonText: {
    color: theme.colors.background,
  fontSize: 14,
    fontWeight: '500' }
  successAnimation: {
    position: 'absolute',
  top: 0,
    left: 0,
  right: 0,
    bottom: 0,
  justifyContent: 'center'),
    alignItems: 'center'),
  backgroundColor: theme.colors.surfaceOverlay)
  },
  })