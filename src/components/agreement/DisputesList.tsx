import React from 'react',
  import {
   View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator  } from 'react-native';
import {
  useDisputeResolution 
} from '@hooks/useDisputeResolution',
  import {
   Dispute, DisputeStatus  } from '@utils/agreement';
import {
  AlertTriangle, Clock, MessageCircle, UserCircle, ChevronRight  } from 'lucide-react-native';
import {
  useColorFix 
} from '@hooks/useColorFix',
  import {
   colorWithOpacity, type Theme  } from '@design-system';
import {
  useTheme 
} from '@design-system',
  interface DisputesListProps { agreementId: string, onSelectDispute: (disputeId: string) => void, onCreateDispute: () => void },
  export default function DisputesList({ agreementId, onSelectDispute, onCreateDispute }: DisputesListProps) { const theme = useTheme(); const styles = createStyles(theme); const { fix  } = useColorFix(); const { disputes, loading, error, fetchDisputes  } = useDisputeResolution(agreementId); const getStatusColor = (status: DisputeStatus) => { switch (status) { case 'open': return theme.colors.warning // Amber case 'in_mediation': return theme.colors.primary; // Blue case 'resolved': return theme.colors.success; // Green case 'closed': return theme.colors.textSecondary; // Slate default: return theme.colors.textSecondary } }; const getStatusLabel = (status: DisputeStatus) => { switch (status) { case 'open': return 'Open'; case 'in_mediation': return 'In Mediation'; case 'resolved': return 'Resolved'; case 'closed': return 'Closed'; default: return status.charAt(0).toUpperCase() + status.slice(1) } }; const formatDate = (dateString: string) => { const date = new Date(dateString); return date.toLocaleDateString('en-US',  { year: 'numeric', month: 'short', day: 'numeric' }); }; const renderDisputeItem = ({ item }: { item: Dispute }) => ( <TouchableOpacity style={styles.disputeCard} onPress={() => onSelectDispute(item.id)} > <View style={styles.disputeHeader}> <Text style={styles.disputeTitle} numberOfLines={1}>{item.title}</Text> <View style={[ styles.statusBadge, { backgroundColor: `${getStatusColor(item.status)}20` } ]}> <Text style={[ styles.statusText, { color: getStatusColor(item.status)} ]}> {getStatusLabel(item.status)} </Text> </View> </View> <Text style={styles.disputeDescription} numberOfLines={2}> {item.description} </Text> <View style={styles.infoRow}> <View style={styles.infoItem}> <UserCircle size={16} color={{theme.colors.textSecondary} /}> <Text style={styles.infoText}> {item.raised_by_name || 'Unknown User'} </Text> </View> <View style={styles.infoItem}> <Clock size={16} color={{theme.colors.textSecondary} /}> <Text style={styles.infoText}> {formatDate(item.created_at)} </Text> </View> {item.message_count > 0 && ( <View style={styles.infoItem}> <MessageCircle size={16} color={{theme.colors.textSecondary} /}> <Text style={styles.infoText}> {item.message_count} message{item.message_count !== 1 ? 's'      : ''} </Text> </View> )} </View> <View style={styles.cardFooter}> <Text style={styles.viewDetailsText}>View Details</Text> <ChevronRight size={16} color={"#6366F1" /}> </View> </TouchableOpacity> ) const renderEmptyState = () => ( <View style={styles.emptyState}> <AlertTriangle size={64} color={"#CBD5E1" /}> <Text style={styles.emptyStateTitle}>No Disputes Reported</Text> <Text style={styles.emptyStateText}> There are currently no issues reported for this agreement. </Text> <TouchableOpacity style={styles.createButton} onPress={ onCreateDispute }> <Text style={styles.createButtonText}>Report an Issue</Text> </TouchableOpacity> </View> ) const renderError = () => ( <View style={styles.errorState}> <AlertTriangle size={64} color={{theme.colors.error} /}> <Text style={styles.errorTitle}>Something went wrong</Text> <Text style={styles.errorText}>{error}</Text> <TouchableOpacity style={styles.retryButton} onPress={() => fetchDisputes()} > <Text style={styles.retryButtonText}>Try Again</Text> </TouchableOpacity> </View> ) if (loading) { return ( <View style={styles.loadingContainer}> <ActivityIndicator size="large" color={"#6366F1" /}> <Text style={styles.loadingText}>Loading disputes...</Text> </View> ) } return ( <View style= {styles.container}> <View style={styles.header}> <Text style={styles.headerTitle}>Dispute Resolution</Text> <TouchableOpacity style={styles.headerButton} onPress={ onCreateDispute }> <Text style={styles.headerButtonText}>New Dispute</Text> </TouchableOpacity> </View> {error ? renderError()     : ( <FlatList { data={disputes} renderItem={renderDisputeItem} keyExtractor={(item) ={}> item.id} ListEmptyComponent={renderEmptyState} contentContainerStyle={[styles.listContent disputes.length === 0 && styles.emptyListContent ]} showsVerticalScrollIndicator={false} /> )} </View> ),
  }
const createStyles = (theme: any) => StyleSheet.create({ container: { flex: 1, backgroundColor: '#F8FAFC' } header: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: 20, paddingVertical: 16, backgroundColor: theme.colors.background, borderBottomWidth: 1, borderBottomColor: theme.colors.border } headerTitle: { fontSize: 18, fontWeight: '600', color: theme.colors.text } headerButton: { backgroundColor: '#6366F1', paddingHorizontal: 12, paddingVertical: 8, borderRadius: 6 } headerButtonText: { color: theme.colors.background, fontSize: 14, fontWeight: '500' } loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 } loadingText: { marginTop: 12, fontSize: 16, color: theme.colors.textSecondary } listContent: { padding: 16 } emptyListContent: { flexGrow: 1, justifyContent: 'center', alignItems: 'center' } disputeCard: { backgroundColor: theme.colors.background, borderRadius: 12, padding: 16, marginBottom: 16, shadowColor: theme.colors.text, shadowOffset: { width: 0, height: 2 } shadowOpacity: 0.05, shadowRadius: 4, elevation: 2, } disputeHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 } disputeTitle: { fontSize: 16, fontWeight: '600', color: theme.colors.text, flex: 1, marginRight: 8 } statusBadge: { paddingHorizontal: 8, paddingVertical: 4, borderRadius: 4 } statusText: { fontSize: 12, fontWeight: '500' } disputeDescription: { fontSize: 14, color: theme.colors.textSecondary, marginBottom: 12 } infoRow: { flexDirection: 'row', alignItems: 'center', flexWrap: 'wrap', marginBottom: 8 } infoItem: { flexDirection: 'row', alignItems: 'center', marginRight: 16, marginBottom: 4 } infoText: { fontSize: 12, color: theme.colors.textSecondary, marginLeft: 4 } cardFooter: { flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end', paddingTop: 8, borderTopWidth: 1, borderTopColor: '#F1F5F9' } viewDetailsText: { fontSize: 14, color: '#6366F1', fontWeight: '500', marginRight: 4 } emptyState: { alignItems: 'center', padding: 20 } emptyStateTitle: { fontSize: 18, fontWeight: '600', color: theme.colors.text, marginTop: 16, marginBottom: 8 } emptyStateText: { fontSize: 14, color: theme.colors.textSecondary, textAlign: 'center', marginBottom: 20 } createButton: { backgroundColor: '#6366F1', paddingHorizontal: 16, paddingVertical: 10, borderRadius: 8 } createButtonText: { color: theme.colors.background, fontSize: 14, fontWeight: '600' } errorState: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 } errorTitle: { fontSize: 18, fontWeight: '600', color: theme.colors.text, marginTop: 16, marginBottom: 8 } errorText: { fontSize: 14, color: theme.colors.textSecondary, textAlign: 'center', marginBottom: 20 } retryButton: { backgroundColor: '#6366F1', paddingHorizontal: 16, paddingVertical: 10, borderRadius: 8 } retryButtonText: { color: theme.colors.background, fontSize: 14, fontWeight: '600' }),
  }) 