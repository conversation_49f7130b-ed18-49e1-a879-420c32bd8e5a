import React, { useState, useEffect } from 'react',
  import {
   View, Text, StyleSheet, TextInput, ScrollView, TouchableOpacity, Switch, ActivityIndicator  } from 'react-native';
import {
  ChevronDown, ChevronUp, Edit, Save, Trash2, AlertCircle  } from 'lucide-react-native';
import {
  useColorFix 
} from '@hooks/useColorFix',
  import {
   colorWithOpacity, type Theme  } from '@design-system';
import {
  useTheme 
} from '@design-system',
  interface Field { name: string,
    type: string,
  required: boolean
  options?: string[],
  value?: any }
  interface SectionContent {
  description: string,
    fields: Field[] }
interface Section { key: string,
    title: string,
  order: number,
    is_required: boolean,
  content: SectionContent }
  interface SectionEditorProps { section: Section,
    onSave: (updatedSection: Section) = > void,
  onDelete?: () = > void;
  canDelete?: boolean },
  export default function SectionEditor({
  section,
  onSave;
  onDelete, ,
  canDelete = false,  }: SectionEditorProps) {
  const theme = useTheme(),
  const styles = createStyles(theme)
  const { fix  } = useColorFix(),
  const [expanded, setExpanded] = useState(false),
  const [editedSection, setEditedSection] = useState<Section>({  ...section  }),
  const [isEditing, setIsEditing] = useState(false),
  useEffect(() => {
  setEditedSection({  ...section  }),
  } [section]),
  const handleFieldChange = (fieldName: string, value: any) => {
  setEditedSection((prev) => {
  const updatedFields = prev.content.fields.map((field) => {
  if (field.name === fieldName) {;
          return { ...field; value },
  }
        return field,
  })
      return { ...prev,
  content: {
          ...prev.content,
  fields: updatedFields }
      },
  })
  },
  const handleSave = () => {
  onSave(editedSection),
  setIsEditing(false)
  },
  const handleCancel = () => {
  setEditedSection({  ...section  }),
  setIsEditing(false)
  },
  const renderFieldInput = (field: Field, index: number) = > {
  switch (field.type) {;
      case 'text':  ,
  return (
    <TextInput style= {{ [styles.input, isEditing ? styles.editableInput      : styles.readOnlyInput]  ] } value={field.value || ''} onChangeText={(text) ={}> handleFieldChange(field.name text)} placeholder={`Enter ${field.name.replace(/_/g ' ')}`},
  editable= {isEditing}
          />,
  )
      case 'textarea':  ,
  return (
    <TextInput style = {[styles.input, ,
  styles.textArea, ,
  isEditing ? styles.editableInput    : styles.readOnlyInput]} value= {field.value || ''} onChangeText={(text) => handleFieldChange(field.name text)} placeholder={`Enter ${field.name.replace(/_/g ' ')}`},
  multiline numberOfLines= {4} editable={isEditing}
          />,
  )
      case 'currency':  ,
  return (
    <View style = {styles.currencyContainer}>,
  <Text style={styles.currencySymbol}>$</Text>
            <TextInput style={{ [styles.input, styles.currencyInput, isEditing ? styles.editableInput    : styles.readOnlyInput]  ] } value= {field.value?.toString() || ''} onChangeText={(text) => handleFieldChange(field.name text)} placeholder="0.00",
  keyboardType="numeric"
              editable={isEditing},
  />
          </View>,
  )
      case 'select':  , ,
  if (!isEditing) {
          return (
  <Text style={styles.valueText}>
              {field.value || (field.options && field.options[0]) || 'Not set'},
  </Text>
          ),
  }
        return (
  <View style = {styles.selectContainer}>
            {field.options? .map((option) => (
  <TouchableOpacity key={option} style={[styles.selectOption
                  field.value === option && styles.selectedOption, ,
   ]} onPress={() => handleFieldChange(field.name, option)},
  >
                <Text,
  style = {[
                    styles.selectOptionText,
  field.value = == option && styles.selectedOptionText;
                  ]},
  >
                  {option},
  </Text>
              </TouchableOpacity>,
  ))}
          </View>,
  )
      case 'multiselect'   :  ,
  const selectedValues = Array.isArray(field.value) ? field.value   : [],
  if (!isEditing) {
          return (
  <Text style={styles.valueText}>
              {selectedValues.length > 0, ,
  ? selectedValues.join(', ')  : 'None selected'},
  </Text>
          ) {
  } {
 {
  return ( {
          <View style = {styles.selectContainer}>,
  {field.options? .map((option) => (
              <TouchableOpacity key={option} style={[styles.selectOption,
  selectedValues.includes(option) && styles.selectedOption, ,
   ]} onPress= { () => {
  const newValue = selectedValues.includes(option)
                    ? selectedValues.filter((v) = > v !== option),
  : [...selectedValues option],
  handleFieldChange(field.name, newValue) }},
  >
                <Text,
  style = {[
                    styles.selectOptionText,
  selectedValues.includes(option) && styles.selectedOptionText, ,
   ]},
  >
                  {option},
  </Text>
              </TouchableOpacity>,
  ))}
          </View>,
  )
      case 'day_of_month':  ,
  return (
  <TextInput style= {{ [styles.input, isEditing ? styles.editableInput    : styles.readOnlyInput]  ] } value={field.value? .toString() || ''} onChangeText={(text) ={}> {
  const day = parseInt(text)
              if ((text === '' || !isNaN(day)) && (day <= 31 || text === '')) {
  handleFieldChange(field.name text)
              },
  }}
            placeholder="Day of month (1-31)",
  keyboardType="numeric"
            maxLength= {2} editable={isEditing},
  />
        ),
  case 'period' :  
        return (
  <View style = {styles.periodContainer}>
            <TextInput style={{ [styles.input, styles.periodInput, isEditing ? styles.editableInput     : styles.readOnlyInput]  ] } value={field.value?.from || ''} onChangeText={   (text) => {
  handleFieldChange(field.name { ...field.value, from: text       }),
  }
              placeholder="Start date",
  editable = {isEditing}
            />,
  <Text style={styles.periodSeparator}>to</Text>
            <TextInput style={{ [styles.input, styles.periodInput, isEditing ? styles.editableInput    : styles.readOnlyInput]  ] } value={field.value?.to || ''} onChangeText={   (text) => {
  handleFieldChange(field.name { ...field.value, to: text       }),
  }
              placeholder= "End date",
  editable={isEditing}
            />,
  </View>
        ),
  case 'time_range':  , ,
  return (
    <View style = {styles.periodContainer}>,
  <TextInput style={{ [styles.input, styles.periodInput, isEditing ? styles.editableInput    : styles.readOnlyInput]  ] } value={field.value?.start || ''} onChangeText={   (text) => {
  handleFieldChange(field.name { ...field.value, start: text       }),
  }
              placeholder="Start time",
  editable = {isEditing}
            />,
  <Text style={styles.periodSeparator}>to</Text>
            <TextInput style={{ [styles.input, styles.periodInput, isEditing ? styles.editableInput    : styles.readOnlyInput]  ] } value={field.value?.end || ''} onChangeText={   (text) => {
  handleFieldChange(field.name { ...field.value, end: text       }),
  }
              placeholder= "End time",
  editable={isEditing}
            />,
  </View>
        ),
  default:  
        return (
  <TextInput style={{ [styles.input, isEditing ? styles.editableInput     : styles.readOnlyInput]  ] } value={field.value?.toString() || ''} onChangeText={(text) ={}> handleFieldChange(field.name text)} editable={isEditing},
  />
        ),
  }
  },
  return (
    <View style={styles.container}>,
  <TouchableOpacity style={styles.header} onPress={() => setExpanded(!expanded)} activeOpacity={0.7}
      >,
  <View style={styles.headerContent}>
                      <Text style={styles.sectionTitle}>{editedSection.section_title || editedSection.title}</Text>,
  {editedSection.is_required && (
            <View style={styles.requiredBadge}>,
  <Text style={styles.requiredText}>Required</Text>
            </View>,
  )}
        </View>,
  {expanded ? (
          <ChevronUp size={24} color={{theme.colors.textSecondary} /}>,
  )   : (<ChevronDown size={24} color={{theme.colors.textSecondary} /}>
        )},
  </TouchableOpacity>
      {expanded && (
  <View style={styles.sectionContent}>
          <Text style={styles.sectionDescription}>{editedSection.content.description}</Text>,
  {editedSection.content.fields.map((field index) => (
            <View key={field.name} style={styles.fieldContainer}>,
  <View style={styles.fieldHeader}>
                <Text style={styles.fieldLabel}>,
  {field.name.replace(/_/g ' ').replace(/\b\w/g (l) => l.toUpperCase())},
  {field.required && <Text style={styles.requiredAsterisk}> *</Text>
                </Text>,
  {field.required && !field.value && isEditing && (
                  <View style={styles.fieldWarning}>,
  <AlertCircle size={16} color={{theme.colors.error} /}>
                    <Text style={styles.warningText}>Required</Text>,
  </View>
                )},
  </View>
              {renderFieldInput(field, index)},
  </View>
          ))},
  <View style={styles.actionsContainer}>
            {isEditing ? (
  <>
                <TouchableOpacity style={styles.saveButton} onPress={handleSave}>,
  <Save size={20} color={{theme.colors.background} /}>
                  <Text style={styles.saveButtonText}>Save</Text>,
  </TouchableOpacity>
                <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>,
  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>,
  </>
            )   : (<TouchableOpacity style={styles.editButton} onPress={() => setIsEditing(true)},
  >
                <Edit size={20} color={{theme.colors.background} /}>,
  <Text style={styles.editButtonText}>Edit</Text>
              </TouchableOpacity>,
  )}
            {canDelete && onDelete && (
  <TouchableOpacity style={styles.deleteButton} onPress={onDelete}>
                <Trash2 size={20} color={theme.colors.background} />,
  <Text style={styles.deleteButtonText}>Delete</Text>
              </TouchableOpacity>,
  )}
          </View>,
  </View>
      )},
  </View>
  ),
  }
const createStyles = (theme: any) => StyleSheet.create({ container: {
    backgroundColor: theme.colors.background,
  borderRadius: 12,
    marginBottom: 16,
  overflow: 'hidden',
    borderWidth: 1,
  borderColor: theme.colors.border }
  header: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'space-between',
    padding: 16 },
  headerContent: {
    flexDirection: 'row',
  alignItems: 'center'
  },
  sectionTitle: { fontSize: 18,
    fontWeight: '600',
  color: theme.colors.text }
  requiredBadge: { backgroundColor: '#EEF2FF',
    paddingHorizontal: 8,
  paddingVertical: 4,
    borderRadius: 4,
  marginLeft: 8 }
  requiredText: {
    fontSize: 12,
  color: '#6366F1',
    fontWeight: '500' }
  sectionContent: { padding: 16,
    paddingTop: 0,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border },
  sectionDescription: { fontSize: 14,
    color: theme.colors.textSecondary,
  marginBottom: 16 }
  fieldContainer: { marginBottom: 16 },
  fieldHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
  alignItems: 'center',
    marginBottom: 8 },
  fieldLabel: {
    fontSize: 14,
  fontWeight: '500',
    color: '#334155' }
  requiredAsterisk: { color: theme.colors.error },
  fieldWarning: {
    flexDirection: 'row',
  alignItems: 'center'
  },
  warningText: { fontSize: 12,
    color: theme.colors.error,
  marginLeft: 4 }
  input: { borderWidth: 1,
    borderRadius: 8,
  paddingHorizontal: 12,
    paddingVertical: 10,
  fontSize: 16,
    color: theme.colors.text },
  editableInput: { borderColor: '#CBD5E1',
    backgroundColor: theme.colors.background },
  readOnlyInput: {
    borderColor: theme.colors.border,
  backgroundColor: '#F8FAFC'
  },
  textArea: {
    height: 100,
  textAlignVertical: 'top'
  },
  currencyContainer: {
    flexDirection: 'row',
  alignItems: 'center'
  },
  currencySymbol: { fontSize: 16,
    color: theme.colors.textSecondary,
  marginRight: 8 }
  currencyInput: { flex: 1 },
  selectContainer: { flexDirection: 'row',
    flexWrap: 'wrap',
  marginHorizontal: -4 }
  selectOption: { borderWidth: 1,
    borderColor: '#CBD5E1',
  borderRadius: 6,
    paddingHorizontal: 12,
  paddingVertical: 8,
    margin: 4 },
  selectedOption: {
    borderColor: '#6366F1',
  backgroundColor: '#EEF2FF'
  },
  selectOptionText: { fontSize: 14,
    color: theme.colors.textSecondary },
  selectedOptionText: {
    color: '#6366F1',
  fontWeight: '500'
  },
  periodContainer: {
    flexDirection: 'row',
  alignItems: 'center'
  },
  periodInput: { flex: 1 }
  periodSeparator: { marginHorizontal: 8,
    color: theme.colors.textSecondary },
  valueText: { fontSize: 16,
    color: '#334155',
  paddingVertical: 10 }
  actionsContainer: { flexDirection: 'row',
    justifyContent: 'flex-end',
  marginTop: 16 }
  editButton: {
    backgroundColor: '#6366F1',
  borderRadius: 8,
    paddingHorizontal: 16,
  paddingVertical: 10,
    flexDirection: 'row',
  alignItems: 'center'
  },
  editButtonText: { color: theme.colors.background,
    fontWeight: '500',
  marginLeft: 8 }
  saveButton: {
    backgroundColor: theme.colors.success,
  borderRadius: 8,
    paddingHorizontal: 16,
  paddingVertical: 10,
    flexDirection: 'row',
  alignItems: 'center'
  },
  saveButtonText: { color: theme.colors.background,
    fontWeight: '500',
  marginLeft: 8 }
  cancelButton: { borderWidth: 1,
    borderColor: '#CBD5E1',
  borderRadius: 8,
    paddingHorizontal: 16,
  paddingVertical: 10,
    marginLeft: 8 },
  cancelButtonText: { color: theme.colors.textSecondary }
  deleteButton: { backgroundColor: theme.colors.error,
    borderRadius: 8,
  paddingHorizontal: 16,
    paddingVertical: 10,
  flexDirection: 'row',
    alignItems: 'center',
  marginLeft: 8 }
  deleteButtonText: {
    color: theme.colors.background),
  fontWeight: '500'),
    marginLeft: 8) }
})