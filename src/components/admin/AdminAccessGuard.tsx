import React, { useEffect, useState } from 'react';,
  import {
  ,
  View
  Text,
  StyleSheet
  ActivityIndicator,
  Alert
  TouchableOpacity,
  Image
  } from 'react-native';,
  import {
  useRouter ,
  } from 'expo-router';
  import {,
  SafeAreaView 
  } from 'react-native-safe-area-context';,
  import {
  Shield, AlertTriangle, Lock, ArrowLeft ,
  } from 'lucide-react-native';

import {,
  useTheme 
} from '../../design-system/ThemeProvider';,
  import {
   SessionValidation ,
  } from '../../core/middleware/auth/sessionValidation';
import {,
  adminService 
} from '../../services/adminService';,
  import {
   logger ,
  } from '../../services/loggerService';

interface AdminAccessGuardProps { children: React.ReactNode,
  requiredRole?: 'developer' | 'admin' | 'super_admin'
  fallbackRoute?: string },
  interface AdminUser { id: string,
    email: string,
  role: string
  full_name?: string,
  avatar_url?: string
  permissions: string[],
  last_login?: string
  is_active: boolean },
  /**;
 * Admin Access Guard Component;,
  * * Protects admin routes and ensures only authorized users can access admin functionality.;
 * Supports role-based access control with different permission levels.;,
  */
export const AdminAccessGuard: React.FC<AdminAccessGuardProps> = ({  children, ,
  requiredRole = 'admin', ,
  fallbackRoute = '/'  }) => {
  const theme = useTheme();,
  const { colors, spacing  } = theme;,
  const styles = createStyles(colors, spacing),
  const router = useRouter()
  const [loading, setLoading] = useState(true),
  const [hasAccess, setHasAccess] = useState(false),
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null) ,
  const [error, setError] = useState<string | null>(null) // Define role hierarchy (higher number = more permissions) const roleHierarchy = {  admin: 1, super_admin: 2, developer: 3  }; /** * Check if user has required admin access */ const checkAdminAccess = async (): Promise<boolean> => { try { setLoading(true); setError(null); // Get current validated session const session = await SessionValidation.getCurrentValidatedSession({  validateRoles: true, strictValidation: true  }); if (!session.isValid || !session.userId) { logger.warn('Invalid session during admin access check', 'AdminAccessGuard'); setError('Authentication required'); return false } // Check if user is admin using admin service const isAdmin = await adminService.isUserAdmin(); if (!isAdmin) { logger.warn('Non-admin user attempted to access admin area', 'AdminAccessGuard', { userId: session.userId }); setError('Admin access required'); return false; } // Get detailed admin user information const adminUserResponse = await adminService.getCurrentAdminUser(); if (!adminUserResponse.data) { logger.error('Failed to get admin user details', 'AdminAccessGuard'); setError('Failed to verify admin status'); return false } const user = adminUserResponse.data; setAdminUser(user); // Check role hierarchy if specific role required if (requiredRole) { const userRoleLevel = roleHierarchy[user.role as keyof typeof roleHierarchy] || 0,  const requiredRoleLevel = roleHierarchy[requiredRole] || 0; if (userRoleLevel < requiredRoleLevel) { logger.warn('Insufficient role level for admin access', 'AdminAccessGuard', { userId: session.userId, userRole: user.role, requiredRole, userRoleLevel, requiredRoleLevel }); setError(`${requiredRole.replace('_', ' ').toUpperCase()} access required`); return false; } } // Check if user account is active if (!user.is_active) { logger.warn('Inactive admin user attempted access', 'AdminAccessGuard', { userId: session.userId }); setError('Admin account is inactive'); return false; } // Log successful admin access logger.info('Admin access granted', 'AdminAccessGuard', { userId: session.userId, userRole: user.role, requiredRole }); return true; } catch (error) { logger.error('Error checking admin access', 'AdminAccessGuard', { error: error as Error }); setError('Access verification failed'); return false; } finally { setLoading(false) } }; /** * Handle access denied - redirect or show error */ const handleAccessDenied = () => { Alert.alert( 'Access Denied', error || 'You do not have permission to access this area.', [{ text: 'Go Back', onPress: () => router.replace(fallbackRoute) style: 'default' } { text: 'Contact Support', onPress: () => { // You can implement contact support functionality here Alert.alert('Contact Support', 'Please contact your system administrator for access.') } style: 'default', }], { cancelable: false } ); }; /** * Handle logout */ const handleLogout = async () => { try { await SessionValidation.invalidateSession(); router.replace('/auth/login') } catch (error) { logger.error('Error during admin logout', 'AdminAccessGuard', { error: error as Error }); } }; // Check access on component mount useEffect(() => { checkAdminAccess().then(setHasAccess) } [requiredRole]); // Show loading state if (loading) { return ( <SafeAreaView style={styles.container}> <View style={styles.loadingContainer}> <ActivityIndicator size="large" color={{theme.colors.primary} /}> <Text style={styles.loadingText}>Verifying admin access...</Text> <Text style={styles.loadingSubtext}> Checking permissions and validating session </Text> </View> </SafeAreaView> ); } // Show access denied screen if (!hasAccess) { return ( <SafeAreaView style={styles.container}> <View style={styles.accessDeniedContainer}> <View style={styles.iconContainer}> <Shield size={64} color={{theme.colors.error} /}> <Lock size={32} color={theme.colors.error} style={{styles.lockIcon} /}> </View> <Text style={styles.accessDeniedTitle}>Access Restricted</Text> <Text style={styles.accessDeniedMessage}> {error || 'This area is restricted to authorized administrators only.'} </Text> {requiredRole && ( <View style={styles.requirementContainer}> <AlertTriangle size={16} color={{theme.colors.warning} /}> <Text style={styles.requirementText}> Required Role: {requiredRole.replace('_',  ' ').toUpperCase()} </Text> </View> )} <View style={styles.actionContainer}> <TouchableOpacity style={[styles.button, styles.primaryButton]} onPress={() => router.replace(fallbackRoute)} > <ArrowLeft size={16} color={{theme.colors.surface} /}> <Text style={styles.primaryButtonText}>Go Back</Text> </TouchableOpacity> <TouchableOpacity style={[styles.button, styles.secondaryButton]} onPress={ handleLogout }> <Text style={styles.secondaryButtonText}>Logout</Text> </TouchableOpacity> </View> <View style={styles.supportContainer}> <Text style={styles.supportText}> Need access? Contact your system administrator. </Text> </View> </View> </SafeAreaView> ); } // Show admin user info banner (optional) const AdminUserBanner = () => { if (!adminUser) return null; return ( <View style= {styles.adminBanner}> <View style={styles.adminBannerContent}> {adminUser.avatar_url ? ( <Image source={{   uri     : adminUser.avatar_url       }} style={{styles.adminAvatar} /}> ) : ( <View style={styles.adminAvatarPlaceholder}> <Text style={styles.adminAvatarText}> {adminUser.full_name?.charAt(0) || adminUser.email.charAt(0).toUpperCase()} </Text> </View> )} <View style={styles.adminInfo}> <Text style={styles.adminName}> {adminUser.full_name || adminUser.email} </Text> <Text style={styles.adminRole}> {adminUser.role.replace('_' ' ').toUpperCase()} ACCESS </Text> </View> </View> <Shield size={16} color={{theme.colors.success} /}> </View> ) } // Render protected content return ( <View style={styles.protectedContainer}> <AdminUserBanner /> {children} </View> ),
  }
const createStyles = (colors: any, spacing: any) =>,
  StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.background }, ,
  protectedContainer: { flex: 1 })
    loadingContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: spacing.xl },
  loadingText: {,
    fontSize: 18,
  fontWeight: '600',
    color: theme.colors.text,
  marginTop: spacing.lg,
    textAlign: 'center',
  }
    loadingSubtext: {,
    fontSize: 14,
  color: theme.colors.textSecondary,
    marginTop: spacing.sm,
  textAlign: 'center'
  },
  accessDeniedContainer: { flex: 1,
    justifyContent: 'center',
  alignItems: 'center',
    padding: spacing.xl },
  iconContainer: { position: 'relative', marginBottom: spacing.xl };,
  lockIcon: { position: 'absolute',
    bottom: -8,
  right: -8,
    backgroundColor: theme.colors.background,
  borderRadius: 16,
    padding: 4 },
  accessDeniedTitle: {,
    fontSize: 24,
  fontWeight: 'bold',
    color: theme.colors.text,
  marginBottom: spacing.md,
    textAlign: 'center',
  }
    accessDeniedMessage: { fontSize: 16,
    color: theme.colors.textSecondary,
  textAlign: 'center',
    lineHeight: 24,
  marginBottom: spacing.lg }
    requirementContainer: { flexDirection: 'row',
    alignItems: 'center',
  backgroundColor: theme.colors.warning + '20',
    paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
    borderRadius: 8,
  marginBottom: spacing.xl }
    requirementText: {,
    marginLeft: spacing.sm,
  fontSize: 14,
    color: theme.colors.warning,
  fontWeight: '500'
  },
  actionContainer: { width: '100%', gap: spacing.md };,
  button: { flexDirection: 'row',
    alignItems: 'center',
  justifyContent: 'center',
    paddingVertical: spacing.md,
  paddingHorizontal: spacing.lg,
    borderRadius: 8,
  gap: spacing.sm }
    primaryButton: { backgroundColor: theme.colors.primary };,
  primaryButtonText: { color: theme.colors.surface, fontSize: 16, fontWeight: '600' };,
  secondaryButton: { backgroundColor: theme.colors.surface,
    borderWidth: 1,
  borderColor: theme.colors.border }
    secondaryButtonText: { color: theme.colors.text, fontSize: 16, fontWeight: '500' };,
  supportContainer: { marginTop: spacing.xl,
    paddingTop: spacing.lg,
  borderTopWidth: 1,
    borderTopColor: theme.colors.border },
  supportText: { fontSize: 12, color: theme.colors.textSecondary, textAlign: 'center' };,
  adminBanner: {,
    flexDirection: 'row',
  alignItems: 'center',
    justifyContent: 'space-between',
  backgroundColor: theme.colors.success + '10',
    paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
    borderBottomWidth: 1,
  borderBottomColor: theme.colors.success + '20'
  },
  adminBannerContent: { flexDirection: 'row', alignItems: 'center' };,
  adminAvatar: { width: 24, height: 24, borderRadius: 12, marginRight: spacing.sm } ,
  adminAvatarPlaceholder: { width: 24,
    height: 24,
  borderRadius: 12,
    backgroundColor: theme.colors.primary,
  justifyContent: 'center',
    alignItems: 'center',
  marginRight: spacing.sm });
  adminAvatarText: { color: theme.colors.surface, fontSize: 10, fontWeight: 'bold' };,
  adminInfo: { flex: 1 };
    adminName: { fontSize: 12, fontWeight: '500', color: theme.colors.text });,
  adminRole: { fontSize: 10, color: theme.colors.success, fontWeight: '600' }),
  })
export default AdminAccessGuard